import{a0 as e,_ as a,a as t,r as l,o as r,c as n,e as d,R as o,f as u,k as s,j as i,S as m,T as p,K as c,E as g,m as v,I as h,J as f,h as b,U as y,n as w,V as x,Z as N,W as _,ac as k,t as I,Y as V,g as D,O as $}from"./index-BDR0Pmj6.js";import{b as M}from"./department-jpUZgat6.js";import{B as C}from"./BatchExtendDialog-CsIAK1fn.js";const S="/department-expense";const Y={class:"expense-container"},E={class:"toolbar"},U={class:"search-box"},T={class:"action-box"},z={class:"operation-buttons"},B={class:"pagination-container"},j={class:"dialog-footer"},q=a({__name:"DepartmentExpense",setup(a){const q=t([]),F=t(!0),L=t(null),A=t(""),W=t(""),R=t([]),K=t(!1),H=t([]),J=l({pageNum:1,pageSize:10,total:0}),O=t(!1),Z=t("add"),G=t(null),P=t(!1),Q=l({id:null,departmentId:null,expenseDate:"",departmentIds:[],selectedMonths:[],itemName:"",amount:null,remark:""}),X=t([]),ee=()=>{const e=new Map,a=new Date;for(let t=0;t<12;t++){const l=new Date(a);l.setDate(1),l.setMonth(a.getMonth()-t);const r=`${l.getFullYear()}-${(l.getMonth()+1).toString().padStart(2,"0")}`;e.has(r)||e.set(r,{value:r,label:r})}for(let t=1;t<=12;t++){const l=new Date(a);l.setDate(1),l.setMonth(a.getMonth()+t);const r=`${l.getFullYear()}-${(l.getMonth()+1).toString().padStart(2,"0")}`;e.has(r)||e.set(r,{value:r,label:r})}X.value=Array.from(e.values()).sort(((e,a)=>a.value.localeCompare(e.value)))},ae=t(!1),te=t([]),le=t(null),re=t([{label:"部门",prop:"departmentName",minWidth:"120px"},{label:"原开销年月",prop:"originalExpenseDate",width:"110px",align:"center"},{label:"项目名称",prop:"itemName",minWidth:"150px"},{label:"金额",prop:"amount",width:"100px",align:"right",isCurrency:!0},{label:"备注",prop:"remark",minWidth:"130px"}]),ne=l({departmentId:[{required:!0,message:"请选择部门",trigger:"change"}],expenseDate:[{required:!0,message:"请选择开销月份",trigger:"change"}],departmentIds:[{type:"array",required:!0,message:"请至少选择一个部门",trigger:"change"},{validator:(e,a,t)=>{a&&0!==a.length?t():t(new Error("请至少选择一个部门"))},trigger:"change"}],selectedMonths:[{type:"array",required:!0,message:"请至少选择一个开销月份",trigger:"change"},{validator:(e,a,t)=>{a&&0!==a.length?t():t(new Error("请至少选择一个开销月份"))},trigger:"change"}],itemName:[{required:!0,message:"请输入项目名称",trigger:"blur"},{max:255,message:"项目名称不能超过255个字符",trigger:"blur"}],amount:[{required:!0,message:"请输入金额",trigger:"blur"},{validator:(e,a,t)=>{null==a?t(new Error("金额不能为空")):a<=0?t(new Error("金额必须大于0")):t()},trigger:"blur"}],remark:[{max:255,message:"备注不能超过255个字符",trigger:"blur"}]}),de=async()=>{F.value=!0;try{let a=null,t=null;if(W.value){const e=W.value.substring(0,4),l=W.value.substring(5,7);a=`${e}-${l}-01`;t=`${e}-${l}-${new Date(e,l,0).getDate()}`}const l={pageNum:J.pageNum,pageSize:J.pageSize,departmentId:L.value||void 0,description:A.value||void 0,startDate:a||void 0,endDate:t||void 0},r=await function(a){return e({url:`${S}/page`,method:"get",params:a})}(l);200===r.code&&r.data?(q.value=r.data.records||r.data.list||[],J.total=r.data.total||0):g.error(r.message||"获取部门开销数据失败")}catch(a){g.error("加载部门开销数据失败: "+(a.message||"网络错误"))}finally{F.value=!1}},oe=()=>{G.value&&G.value.resetFields(),Q.id=null,Q.departmentId=null,Q.expenseDate="",Q.departmentIds=[],Q.selectedMonths=[],Q.itemName="",Q.amount=null,Q.remark=""},ue=()=>{O.value=!1,oe()},se=()=>{Z.value="add",oe(),0===R.value.length&&we(),O.value=!0},ie=async a=>{Z.value="edit",oe(),0===R.value.length&&await we(),P.value=!0;try{const l=await(t=a.id,e({url:`${S}/${t}`,method:"get"}));if(200!==l.code||!l.data)return g.error(l.message||"获取开销详情失败"),void ue();Q.id=l.data.id,Q.departmentId=l.data.departmentId,Q.expenseDate=l.data.expenseDate?l.data.expenseDate.substring(0,7):"",Q.itemName=l.data.itemName,Q.amount=Number(l.data.amount)||null,Q.remark=l.data.remark||"",Q.departmentIds=[],Q.selectedMonths=[]}catch(l){return g.error("获取开销详情失败: "+(l.message||"网络错误")),void ue()}finally{P.value=!1}var t;O.value=!0},me=a=>{$.confirm(`确定要删除部门 "${a.departmentName}" 在 "${a.expenseDate}" 关于 "${a.itemName||a.description}" 的开销记录吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{F.value=!0;try{const l=await(t=a.id,e({url:`${S}/${t}`,method:"delete"}));200===l.code?(g.success("删除成功"),de()):g.error(l.message||"删除失败")}catch(l){g.error("删除操作失败: "+(l.message||"网络错误"))}finally{F.value=!1}var t})).catch((()=>{}))},pe=()=>{if(0===H.value.length)return void g.warning("请选择要删除的记录");const a=H.value.map((e=>e.id));$.confirm(`确定要批量删除选中的 ${a.length} 条开销记录吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{F.value=!0;try{const t=await function(a){return e({url:`${S}/batch-delete`,method:"post",data:a})}(a);200===t.code?(g.success("批量删除成功"),H.value=[],de()):g.error(t.message||"批量删除失败")}catch(t){g.error("批量删除操作失败: "+(t.message||"网络错误"))}finally{F.value=!1}})).catch((()=>{}))},ce=async a=>{a&&await a.validate((async a=>{if(!a)return g.warning("请完善表单信息"),!1;P.value=!0;try{let a;const l={};"add"===Z.value?(l.departmentIds=Q.departmentIds,l.expenseMonths=Q.selectedMonths,l.itemName=Q.itemName,l.amount=Number(Q.amount),l.remark=Q.remark,a=await(t=l,e({url:`${S}/batch`,method:"post",data:t}))):(l.id=Q.id,l.departmentId=Q.departmentId,l.expenseDate=Q.expenseDate?`${Q.expenseDate}-01`:null,l.itemName=Q.itemName,l.amount=Number(Q.amount),l.remark=Q.remark,a=await function(a){return e({url:S,method:"put",data:a})}(l)),200===a.code?(g.success("edit"===Z.value?"更新成功":"批量添加成功"),O.value=!1,de()):g.error(a.message||("edit"===Z.value?"更新失败":"批量添加失败"))}catch(l){let e="提交操作失败";l.response&&l.response.data&&l.response.data.message?e=l.response.data.message:l.message&&(e=l.message),g.error(e)}finally{P.value=!1}var t}))},ge=e=>{H.value=e},ve=()=>{J.pageNum=1,de()},he=()=>{L.value=null,A.value="",W.value="",J.pageNum=1,de()},fe=e=>{J.pageNum=e,de()},be=e=>{J.pageSize=e,J.pageNum=1,de()},ye=e=>{if(!e)return"-";try{const a=new Date(e);return isNaN(a.getTime())?"-":a.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1})}catch(a){return"-"}},we=async()=>{K.value=!0;try{const e=await M();200===e.code&&Array.isArray(e.data)?R.value=e.data.map((e=>({departmentId:e.id||e.departmentId,departmentName:e.name||e.departmentName}))):200===e.code&&e.data&&Array.isArray(e.data.list)?R.value=e.data.list.map((e=>({departmentId:e.id||e.departmentId,departmentName:e.name||e.departmentName}))):(g.error(e.message||"获取部门列表失败"),R.value=[])}catch(e){g.error("加载部门列表失败: "+(e.message||"网络错误")),R.value=[]}finally{K.value=!1}},xe=()=>{if(0===H.value.length)return void g.warning("请至少选择一条开销记录进行延用");const e=new Set;for(const a of H.value){const t=`${a.departmentId}_${a.itemName}`;if(e.has(t))return void g.error('检测到对相同部门的相同项目选择了多个原始记录。请一次只为一种"部门-项目"组合进行延用，或分别操作。');e.add(t)}te.value=H.value.map((e=>({id:e.id,departmentId:e.departmentId,departmentName:e.departmentName,originalExpenseDate:e.expenseDate?e.expenseDate.substring(0,7):"-",itemName:e.itemName,amount:Number(e.amount)||0,remark:e.remark||""}))),0===X.value.length&&ee(),ae.value=!0},Ne=async a=>{if(!le.value)return void g.error("批量延用组件引用错误");const t=[];if(a.selectedItems.forEach((e=>{a.targetMonths.forEach((a=>{const l=`${a}-01`;t.push({departmentId:e.departmentId,expenseDate:l,itemName:e.itemName,amount:e.amount,remark:e.remark})}))})),0===t.length)return g.warning("未能构建任何有效的延用数据"),void(le.value&&"function"==typeof le.value.resetLoadingState&&le.value.resetLoadingState());try{const a=await(l={items:t},e({url:`${S}/extend-batch`,method:"post",data:l}));if(200===a.code&&a.data){const{successfullyExtendedItems:e,skippedDuplicateItems:l}=a.data,r=e?e.length:0,n=l?l.length:0,d=`批量延用处理完成：成功 ${r} 项，失败/跳过 ${n} 项。`;if(r>0&&0===n?g.success(d):r>0&&n>0||0===r&&n>0?g.warning(d):0===r&&0===n&&t.length>0?g.warning(`批量延用尝试处理 ${t.length} 项，但均未成功也未被标记为跳过。`):g.info(a.message||"批量延用已处理，但未返回明确的成功或跳过信息。"),n>0&&l){let e="<strong>被跳过或延用失败的项目详情：</strong><br/>";l.forEach((a=>{e+=`- ${a}<br/>`})),$.alert(e,"跳过项目详情",{confirmButtonText:"我知道了",type:"warning",dangerouslyUseHTMLString:!0})}ae.value=!1,de(),H.value=[]}else g.error(a.message||"批量延用失败，未返回有效数据")}catch(r){g.error("批量延用操作失败: "+(r.message||"网络错误"))}finally{le.value&&"function"==typeof le.value.resetLoadingState&&le.value.resetLoadingState()}var l};return r((()=>{we(),de(),ee()})),(e,a)=>{const t=i("el-option"),l=i("el-select"),r=i("el-icon"),g=i("el-input"),$=i("el-date-picker"),M=i("el-button"),S=i("el-table-column"),ee=i("el-tag"),de=i("el-table"),oe=i("el-pagination"),we=i("el-form-item"),_e=i("el-input-number"),ke=i("el-form"),Ie=i("el-dialog"),Ve=p("loading");return v(),n("div",Y,[d("div",E,[d("div",U,[u(l,{modelValue:L.value,"onUpdate:modelValue":a[0]||(a[0]=e=>L.value=e),placeholder:"选择部门筛选",clearable:"",filterable:"",loading:K.value,onChange:ve},{default:s((()=>[(v(!0),n(h,null,f(R.value,(e=>(v(),c(t,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"]),u(g,{modelValue:A.value,"onUpdate:modelValue":a[1]||(a[1]=e=>A.value=e),placeholder:"搜索项目名称",clearable:"",onKeyup:m(ve,["enter"])},{prefix:s((()=>[u(r,null,{default:s((()=>[u(b(y))])),_:1})])),_:1},8,["modelValue"]),u($,{modelValue:W.value,"onUpdate:modelValue":a[2]||(a[2]=e=>W.value=e),type:"month",placeholder:"选择开销月份",format:"YYYY-MM","value-format":"YYYY-MM",clearable:"",onChange:ve},null,8,["modelValue"]),u(M,{type:"primary",onClick:ve},{default:s((()=>[u(r,null,{default:s((()=>[u(b(y))])),_:1}),a[13]||(a[13]=w("搜索"))])),_:1}),u(M,{onClick:he},{default:s((()=>[u(r,null,{default:s((()=>[u(b(x))])),_:1}),a[14]||(a[14]=w("重置"))])),_:1})]),d("div",T,[u(M,{type:"danger",disabled:0===H.value.length,onClick:pe},{default:s((()=>[u(r,null,{default:s((()=>[u(b(N))])),_:1}),a[15]||(a[15]=w("批量删除 "))])),_:1},8,["disabled"]),u(M,{type:"primary",class:"add-btn",onClick:se},{default:s((()=>[u(r,null,{default:s((()=>[u(b(_))])),_:1}),a[16]||(a[16]=w("添加开销 "))])),_:1}),u(M,{type:"success",disabled:0===H.value.length,onClick:xe},{default:s((()=>[u(r,null,{default:s((()=>[u(b(k))])),_:1}),a[17]||(a[17]=w("延用选中开销 "))])),_:1},8,["disabled"])])]),o((v(),c(de,{data:q.value,border:"","row-key":"id",onSelectionChange:ge,"max-height":"calc(100vh - 220px)",class:"custom-table"},{default:s((()=>[u(S,{type:"selection",width:"55",align:"center"}),u(S,{label:"部门",prop:"departmentName","min-width":"150","show-overflow-tooltip":""}),u(S,{label:"年月",prop:"expenseDate",width:"120",align:"center"},{default:s((({row:e})=>[u(ee,{type:"info"},{default:s((()=>[w(I(e.expenseDate?e.expenseDate.substring(0,7):"-"),1)])),_:2},1024)])),_:1}),u(S,{label:"项目名称",prop:"itemName","min-width":"200","show-overflow-tooltip":""},{default:s((({row:e})=>[w(I(e.itemName||"-"),1)])),_:1}),u(S,{label:"金额",prop:"amount","min-width":"120",align:"right","show-overflow-tooltip":""},{default:s((({row:e})=>{return[w(I((a=e.amount,null==a||isNaN(parseFloat(a))?"¥0.00":"¥"+parseFloat(a).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","))),1)];var a})),_:1}),u(S,{label:"备注",prop:"remark","min-width":"180","show-overflow-tooltip":""},{default:s((({row:e})=>[w(I(e.remark||"-"),1)])),_:1}),u(S,{label:"创建时间",prop:"createTime",width:"180",align:"center","show-overflow-tooltip":""},{default:s((({row:e})=>[w(I(ye(e.createTime)),1)])),_:1}),u(S,{label:"更新时间",prop:"updateTime",width:"180",align:"center","show-overflow-tooltip":""},{default:s((({row:e})=>[w(I(ye(e.updateTime)),1)])),_:1}),u(S,{label:"操作",width:"190",align:"center",fixed:"right","class-name":"operation-column"},{default:s((({row:e})=>[d("div",z,[u(M,{class:"edit-btn",onClick:a=>ie(e),title:"编辑"},{default:s((()=>[u(r,null,{default:s((()=>[u(b(V))])),_:1})])),_:2},1032,["onClick"]),u(M,{class:"delete-btn",onClick:a=>me(e),title:"删除"},{default:s((()=>[u(r,null,{default:s((()=>[u(b(N))])),_:1})])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[Ve,F.value]]),d("div",B,[u(oe,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":J.pageNum,"page-size":J.pageSize,total:J.total,"page-sizes":[10,20,50,100],onSizeChange:be,onCurrentChange:fe},null,8,["current-page","page-size","total"])]),u(Ie,{modelValue:O.value,"onUpdate:modelValue":a[11]||(a[11]=e=>O.value=e),title:"add"===Z.value?"添加部门开销":"编辑部门开销",width:"500px","destroy-on-close":"",class:"custom-dialog","close-on-click-modal":!1},{default:s((()=>[u(ke,{ref_key:"formRef",ref:G,model:Q,rules:ne,"label-width":"100px",class:"dialog-form"},{default:s((()=>["add"===Z.value?(v(),c(we,{key:0,label:"所属部门",prop:"departmentIds"},{default:s((()=>[u(l,{modelValue:Q.departmentIds,"onUpdate:modelValue":a[3]||(a[3]=e=>Q.departmentIds=e),placeholder:"请选择一个或多个部门",style:{width:"100%"},loading:K.value,filterable:"",multiple:"",clearable:""},{default:s((()=>[(v(!0),n(h,null,f(R.value,(e=>(v(),c(t,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})):D("",!0),"edit"===Z.value?(v(),c(we,{key:1,label:"所属部门",prop:"departmentId"},{default:s((()=>[u(l,{modelValue:Q.departmentId,"onUpdate:modelValue":a[4]||(a[4]=e=>Q.departmentId=e),placeholder:"请选择部门",style:{width:"100%"},loading:K.value,filterable:""},{default:s((()=>[(v(!0),n(h,null,f(R.value,(e=>(v(),c(t,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})):D("",!0),"add"===Z.value?(v(),c(we,{key:2,label:"开销月份",prop:"selectedMonths"},{default:s((()=>[u(l,{modelValue:Q.selectedMonths,"onUpdate:modelValue":a[5]||(a[5]=e=>Q.selectedMonths=e),multiple:"",filterable:"",placeholder:"请选择一个或多个开销月份",style:{width:"100%"},clearable:""},{default:s((()=>[(v(!0),n(h,null,f(X.value,(e=>(v(),c(t,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})):D("",!0),"edit"===Z.value?(v(),c(we,{key:3,label:"开销月份",prop:"expenseDate"},{default:s((()=>[u($,{modelValue:Q.expenseDate,"onUpdate:modelValue":a[6]||(a[6]=e=>Q.expenseDate=e),type:"month",placeholder:"选择开销月份",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"100%"},disabled:!0},null,8,["modelValue"])])),_:1})):D("",!0),u(we,{label:"项目名称",prop:"itemName"},{default:s((()=>[u(g,{modelValue:Q.itemName,"onUpdate:modelValue":a[7]||(a[7]=e=>Q.itemName=e),placeholder:"请输入项目名称"},null,8,["modelValue"])])),_:1}),u(we,{label:"金额",prop:"amount"},{default:s((()=>[u(_e,{modelValue:Q.amount,"onUpdate:modelValue":a[8]||(a[8]=e=>Q.amount=e),precision:2,step:100,min:.01,style:{width:"100%"},"controls-position":"right"},null,8,["modelValue"])])),_:1}),u(we,{label:"备注",prop:"remark"},{default:s((()=>[u(g,{modelValue:Q.remark,"onUpdate:modelValue":a[9]||(a[9]=e=>Q.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息 (可选)",maxlength:"255","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),d("div",j,[u(M,{onClick:ue},{default:s((()=>a[18]||(a[18]=[w("取消")]))),_:1}),u(M,{type:"primary",loading:P.value,onClick:a[10]||(a[10]=e=>ce(G.value))},{default:s((()=>a[19]||(a[19]=[w("确定")]))),_:1},8,["loading"])])])),_:1},8,["modelValue","title"]),u(C,{ref_key:"batchExtendDialogRef",ref:le,isVisible:ae.value,"onUpdate:isVisible":a[12]||(a[12]=e=>ae.value=e),"items-to-extend":te.value,"month-options":X.value,"entity-name":"部门开销","item-key-field":"id","item-display-fields":re.value,onSubmitExtend:Ne},null,8,["isVisible","items-to-extend","month-options","item-display-fields"])])}}},[["__scopeId","data-v-24c075a2"]]);export{q as default};
