import{g as a,a as e}from"./performance-DATiRBUB.js";import{_ as l,a as t,r as s,o,c as i,d as r,Z as n,f as d,e as c,g as u,a7 as p,i as v,j as m,Y as f,z as g,E as h,m as y,n as w,a1 as b,t as P}from"./index-CGqeVPF3.js";import"./request-Cm8Ap7dD.js";const x={class:"performance-container"},k={class:"toolbar"},_={class:"filter-actions"},E={key:0},M={class:"black-bold-text"},z={class:"black-bold-text"},C={key:0,class:"empty-block"},L={class:"pagination-container"},Y={key:0,class:"detail-container"},S={class:"detail-header"},D={class:"date-label"},j={class:"completion-rate"},V={class:"detail-content"},O={class:"detail-item"},$={class:"detail-value"},A={class:"detail-item"},F={class:"detail-value highlight"},U={key:0,class:"detail-item"},q={class:"detail-value total"},B={key:1,class:"detail-item"},I={class:"detail-value"},Z={key:2,class:"detail-item"},G={class:"detail-value"},H={key:3,class:"detail-item"},J={class:"detail-value"},K={key:4,class:"detail-item"},N={class:"detail-value black-bold-text"},Q={key:5,class:"detail-item"},R={class:"detail-value black-bold-text"},T={class:"detail-item"},W={class:"dialog-footer"},X=l({__name:"Performance",setup(l){const X=t([]),aa=t(!1),ea=t(""),la=t(null),ta=t(!1),sa=s({page:1,size:10,total:0}),oa=async()=>{try{aa.value=!0;let e={page:sa.page,size:sa.size};ea.value&&(e.date=ea.value.substring(0,7));const l=await a(e);200===l.code?(X.value=l.data||[],l.data&&"object"==typeof l.data&&(Array.isArray(l.data.records)?(X.value=l.data.records,sa.total=l.data.total||0):(X.value=l.data,sa.total=l.data.length||0))):h.error(l.message||"获取业绩数据失败")}catch(e){h.error("获取业绩列表失败，请稍后再试")}finally{aa.value=!1}},ia=async()=>{sa.page=1,await oa()},ra=async()=>{ea.value="",sa.page=1,await oa()},na=a=>{sa.page=a,oa()},da=a=>{sa.size=a,sa.page=1,oa()},ca=(a,e)=>a&&0!==a?Math.round(e/a*100):0,ua=a=>null==a?"¥0.00":"¥"+parseFloat(a).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),pa=a=>{if(!a)return"";if(/^\d{4}-\d{2}$/.test(a)){const[e,l]=a.split("-");return`${e}年${l}月`}return a};return o((()=>{oa()})),(a,l)=>{const t=v("el-date-picker"),s=v("el-icon"),o=v("el-button"),oa=v("el-tag"),va=v("el-table-column"),ma=v("el-table"),fa=v("el-empty"),ga=v("el-pagination"),ha=v("el-progress"),ya=v("el-divider"),wa=v("el-dialog"),ba=f("loading");return y(),i("div",x,[r("div",k,[r("div",_,[c(t,{modelValue:ea.value,"onUpdate:modelValue":l[0]||(l[0]=a=>ea.value=a),type:"month",placeholder:"选择月份",format:"YYYY-MM","value-format":"YYYY-MM-DD","prefix-icon":u(p),onChange:ia,clearable:""},null,8,["modelValue","prefix-icon"]),c(o,{onClick:ra,disabled:!ea.value},{default:m((()=>[c(s,null,{default:m((()=>[c(u(b))])),_:1}),l[3]||(l[3]=w(" 重置 "))])),_:1},8,["disabled"])])]),n((y(),g(ma,{data:X.value,border:"",stripe:"",style:{width:"100%"},"row-key":"id","max-height":"calc(100vh - 280px)",class:"custom-table"},{default:m((()=>[c(va,{label:"年月",prop:"date",width:"120",align:"center"},{default:m((({row:a})=>[c(oa,{type:"info"},{default:m((()=>[w(P(a.date),1)])),_:2},1024)])),_:1}),c(va,{label:"预估业绩",prop:"estimatedPerformance","min-width":"150",align:"right"},{default:m((({row:a})=>[w(P(ua(a.estimatedPerformance)),1)])),_:1}),c(va,{label:"实际业绩",prop:"actualPerformance","min-width":"150",align:"right"},{default:m((({row:a})=>[w(P(ua(a.actualPerformance)),1)])),_:1}),c(va,{label:"本月备用金",prop:"totalPettyCash","min-width":"150",align:"right","show-overflow-tooltip":""},{default:m((({row:a})=>[w(P(ua(a.totalPettyCash)),1)])),_:1}),c(va,{label:"本月发布工资","min-width":"150",align:"right",prop:"totalSalary","show-overflow-tooltip":""},{default:m((({row:a})=>[null!==a.totalSalary&&void 0!==a.totalSalary?(y(),i("span",E,P(ua(a.totalSalary)),1)):(y(),g(oa,{key:1,type:"info",size:"small"},{default:m((()=>l[4]||(l[4]=[w("暂无数据")]))),_:1}))])),_:1}),c(va,{label:"本月平均部门开销",prop:"averageDepartmentExpense","min-width":"170",align:"right","show-overflow-tooltip":""},{default:m((({row:a})=>[w(P(ua(a.averageDepartmentExpense)),1)])),_:1}),c(va,{label:"本月员工费用",prop:"totalEmployeeOtherExpenses","min-width":"160",align:"right","show-overflow-tooltip":""},{default:m((({row:a})=>[w(P(ua(a.totalEmployeeOtherExpenses)),1)])),_:1}),c(va,{label:"本月预计盈亏",prop:"estimatedMonthlyProfitLoss","min-width":"160",align:"right","show-overflow-tooltip":""},{default:m((({row:a})=>[r("span",M,P(ua(a.estimatedMonthlyProfitLoss)),1)])),_:1}),c(va,{label:"本月实际盈亏",prop:"actualMonthlyProfitLoss","min-width":"160",align:"right","show-overflow-tooltip":""},{default:m((({row:a})=>[r("span",z,P(ua(a.actualMonthlyProfitLoss)),1)])),_:1}),c(va,{label:"操作",width:"120",align:"center",fixed:"right","class-name":"operation-column"},{default:m((({row:a})=>[c(o,{type:"primary",size:"small",onClick:l=>(async a=>{try{aa.value=!0;const l=await e(a.date);200===l.code?(la.value=l.data,ta.value=!0):h.error(l.message||"获取业绩详情失败")}catch(l){h.error("获取业绩详情失败，请稍后再试")}finally{aa.value=!1}})(a)},{default:m((()=>l[5]||(l[5]=[w(" 详情 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[ba,aa.value]]),0!==X.value.length||aa.value?d("",!0):(y(),i("div",C,[c(fa,{description:"暂无业绩数据"})])),r("div",L,[c(ga,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":sa.page,"page-size":sa.size,total:sa.total,"page-sizes":[10,20,50,100],onSizeChange:da,onCurrentChange:na},null,8,["current-page","page-size","total"])]),c(wa,{modelValue:ta.value,"onUpdate:modelValue":l[2]||(l[2]=a=>ta.value=a),title:"业绩详情",width:"520px","destroy-on-close":"",class:"custom-dialog","close-on-click-modal":!1},{footer:m((()=>[r("span",W,[c(o,{onClick:l[1]||(l[1]=a=>ta.value=!1)},{default:m((()=>l[16]||(l[16]=[w("关闭")]))),_:1})])])),default:m((()=>[la.value?(y(),i("div",Y,[r("div",S,[r("span",D,P(pa(la.value.date)),1),r("div",j,[c(ha,{type:"circle",percentage:ca(la.value.estimatedPerformance,la.value.actualPerformance),status:ca(la.value.estimatedPerformance,la.value.actualPerformance)>=100?"success":"warning",width:80},null,8,["percentage","status"]),l[6]||(l[6]=r("span",{class:"rate-text"},"完成率",-1))])]),r("div",V,[r("div",O,[l[7]||(l[7]=r("span",{class:"detail-label"},"预估业绩",-1)),r("span",$,P(ua(la.value.estimatedPerformance)),1)]),r("div",A,[l[8]||(l[8]=r("span",{class:"detail-label"},"实际业绩",-1)),r("span",F,P(ua(la.value.actualPerformance)),1)]),c(ya),null!==la.value.totalSalary&&void 0!==la.value.totalSalary?(y(),i("div",U,[l[9]||(l[9]=r("span",{class:"detail-label"},"总工资",-1)),r("span",q,P(ua(la.value.totalSalary)),1)])):d("",!0),null!==la.value.totalPettyCash&&void 0!==la.value.totalPettyCash?(y(),i("div",B,[l[10]||(l[10]=r("span",{class:"detail-label"},"本月备用金",-1)),r("span",I,P(ua(la.value.totalPettyCash)),1)])):d("",!0),null!==la.value.averageDepartmentExpense&&void 0!==la.value.averageDepartmentExpense?(y(),i("div",Z,[l[11]||(l[11]=r("span",{class:"detail-label"},"本月平均部门开销",-1)),r("span",G,P(ua(la.value.averageDepartmentExpense)),1)])):d("",!0),null!==la.value.totalEmployeeOtherExpenses&&void 0!==la.value.totalEmployeeOtherExpenses?(y(),i("div",H,[l[12]||(l[12]=r("span",{class:"detail-label"},"本月员工费用",-1)),r("span",J,P(ua(la.value.totalEmployeeOtherExpenses)),1)])):d("",!0),c(ya),null!==la.value.estimatedMonthlyProfitLoss&&void 0!==la.value.estimatedMonthlyProfitLoss?(y(),i("div",K,[l[13]||(l[13]=r("span",{class:"detail-label"},"本月预计盈亏",-1)),r("span",N,P(ua(la.value.estimatedMonthlyProfitLoss)),1)])):d("",!0),null!==la.value.actualMonthlyProfitLoss&&void 0!==la.value.actualMonthlyProfitLoss?(y(),i("div",Q,[l[14]||(l[14]=r("span",{class:"detail-label"},"本月实际盈亏",-1)),r("span",R,P(ua(la.value.actualMonthlyProfitLoss)),1)])):d("",!0),r("div",T,[l[15]||(l[15]=r("span",{class:"detail-label"},"状态",-1)),c(oa,{size:"small",type:ca(la.value.estimatedPerformance,la.value.actualPerformance)>=100?"success":"warning"},{default:m((()=>[w(P(ca(la.value.estimatedPerformance,la.value.actualPerformance)>=100?"已完成":"未完成"),1)])),_:1},8,["type"])])])])):d("",!0)])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-a708acd3"]]);export{X as default};
