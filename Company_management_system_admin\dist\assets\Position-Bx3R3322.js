import{_ as e,a,r as t,o as l,c as o,e as i,R as s,f as n,k as d,S as r,j as u,T as p,K as m,E as c,m as g,h as v,U as _,I as f,J as h,n as y,V as b,W as w,t as V,Y as x,Z as I,O as k}from"./index-BDR0Pmj6.js";import{g as C,d as N,a as z,u as P}from"./position-94XStkZ1.js";import{b as S}from"./department-jpUZgat6.js";const U={class:"position-container"},j={class:"toolbar"},q={class:"search-box"},A={class:"action-box"},D={class:"operation-buttons"},T={class:"pagination-container"},B={class:"dialog-footer"},K=e({__name:"Position",setup(e){const K=a([]),R=a(!0),E=a(""),F=a(""),J=a([]),L=a(!1),O=t({currentPage:1,pageSize:10,total:0}),W=a(!1),Y=a("add"),Z=a(null),$=a(!1),G=t({position_id:null,position_name:"",position_description:"",department_id:null,department_ids:[],status:"Active"}),H={position_name:[{required:!0,message:"请输入职位名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],department_ids:[{required:!0,message:"请选择所属部门",trigger:"change"},{type:"array",min:1,message:"至少选择一个部门",trigger:"change"}],position_description:[{max:255,message:"长度不能超过 255 个字符",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},M=async()=>{R.value=!0,K.value=[];try{const e=await C({pageNum:O.currentPage,pageSize:O.pageSize,positionName:E.value||void 0,departmentId:F.value||void 0});200===e.code?(K.value=e.data.list||[],O.total=e.data.total||0):c.error(e.msg||"获取职位数据失败")}catch(e){c.error("加载职位数据失败: "+(e.message||"未知错误"))}finally{R.value=!1}},Q=()=>{Z.value&&Z.value.resetFields(),G.position_id=null,G.position_name="",G.position_description="",G.department_id=null,G.department_ids=[],G.status="Active"},X=()=>{W.value=!1,Q()},ee=()=>{Y.value="add",Q(),W.value=!0},ae=()=>{O.currentPage=1,M()},te=()=>{E.value="",F.value="",O.currentPage=1,M()},le=e=>{O.currentPage=e,M()},oe=e=>{O.pageSize=e,O.currentPage=1,M()},ie=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}).replace(/\//g,"-")},se=e=>e.departmentNames&&e.departmentNames.length>0?e.departmentNames.join(", "):e.departmentId?(e=>{const a=J.value.find((a=>a.departmentId===e));return a?a.departmentName:""})(e.departmentId):"";return l((()=>{(async()=>{L.value=!0;try{const e=await S();200===e.code?J.value=e.data||[]:c.error(e.msg||"获取部门列表失败")}catch(e){c.error("加载部门列表失败: "+(e.message||"未知错误"))}finally{L.value=!1}})(),M()})),(e,a)=>{const t=u("el-icon"),l=u("el-input"),C=u("el-option"),S=u("el-select"),ne=u("el-button"),de=u("el-table-column"),re=u("el-tag"),ue=u("el-table"),pe=u("el-pagination"),me=u("el-form-item"),ce=u("el-form"),ge=u("el-dialog"),ve=p("loading");return g(),o("div",U,[i("div",j,[i("div",q,[n(l,{modelValue:E.value,"onUpdate:modelValue":a[0]||(a[0]=e=>E.value=e),placeholder:"搜索职位名称",clearable:"",onKeyup:r(ae,["enter"])},{prefix:d((()=>[n(t,null,{default:d((()=>[n(v(_))])),_:1})])),_:1},8,["modelValue"]),n(S,{modelValue:F.value,"onUpdate:modelValue":a[1]||(a[1]=e=>F.value=e),placeholder:"选择部门",clearable:"",style:{width:"220px"}},{default:d((()=>[(g(!0),o(f,null,h(J.value,(e=>(g(),m(C,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),n(ne,{type:"primary",onClick:ae},{default:d((()=>a[8]||(a[8]=[y("搜索")]))),_:1}),n(ne,{onClick:te},{default:d((()=>[n(t,null,{default:d((()=>[n(v(b))])),_:1}),a[9]||(a[9]=y("重置 "))])),_:1})]),i("div",A,[n(ne,{type:"primary",onClick:ee,class:"add-btn"},{default:d((()=>[n(t,null,{default:d((()=>[n(v(w))])),_:1}),a[10]||(a[10]=y("添加职位 "))])),_:1})])]),s((g(),m(ue,{data:K.value,border:"","row-key":"positionId","max-height":"calc(100vh - 220px)",class:"custom-table"},{default:d((()=>[n(de,{type:"index",width:"70",align:"center",label:"序号",fixed:"","class-name":"index-column"}),n(de,{prop:"positionName",label:"职位名称","min-width":"150","show-overflow-tooltip":""}),n(de,{label:"所属部门","min-width":"150","show-overflow-tooltip":""},{default:d((({row:e})=>[y(V(se(e)),1)])),_:1}),n(de,{prop:"positionDescription",label:"职位描述","min-width":"200","show-overflow-tooltip":""}),n(de,{prop:"status",label:"状态",width:"100",align:"center","show-overflow-tooltip":""},{default:d((({row:e})=>[n(re,{type:"Active"===e.status?"success":"danger",class:"status-tag"},{default:d((()=>[y(V("Active"===e.status?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),n(de,{label:"创建时间","min-width":"160","show-overflow-tooltip":""},{default:d((({row:e})=>[y(V(ie(e.createTime)),1)])),_:1}),n(de,{label:"更新时间","min-width":"160","show-overflow-tooltip":""},{default:d((({row:e})=>[y(V(ie(e.updateTime)),1)])),_:1}),n(de,{label:"操作",width:"180",align:"center",fixed:"right","class-name":"operation-column"},{default:d((({row:e})=>[i("div",D,[n(ne,{class:"edit-btn",onClick:a=>(e=>{Y.value="edit",Q(),G.position_id=e.positionId,G.position_name=e.positionName,G.position_description=e.positionDescription||"",G.department_id=e.departmentId,G.department_ids=e.departmentIds||[],G.status=e.status,W.value=!0})(e),title:"编辑"},{default:d((()=>[n(t,null,{default:d((()=>[n(v(x))])),_:1})])),_:2},1032,["onClick"]),n(ne,{class:"delete-btn",onClick:a=>(e=>{k.confirm(`确定要删除职位 "${e.positionName}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{R.value=!0;try{const a=await N(e.positionId);200===a.code?(c({type:"success",message:"删除职位成功",duration:2e3}),M()):(c({type:"error",message:a.msg||"删除失败",duration:3e3}),R.value=!1)}catch(a){c({type:"error",message:"删除失败: "+(a.message||"未知错误"),duration:3e3}),R.value=!1}})).catch((()=>{}))})(e),title:"删除"},{default:d((()=>[n(t,null,{default:d((()=>[n(v(I))])),_:1})])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[ve,R.value]]),i("div",T,[n(pe,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":O.currentPage,"page-size":O.pageSize,total:O.total,"page-sizes":[10,20,50,100],onSizeChange:oe,onCurrentChange:le},null,8,["current-page","page-size","total"])]),n(ge,{modelValue:W.value,"onUpdate:modelValue":a[7]||(a[7]=e=>W.value=e),title:"add"===Y.value?"添加职位":"编辑职位",width:"500px","destroy-on-close":"",class:"custom-dialog"},{default:d((()=>[n(ce,{ref_key:"formRef",ref:Z,model:G,rules:H,"label-position":"left","label-width":"100px",class:"dialog-form"},{default:d((()=>[n(me,{label:"职位名称",prop:"position_name",required:""},{default:d((()=>[n(l,{modelValue:G.position_name,"onUpdate:modelValue":a[2]||(a[2]=e=>G.position_name=e),placeholder:"请输入职位名称"},null,8,["modelValue"])])),_:1}),n(me,{label:"所属部门",prop:"department_ids",required:""},{default:d((()=>[n(S,{modelValue:G.department_ids,"onUpdate:modelValue":a[3]||(a[3]=e=>G.department_ids=e),placeholder:"请选择所属部门",style:{width:"100%"},loading:L.value,multiple:"","collapse-tags":"","collapse-tags-tooltip":""},{default:d((()=>[(g(!0),o(f,null,h(J.value,(e=>(g(),m(C,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),n(me,{label:"职位描述",prop:"position_description"},{default:d((()=>[n(l,{modelValue:G.position_description,"onUpdate:modelValue":a[4]||(a[4]=e=>G.position_description=e),type:"textarea",rows:3,placeholder:"请输入职位描述"},null,8,["modelValue"])])),_:1}),n(me,{label:"状态",prop:"status",required:""},{default:d((()=>[n(S,{modelValue:G.status,"onUpdate:modelValue":a[5]||(a[5]=e=>G.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:d((()=>[n(C,{label:"启用",value:"Active"}),n(C,{label:"禁用",value:"Inactive"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"]),i("div",B,[n(ne,{onClick:X},{default:d((()=>a[11]||(a[11]=[y("取消")]))),_:1}),n(ne,{type:"primary",loading:$.value,onClick:a[6]||(a[6]=e=>(async e=>{e&&await e.validate((async e=>{if(!e)return c({type:"warning",message:"请完善表单信息",duration:2e3}),!1;$.value=!0;try{const e={positionId:G.position_id,positionName:G.position_name,positionDescription:G.position_description,departmentId:G.department_ids&&G.department_ids.length>0?G.department_ids[0]:null,departmentIds:G.department_ids,status:G.status};let a;a="add"===Y.value?await z(e):await P(e),200===a.code?(c({type:"success",message:"add"===Y.value?"添加职位成功":"更新职位成功",duration:2e3}),W.value=!1,Q(),M()):c({type:"error",message:a.msg||("add"===Y.value?"添加失败":"更新失败"),duration:3e3})}catch(a){c({type:"error",message:"提交失败: "+(a.message||"未知错误"),duration:3e3})}finally{$.value=!1}}))})(Z.value))},{default:d((()=>a[12]||(a[12]=[y("确定")]))),_:1},8,["loading"])])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-5487b0b1"]]);export{K as default};
