import{u as e}from"./token-Ced5ba2J.js";import{g as a,a as t,b as l}from"./department-DD9JCT-L.js";import{_ as n,a as s,x as r,a8 as u,c as d,m as i,d as o,e as p,z as c,f as m,j as v,g as y,a9 as g,aa as h,B as f,ab as w,t as b,ac as _,Z as k,ad as D,ae as z,af as x,a0 as S,n as I,ag as C,ah as N,ai as j,aj as E,ak as P,Y as V,F as K,A,al as $,am as F,an as O,ao as L,o as T,E as q,ap as M}from"./index-CGqeVPF3.js";import"./request-Cm8Ap7dD.js";const R={class:"node-header"},Y={class:"department-name"},B={class:"node-content"},U={key:0,class:"employee-section"},Z={class:"section-header"},G={class:"search-box"},H={key:0,class:"search-result-info"},J={key:2,style:{"text-align":"center",color:"#999",margin:"10px 0"}},Q={key:3,class:"pagination-container"},W={key:1,class:"sub-departments-section"},X={key:2,class:"sub-departments-section"},ee={key:0},ae={key:1,style:{"text-align":"center",color:"#999",margin:"10px 0"}},te={key:3,class:"siblings-section"},le=n({__name:"DepartmentNode",props:{department:{type:Object,required:!0},depth:{type:Number,default:0}},setup(e){const a=O((()=>L((()=>Promise.resolve().then((()=>ne))),void 0))),t=e,l=s(0===t.depth),n=s([]),T=s(!1),q=s(!1),M=s(""),le=u("displayData"),se=u("fetchEmployees"),re=u("searchEmployees"),ue=u("loadSubDepartmentsForNode"),de=u("handlePageChange"),ie=u("handleSizeChange"),oe=u("formatDate"),pe=r((()=>le.value[t.department.id])),ce=()=>{re&&re(t.department.id,M.value.trim())},me=()=>{M.value="",re(t.department.id,"")},ve=async()=>{if(l.value=!l.value,l.value&&(t.department.isVirtual||pe.value&&0!==pe.value.employees.length||"function"==typeof se&&await se(t.department.id,1),!t.department.subDepartments&&0===n.value.length&&!q.value))if(T.value=!0,q.value=!0,"function"==typeof ue)try{const e=await ue(t.department.id);n.value=e||[]}catch(e){n.value=[]}finally{T.value=!1}else T.value=!1};return l.value&&(t.department.isVirtual||pe.value&&0!==pe.value.employees.length||"function"==typeof se&&se(t.department.id,1),t.department.subDepartments||0!==n.value.length||q.value||(T.value=!0,q.value=!0,"function"==typeof ue?ue(t.department.id).then((e=>{n.value=e||[]})).catch((e=>{n.value=[]})).finally((()=>{T.value=!1})):T.value=!1)),(t,s)=>{const r=V("loading");return i(),d("div",{class:f(["department-node",{"virtual-node":e.department.isVirtual}]),style:F({marginLeft:20*e.depth+"px"})},[o("div",R,[p(y(w),{onClick:ve,class:f(["expand-icon",{"is-expanded":l.value}])},{default:v((()=>[l.value?(i(),c(y(h),{key:1})):(i(),c(y(g),{key:0}))])),_:1},8,["class"]),o("span",Y,b(e.department.name),1),T.value?(i(),c(y(w),{key:0,class:"is-loading",style:{"margin-left":"5px"}},{default:v((()=>[p(y(_))])),_:1})):m("",!0)]),p(y($),null,{default:v((()=>{var t,u,g,h,f,w,_,V,$,F;return[k(o("div",B,[e.department.isVirtual?m("",!0):k((i(),d("div",U,[o("div",Z,[s[3]||(s[3]=o("h4",null,"员工列表",-1)),o("div",G,[p(y(z),{modelValue:M.value,"onUpdate:modelValue":s[0]||(s[0]=e=>M.value=e),placeholder:"搜索员工姓名",size:"small",clearable:"",onClear:me},{append:v((()=>[p(y(x),{onClick:ce,icon:y(S)},null,8,["icon"])])),_:1},8,["modelValue"])])]),(null==(t=pe.value)?void 0:t.searchKeyword)&&!(null==(u=pe.value)?void 0:u.loading)?(i(),d("div",H,[I(' 搜索"'+b(pe.value.searchKeyword)+'"：找到 '+b(pe.value.total)+" 条结果 ",1),p(y(x),{type:"text",onClick:me},{default:v((()=>s[4]||(s[4]=[I("清除搜索")]))),_:1})])):m("",!0),(null==(h=null==(g=pe.value)?void 0:g.employees)?void 0:h.length)>0?(i(),c(y(E),{key:1,data:pe.value.employees,style:{width:"100%","margin-bottom":"10px"},border:"",fit:"",size:"small"},{default:v((()=>[p(y(C),{prop:"employeeId",label:"员工ID",width:"80",align:"center"}),p(y(C),{prop:"name",label:"姓名","min-width":"100",align:"center"}),p(y(C),{prop:"email",label:"邮箱","min-width":"180",align:"center"}),p(y(C),{label:"部门","min-width":"120",align:"center"},{default:v((e=>[o("span",null,b(e.row.departmentName||"未分配"),1)])),_:1}),p(y(C),{label:"职位","min-width":"120",align:"center"},{default:v((e=>[o("span",null,b(e.row.positionName||"未分配"),1)])),_:1}),p(y(C),{label:"入职日期","min-width":"120",align:"center"},{default:v((e=>[o("span",null,b(y(oe)(e.row.entryDate)),1)])),_:1}),p(y(C),{prop:"status",label:"状态",width:"100",align:"center"},{default:v((e=>["Inactive"===e.row.status?(i(),c(y(N),{key:0,content:e.row.exitDate?`离职日期: ${y(oe)(e.row.exitDate)}`:"无离职日期记录",placement:"top"},{default:v((()=>[p(y(j),{type:"Active"===e.row.status?"success":"Inactive"===e.row.status?"danger":"info",size:"small"},{default:v((()=>[I(b("Active"===e.row.status?"在职":"Inactive"===e.row.status?"离职":e.row.status||"-"),1)])),_:2},1032,["type"])])),_:2},1032,["content"])):(i(),c(y(j),{key:1,type:"Active"===e.row.status?"success":"Inactive"===e.row.status?"danger":"info",size:"small"},{default:v((()=>[I(b("Active"===e.row.status?"在职":"Inactive"===e.row.status?"离职":e.row.status||"-"),1)])),_:2},1032,["type"]))])),_:1}),p(y(C),{label:"物流线路","min-width":"120",align:"center"},{default:v((e=>[o("span",null,b(e.row.logisticsRoute||"-"),1)])),_:1})])),_:1},8,["data"])):(null==(f=pe.value)?void 0:f.loading)||0!==(null==(_=null==(w=pe.value)?void 0:w.employees)?void 0:_.length)?m("",!0):(i(),d("p",J,b((null==(V=pe.value)?void 0:V.searchKeyword)?"没有找到符合条件的员工":"暂无员工数据"),1)),(null==($=pe.value)?void 0:$.total)>0?(i(),d("div",Q,[p(y(P),{background:"",size:"small",layout:"total, sizes, prev, pager, next","page-sizes":[5,10,20],total:pe.value.total,"page-size":pe.value.pageSize,"current-page":pe.value.currentPage,onCurrentChange:s[1]||(s[1]=a=>y(de)(e.department.id,a)),onSizeChange:s[2]||(s[2]=a=>y(ie)(e.department.id,a))},null,8,["total","page-size","current-page"])])):m("",!0)])),[[r,null==(F=pe.value)?void 0:F.loading]]),e.department.subDepartments&&e.department.subDepartments.length>0?(i(),d("div",W,[(i(!0),d(K,null,A(e.department.subDepartments,(t=>(i(),c(y(a),{key:t.id,department:t,depth:e.depth+1},null,8,["department","depth"])))),128))])):(i(),d("div",X,[n.value.length>0||T.value?(i(),d("h4",ee,"子部门")):m("",!0),(i(!0),d(K,null,A(n.value,(t=>(i(),c(y(a),{key:t.id,department:t,depth:e.depth+1},null,8,["department","depth"])))),128)),!T.value&&l.value&&q.value&&0===n.value.length?(i(),d("p",ae,"无子部门")):m("",!0)])),e.department.siblings&&e.department.siblings.length>0?(i(),d("div",te,[(i(!0),d(K,null,A(e.department.siblings,(t=>(i(),c(y(a),{key:t.id,department:t,depth:e.depth},null,8,["department","depth"])))),128))])):m("",!0)],512),[[D,l.value]])]})),_:1})],6)}}},[["__scopeId","data-v-863d0bd6"]]),ne=Object.freeze(Object.defineProperty({__proto__:null,default:le},Symbol.toStringTag,{value:"Module"})),se={class:"department-employee-list"},re={key:0,class:"department-tree-container"},ue={key:1,class:"empty-state"},de={key:0},ie={key:1},oe=n({__name:"DepartmentEmployeeList",setup(n){e();const r=s(!1),u=s(null),o=s({}),c=e=>{o.value[e]||(o.value[e]={employees:[],currentPage:1,pageSize:10,total:0,loading:!1,searchKeyword:""})},m=async(e,a=1,l=10)=>{if(!e||!o.value[e])return;const n=o.value[e];n.loading=!0;try{const l={pageNum:a,pageSize:n.pageSize};n.searchKeyword&&(l.name=n.searchKeyword);const s=await t(e,l);200===s.code?(n.employees=s.data.list||[],n.total=s.data.total,n.currentPage=a):(q.error(s.message||"获取员工数据失败"),n.employees=[],n.total=0)}catch(s){q.error(`获取部门 ${e} 员工信息失败`),n.employees=[],n.total=0}finally{n.loading=!1}};return M("displayData",o),M("formatDate",(e=>{if(!e)return"-";try{const a=new Date(e);if(isNaN(a.getTime()))return"-";const t=a.getFullYear(),l=String(a.getMonth()+1).padStart(2,"0");return`${t}-${l}-${String(a.getDate()).padStart(2,"0")}`}catch(a){return"-"}})),M("fetchEmployees",m),M("searchEmployees",(async(e,a)=>{e&&o.value[e]&&(o.value[e].searchKeyword=a,o.value[e].currentPage=1,await m(e,1))})),M("handlePageChange",((e,a)=>{m(e,a)})),M("handleSizeChange",((e,a)=>{o.value[e]&&(o.value[e].pageSize=a,o.value[e].currentPage=1,m(e,1))})),M("loadSubDepartmentsForNode",(async e=>{if(!e)return[];try{const a=await l(e);if(200===a.code){const e=(a.data||[]).map((e=>({id:e.departmentId,name:e.departmentName})));return e.forEach((e=>{c(e.id)})),e}return q.error(a.message||"获取子部门列表失败"),[]}catch(a){return q.error("获取子部门列表失败"),[]}})),T((async()=>{await(async()=>{r.value=!0;try{const e=await a();if(200===e.code){const{myDepartment:a,leadingDepartments:t}=e.data;if(u.value=null,t&&t.length>0){const e={id:"leading-departments",name:"负责部门",isVirtual:!0,subDepartments:t.map((e=>({id:e.departmentId,name:e.departmentName})))};u.value=e,t.forEach((e=>{c(e.departmentId)}))}return u.value||q.error("您没有负责的部门，无法加载员工列表"),u.value}return q.error(e.message||"获取当前部门信息失败"),u.value=null,null}catch(e){return q.error("获取当前部门信息失败"),u.value=null,null}finally{r.value=!1}})(),u.value||q.error("无法获取您的部门信息，无法加载员工列表")})),(e,a)=>(i(),d("div",se,[u.value?(i(),d("div",re,[p(le,{department:u.value,depth:0},null,8,["department"])])):(i(),d("div",ue,[r.value?(i(),d("p",de,"正在加载部门数据...")):(i(),d("p",ie,"无法获取您的部门信息，无法加载员工列表"))]))]))}},[["__scopeId","data-v-cf2cf3b6"]]);export{oe as default};
