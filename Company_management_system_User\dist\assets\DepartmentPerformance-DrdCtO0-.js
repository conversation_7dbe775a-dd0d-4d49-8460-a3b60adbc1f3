import{_ as e,x as t,v as a,z as l,m as r,j as o,Z as n,f as s,Y as i,i as d,e as p,d as u,t as m,B as c,n as f,a as h,r as g,o as v,c as y,g as w,a7 as b,$ as P,E as _,a0 as D,a1 as M,N as x}from"./index-CGqeVPF3.js";import{b as C,c as N}from"./performance-DATiRBUB.js";import{c as E}from"./department-DD9JCT-L.js";import"./request-Cm8Ap7dD.js";const S={class:"dialog-header"},$={class:"date-range-tag"},L={class:"dialog-footer"},k=e({__name:"PerformanceStatistics",props:{statisticsData:{type:Array,default:()=>[]},performanceList:{type:Array,default:()=>[]},selectedDepartments:{type:Array,default:()=>[]},departmentOptions:{type:Array,default:()=>[]},dateRange:{type:Array,default:()=>[]},visible:{type:Boolean,default:!1},formatCurrency:{type:Function,required:!0},loading:{type:Boolean,default:!1}},emits:["update:visible","close"],setup(e,{emit:h}){const g=e,v=h,y=()=>{v("update:visible",!1),v("close")},w=t((()=>{if(!g.dateRange||g.dateRange.length<2||!g.dateRange[0]||!g.dateRange[1])return"全部时间";const e=g.dateRange[0].substring(0,7),t=g.dateRange[1].substring(0,7),a=e=>{const[t,a]=e.split("-");return`${t}年${a}月`};return`${a(e)} 至 ${a(t)}`})),b=e=>{if(!e)return"未知部门";const t=String(e),a=(e,t)=>{const l=String(t);for(const r of e){if(String(r.value)===l)return r.label;if(r.children&&r.children.length>0){const e=a(r.children,t);if(e)return e}}return null};return a(g.departmentOptions,t)||`部门(${t})`};t((()=>g.selectedDepartments&&0!==g.selectedDepartments.length?g.selectedDepartments.length<=3?g.selectedDepartments.map((e=>b(e))).join("、"):g.selectedDepartments.slice(0,3).map((e=>b(e))).join("、")+`等${g.selectedDepartments.length}个部门`:"未选择部门"));const P=t((()=>{const e=[];if(!g.selectedDepartments||0===g.selectedDepartments.length)return[];const t=(e=>{if(!e||e.length<2||!e[0]||!e[1]){if(g.statisticsData&&g.statisticsData.length>0){const e=new Set(g.statisticsData.map((e=>e.yearMonth)));return Array.from(e).sort()}return[]}const t=[];let a=new Date(e[0].substring(0,7)+"-01");const l=new Date(e[1].substring(0,7)+"-01");for(;a<=l;){const e=a.getFullYear(),l=(a.getMonth()+1).toString().padStart(2,"0");t.push(`${e}-${l}`),a.setMonth(a.getMonth()+1)}return t})(g.dateRange);if(0===t.length&&(!g.statisticsData||0===g.statisticsData.length))return[];const a=g.statisticsData||[],l=t.length>0?t:a.length>0?Array.from(new Set(a.map((e=>e.yearMonth)))).sort():[],r=g.selectedDepartments;for(const o of r){const t=b(o);for(const r of l){const l=a.find((e=>("number"==typeof e.departmentId?e.departmentId.toString():e.departmentId)===("number"==typeof o?o.toString():o)&&e.yearMonth===r));l?e.push(l):e.push({departmentId:o,departmentName:t,yearMonth:r,estimatedPerformance:0,actualPerformance:0,totalSalary:0,totalPettyCash:0,totalDepartmentExpense:0,totalEmployeeExpense:0,estimatedMonthlyProfitLoss:0,monthlyProfitLoss:0,recordCount:0})}}return e.sort(((e,t)=>{if(e.yearMonth>t.yearMonth)return-1;if(e.yearMonth<t.yearMonth)return 1;const a=String(e.departmentName||""),l=String(t.departmentName||"");return a<l?-1:a>l?1:0})),e})),_=t((()=>{if(!P.value||0===P.value.length)return{departmentName:"总计",yearMonth:"-",estimatedPerformance:0,actualPerformance:0,totalSalary:0,totalPettyCash:0,totalDepartmentExpense:0,totalEmployeeExpense:0,estimatedMonthlyProfitLoss:0,monthlyProfitLoss:0};return{departmentName:"总计",yearMonth:"-",...P.value.reduce(((e,t)=>(e.estimatedPerformance+=parseFloat(t.estimatedPerformance||0),e.actualPerformance+=parseFloat(t.actualPerformance||0),e.totalSalary+=parseFloat(t.totalSalary||0),e.totalPettyCash+=parseFloat(t.totalPettyCash||0),e.totalDepartmentExpense+=parseFloat(t.totalDepartmentExpense||0),e.totalEmployeeExpense+=parseFloat(t.totalEmployeeExpense||0),e.estimatedMonthlyProfitLoss+=parseFloat(t.estimatedMonthlyProfitLoss||0),e.monthlyProfitLoss+=parseFloat(t.monthlyProfitLoss||0),e)),{estimatedPerformance:0,actualPerformance:0,totalSalary:0,totalPettyCash:0,totalDepartmentExpense:0,totalEmployeeExpense:0,estimatedMonthlyProfitLoss:0,monthlyProfitLoss:0})}})),D=t((()=>P.value&&0!==P.value.length?[...P.value,_.value]:[_.value]));return a((()=>g.performanceList),(()=>{}),{deep:!0}),a((()=>g.statisticsData),(e=>{}),{deep:!0}),(t,a)=>{const h=d("el-table-column"),g=d("el-table"),b=d("el-empty"),P=d("el-button"),_=d("el-dialog"),M=i("loading");return r(),l(_,{"model-value":e.visible,"onUpdate:modelValue":a[0]||(a[0]=e=>v("update:visible",e)),width:"80vw",onClose:y,"destroy-on-close":"",class:"performance-statistics-dialog"},{header:o((()=>[u("div",S,[a[1]||(a[1]=u("div",{class:"dialog-title"},"部门利润统计",-1)),u("div",$,m(w.value),1)])])),footer:o((()=>[u("div",L,[p(P,{type:"primary",onClick:y},{default:o((()=>a[2]||(a[2]=[f("关闭")]))),_:1})])])),default:o((()=>[n((r(),l(g,{data:D.value,border:"",stripe:"",style:{width:"100%"},"row-class-name":e=>"总计"===e.departmentName?"total-row":""},{default:o((()=>[p(h,{label:"年月",prop:"yearMonth","min-width":"120",align:"center","show-overflow-tooltip":""},{default:o((({row:e})=>[u("span",{class:c({"total-cell":"总计"===e.departmentName&&"-"===e.yearMonth})},m(e.yearMonth),3)])),_:1}),p(h,{label:"部门",prop:"departmentName","min-width":"180",align:"center","show-overflow-tooltip":""}),p(h,{label:"本月预估业绩",prop:"estimatedPerformance","min-width":"150",align:"right","show-overflow-tooltip":""},{default:o((({row:t})=>[u("span",{class:c({"total-cell":"总计"===t.departmentName})},m(e.formatCurrency(t.estimatedPerformance)),3)])),_:1}),p(h,{label:"本月实际业绩",prop:"actualPerformance","min-width":"150",align:"right","show-overflow-tooltip":""},{default:o((({row:t})=>[u("span",{class:c({"total-cell":"总计"===t.departmentName})},m(e.formatCurrency(t.actualPerformance)),3)])),_:1}),p(h,{label:"本月发布工资",prop:"totalSalary","min-width":"150",align:"right","show-overflow-tooltip":""},{default:o((({row:t})=>[u("span",{class:c({"total-cell":"总计"===t.departmentName})},m(e.formatCurrency(t.totalSalary)),3)])),_:1}),p(h,{label:"本月备用金",prop:"totalPettyCash","min-width":"150",align:"right","show-overflow-tooltip":""},{default:o((({row:t})=>[u("span",{class:c({"total-cell":"总计"===t.departmentName})},m(e.formatCurrency(t.totalPettyCash)),3)])),_:1}),p(h,{label:"本月部门总开销",prop:"totalDepartmentExpense","min-width":"160",align:"right","show-overflow-tooltip":""},{default:o((({row:t})=>[u("span",{class:c({"total-cell":"总计"===t.departmentName})},m(e.formatCurrency(t.totalDepartmentExpense)),3)])),_:1}),p(h,{label:"本月员工总费用",prop:"totalEmployeeExpense","min-width":"160",align:"right","show-overflow-tooltip":""},{default:o((({row:t})=>[u("span",{class:c({"total-cell":"总计"===t.departmentName})},m(e.formatCurrency(t.totalEmployeeExpense)),3)])),_:1}),p(h,{label:"本月预计盈亏",prop:"estimatedMonthlyProfitLoss","min-width":"160",align:"right","show-overflow-tooltip":""},{default:o((({row:t})=>[u("span",{class:c({"total-cell":"总计"===t.departmentName,"bold-profit-loss":!0})},m(e.formatCurrency(t.estimatedMonthlyProfitLoss)),3)])),_:1}),p(h,{label:"本月实际盈亏",prop:"monthlyProfitLoss","min-width":"160",align:"right","show-overflow-tooltip":""},{default:o((({row:t})=>[u("span",{class:c({"total-cell":"总计"===t.departmentName,"bold-profit-loss":!0})},m(e.formatCurrency(t.monthlyProfitLoss)),3)])),_:1})])),_:1},8,["data","row-class-name"])),[[M,e.loading]]),e.loading||e.statisticsData&&0!==e.statisticsData.length?s("",!0):(r(),l(b,{key:0,description:"暂无业绩统计数据"}))])),_:1},8,["model-value"])}}},[["__scopeId","data-v-0d9e1542"]]),z={class:"department-performance-container"},F={class:"toolbar"},I={class:"filter-actions"},A={key:0,class:"date-format"},Y={key:0},V={key:0},j={class:"pagination-container"},R={key:0,class:"empty-data"},U=e({__name:"DepartmentPerformance",setup(e){const t=h([]),a=h(!1),S=h([]),$=h(""),L=h([]),U=h(!1),B=h(null),O=h(!1),q=h([]),K=h([]),T=h([]),Z={checkStrictly:!0,emitPath:!1,expandTrigger:"hover",multiple:!0},G=h([]),H=h(!1),J=e=>{if(G.value.includes(e))return!0;for(const t of q.value)if(G.value.includes(t.value)){const a=(e,t)=>{if(!e||0===e.length)return!1;for(const l of e){if(l.value===t)return!0;if(l.children&&a(l.children,t))return!0}return!1};if(a(t.children,e))return!0}return!1},Q=g({page:1,size:10,total:0}),W=async()=>{if(!T.value||0===T.value.length)return void _.warning("请先选择要查看的部门");let e=!1;for(const t of T.value)if(!J(t)){e=!0;break}if(e)return _.warning("您只能查看自己负责的部门及其下级部门的业绩数据"),t.value=[],void(Q.total=0);try{a.value=!0;let e={page:Q.page,size:Q.size,departmentIds:T.value};S.value&&2===S.value.length&&(e.startDate=S.value[0].substring(0,7),e.endDate=S.value[1].substring(0,7)),$.value&&""!==$.value.trim()&&(e.employeeName=$.value.trim());const l=await C(e);if(200===l.code){let e=[];l.data&&"object"==typeof l.data&&(Array.isArray(l.data.records)?(e=l.data.records.filter((e=>null!=e)),e=e.map(((e,t)=>(e.id||(e.id=`${e.date}_${e.departmentId||T.value[0]}_${t}`),!e.departmentName&&e.department?e.departmentName=e.department:e.departmentName||(e.departmentName=B.value?B.value.name:"未知部门"),e))),Q.total=l.data.total||0):Array.isArray(l.data)?(e=l.data.filter((e=>null!=e)),e=e.map(((e,t)=>(e.id||(e.id=`${e.date}_${e.departmentId||T.value[0]}_${t}`),!e.departmentName&&e.department?e.departmentName=e.department:e.departmentName||(e.departmentName=B.value?B.value.name:"未知部门"),e))),Q.total=e.length||0):(e=[],Q.total=0)),t.value=e,e.length}else _.error(l.message||"获取部门业绩数据失败")}catch(l){_.error("获取部门业绩列表失败，请稍后再试")}finally{a.value=!1}},X=e=>e&&e.length?e.map((e=>({value:e.departmentId,label:e.departmentName,children:X(e.children||[])}))):[],ee=e=>{const t=[],a=(e,t)=>{if(!e||!e.length)return null;for(const l of e){if(l.value===t)return l;if(l.children&&l.children.length){const e=a(l.children,t);if(e)return e}}return null},l=e=>{if(e.children&&e.children.length)for(const a of e.children)t.push(a.value),l(a)},r=a(q.value,e);return r&&l(r),t},te=e=>{if(!e||0===e.length)return T.value=[],K.value=[],Q.page=1,void W();const t=T.value||[],a=e.filter((e=>!t.includes(e))),l=t.filter((t=>!e.includes(t)));let r=[...e],o=[...e];for(const n of a){const e=ee(n);if(e.length>0)for(const t of e)r.includes(t)||r.push(t),o.includes(t)||o.push(t)}for(const n of l){const e=ee(n);e.length>0&&(r=r.filter((t=>!e.includes(t))),o=o.filter((t=>!e.includes(t))))}K.value=o,T.value=r,Q.page=1,W()},ae=async()=>{Q.page=1,await W()},le=async()=>{S.value=[],$.value="",Q.page=1,await W()},re=e=>{Q.page=e,W()},oe=e=>{Q.size=e,Q.page=1,W()},ne=e=>null==e?"¥0.00":"¥"+parseFloat(e).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),se=e=>{if(!e)return"";if(/^\d{4}-\d{2}$/.test(e)){const[t,a]=e.split("-");return`${t}年${a}月`}return e},ie=e=>{if(null==e)return"";const t=parseFloat(e);return t>0?"profit-positive":t<0?"profit-negative":""},de=async()=>{if(!T.value||0===T.value.length)return void _.warning("请先选择要统计的部门");let e=!1;for(const a of T.value)if(!J(a)){e=!0;break}if(e)_.warning("您只能统计自己负责的部门及其下级部门的业绩数据");else try{U.value=!0;const e={departmentIds:T.value};S.value&&2===S.value.length&&(e.startDate=S.value[0].substring(0,7),e.endDate=S.value[1].substring(0,7));const t=await N(e);200===t.code?(L.value=t.data||[],H.value=!0):_.error(t.message||"获取部门业绩统计数据失败")}catch(t){_.error("获取部门业绩统计数据失败，请稍后再试")}finally{U.value=!1}};return v((async()=>{S.value=(()=>{const e=new Date,t=e.getFullYear(),a=e.getMonth()+1,l=a<10?`0${a}`:a;return[`${t}-${l}-01`,`${t}-${l}-${new Date(t,a,0).getDate()}`]})(),await(async()=>{try{O.value=!0;const e=await E();if(200===e.code){const t=e.data;if(q.value=t.map((e=>({value:e.departmentId,label:e.departmentName,children:X(e.children||[])}))),G.value=t.map((e=>e.departmentId)),G.value.length>0){const e=G.value[0],t=ee(e);K.value=[e,...t],T.value=[e,...t],await W()}else _.warning("您没有任何可以查看业绩的部门")}else _.error(e.message||"获取部门信息失败")}catch(e){_.error("加载部门信息失败，请稍后再试")}finally{O.value=!1}})()})),(e,h)=>{const g=d("el-cascader"),v=d("el-date-picker"),_=d("el-button"),C=d("el-input"),N=d("el-icon"),E=d("el-tag"),B=d("el-table-column"),G=d("el-table"),J=d("el-pagination"),W=d("el-empty"),X=i("loading");return r(),y("div",z,[u("div",F,[u("div",I,[p(g,{modelValue:K.value,"onUpdate:modelValue":h[0]||(h[0]=e=>K.value=e),options:q.value,props:Z,placeholder:"请选择您负责的部门",clearable:"",loading:O.value,onChange:te,style:{width:"280px"},"collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":3},null,8,["modelValue","options","loading"]),p(v,{modelValue:S.value,"onUpdate:modelValue":h[1]||(h[1]=e=>S.value=e),type:"monthrange","range-separator":"至","start-placeholder":"开始月份","end-placeholder":"结束月份",format:"YYYY-MM","value-format":"YYYY-MM-DD","prefix-icon":w(b),onChange:ae,clearable:"",style:{"margin-right":"10px"}},null,8,["modelValue","prefix-icon"]),p(C,{modelValue:$.value,"onUpdate:modelValue":h[2]||(h[2]=e=>$.value=e),placeholder:"请输入员工姓名",clearable:"",onKeyup:P(ae,["enter"]),onClear:ae,style:{width:"200px","margin-right":"10px"}},{append:o((()=>[p(_,{icon:w(D),onClick:ae},null,8,["icon"])])),_:1},8,["modelValue"]),p(_,{onClick:le,disabled:!S.value||S.value.length<2},{default:o((()=>[p(N,null,{default:o((()=>[p(w(M))])),_:1}),h[4]||(h[4]=f(" 重置 "))])),_:1},8,["disabled"]),p(_,{type:"primary",onClick:de,disabled:!T.value||0===T.value.length},{default:o((()=>[p(N,null,{default:o((()=>[p(w(x))])),_:1}),h[5]||(h[5]=f(" 利润统计 "))])),_:1},8,["disabled"])])]),n((r(),l(G,{data:t.value,border:"",stripe:"",style:{width:"100%"},"row-key":e=>e.employeeId+"_"+e.date,"max-height":"calc(100vh - 280px)",class:"custom-table"},{default:o((()=>[p(B,{label:"年月",prop:"date",width:"120",align:"center","show-overflow-tooltip":""},{default:o((({row:e})=>[e.date?(r(),y("span",A,m(se(e.date)),1)):(r(),l(E,{key:1,type:"info",size:"small"},{default:o((()=>h[6]||(h[6]=[f("暂无数据")]))),_:1}))])),_:1}),p(B,{label:"员工姓名",prop:"employeeName","min-width":"120",align:"center","show-overflow-tooltip":""},{default:o((({row:e})=>[f(m(e.employeeName||"未知"),1)])),_:1}),p(B,{label:"部门",prop:"departmentName","min-width":"150",align:"center","show-overflow-tooltip":""},{default:o((({row:e})=>[f(m(e.departmentName||e.department||"总计"),1)])),_:1}),p(B,{label:"预估业绩",prop:"estimatedPerformance","min-width":"150",align:"right","show-overflow-tooltip":""},{default:o((({row:e})=>[f(m(ne(e.estimatedPerformance)),1)])),_:1}),p(B,{label:"实际业绩",prop:"actualPerformance","min-width":"150",align:"right","show-overflow-tooltip":""},{default:o((({row:e})=>[f(m(ne(e.actualPerformance)),1)])),_:1}),p(B,{label:"本月备用金",prop:"totalPettyCash","min-width":"150",align:"right","show-overflow-tooltip":""},{default:o((({row:e})=>[null!==e.totalPettyCash&&void 0!==e.totalPettyCash?(r(),y("span",Y,m(ne(e.totalPettyCash)),1)):(r(),l(E,{key:1,type:"info",size:"small"},{default:o((()=>h[7]||(h[7]=[f("无记录")]))),_:1}))])),_:1}),p(B,{label:"本月发布工资","min-width":"150",align:"right",prop:"totalSalary","show-overflow-tooltip":""},{default:o((({row:e})=>[null!==e.totalSalary&&void 0!==e.totalSalary?(r(),y("span",V,m(ne(e.totalSalary)),1)):(r(),l(E,{key:1,type:"info",size:"small"},{default:o((()=>h[8]||(h[8]=[f("本月暂无数据")]))),_:1}))])),_:1}),p(B,{label:"本月平均部门开销",prop:"averageDepartmentExpense","min-width":"170",align:"right","show-overflow-tooltip":""},{default:o((({row:e})=>[f(m(ne(e.averageDepartmentExpense)),1)])),_:1}),p(B,{label:"本月员工费用",prop:"totalEmployeeOtherExpenses","min-width":"160",align:"right","show-overflow-tooltip":""},{default:o((({row:e})=>[f(m(ne(e.totalEmployeeOtherExpenses)),1)])),_:1}),p(B,{label:"本月预计盈亏",prop:"estimatedMonthlyProfitLoss","min-width":"160",align:"right","show-overflow-tooltip":""},{default:o((({row:e})=>[u("span",{class:c([ie(e.estimatedMonthlyProfitLoss),"font-bold"])},m(ne(e.estimatedMonthlyProfitLoss)),3)])),_:1}),p(B,{label:"本月实际盈亏",prop:"actualMonthlyProfitLoss","min-width":"160",align:"right","show-overflow-tooltip":""},{default:o((({row:e})=>[u("span",{class:c([ie(e.actualMonthlyProfitLoss),"font-bold"])},m(ne(e.actualMonthlyProfitLoss)),3)])),_:1})])),_:1},8,["data","row-key"])),[[X,a.value]]),u("div",j,[p(J,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":Q.page,"page-size":Q.size,total:Q.total,"page-sizes":[10,20,50,100],onSizeChange:oe,onCurrentChange:re},null,8,["current-page","page-size","total"])]),a.value||0!==t.value.length?s("",!0):(r(),y("div",R,[p(W,{description:"暂无业绩数据"})])),p(k,{visible:H.value,"onUpdate:visible":h[3]||(h[3]=e=>H.value=e),"statistics-data":L.value,"performance-list":t.value,"selected-departments":K.value,"department-options":q.value,"date-range":S.value,"format-currency":ne,loading:U.value},null,8,["visible","statistics-data","performance-list","selected-departments","department-options","date-range","loading"])])}}},[["__scopeId","data-v-4a8b0511"]]);export{U as default};
