import{a0 as e}from"./index-BDR0Pmj6.js";function t(t){return e({url:"/employee/page",method:"get",params:t})}function a(t){return e({url:`/employee/${t}`,method:"get"})}function r(t){return e({url:"/employee/add",method:"post",data:t})}function o(t){return e({url:"/employee/update",method:"put",data:t})}function u(t){return e({url:`/employee/${t}`,method:"delete"})}function n(t){return e({url:"/employee/page",method:"get",params:{pageNum:1,pageSize:10,name:t}})}export{r as a,a as b,u as d,t as g,n as s,o as u};
