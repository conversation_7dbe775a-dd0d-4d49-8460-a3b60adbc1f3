import{_ as e,a,o as l,c as t,e as n,j as r,i as o,E as i,Y as u,m as s,d,Z as p,f as c,$ as m,g as v,a0 as h,n as g,a1 as f,z as w,t as y}from"./index-CGqeVPF3.js";import{r as b}from"./request-Cm8Ap7dD.js";import{c as x}from"./department-DD9JCT-L.js";import{u as _}from"./token-Ced5ba2J.js";const N={class:"department-expense-container"},z={class:"toolbar"},C={class:"search-box"},k={class:"date-month"},Y={class:"pagination-container"},V={key:0,class:"empty-data"},I=e({__name:"DepartmentExpense",setup(e){_();const I=a(!1),S=a(1),j=a(10),D=a(0),M=a(""),T=a(""),U=a(!1),P=a([]),E=a([]),F=a([]),$={checkStrictly:!0,emitPath:!1,expandTrigger:"hover",multiple:!0},A=a([]),q=a([]),K=e=>e&&e.length?e.map((e=>({value:e.departmentId,label:e.departmentName,children:K(e.children||[])}))):[],L=e=>{const a=[],l=(e,a)=>{if(!e||!e.length)return null;for(const t of e){if(t.value===a)return t;if(t.children&&t.children.length){const e=l(t.children,a);if(e)return e}}return null},t=e=>{if(e.children&&e.children.length)for(const l of e.children)a.push(l.value),t(l)},n=l(P.value,e);return n&&t(n),a},Z=e=>{if(!e||0===e.length)return F.value=[],E.value=[],S.value=1,void B();const a=F.value||[],l=e.filter((e=>!a.includes(e))),t=a.filter((a=>!e.includes(a)));let n=[...e],r=[...e];for(const o of l){const e=L(o);e.length>0&&e.forEach((e=>{n.includes(e)||n.push(e),r.includes(e)||r.push(e)}))}for(const o of t){const e=L(o);e.length>0&&(n=n.filter((a=>!e.includes(a))),r=r.filter((a=>!e.includes(a))))}E.value=r,F.value=n,S.value=1,B()};l((()=>{(async()=>{try{U.value=!0;const e=await x();if(200===e.code)if(P.value=(e.data||[]).map((e=>({value:e.departmentId,label:e.departmentName,children:K(e.children||[])}))),A.value=(e.data||[]).map((e=>e.departmentId)),A.value.length>0){const e=A.value[0],a=L(e);E.value=[e,...a],F.value=[e,...a],await B()}else i.warning("您没有任何可以查看部门开销的部门");else i.error(e.message||"获取部门信息失败")}catch(e){i.error("加载部门信息失败，请稍后再试")}finally{U.value=!1}})()}));const B=async()=>{if(!F.value||0===F.value.length)return i.info("请选择要查询的部门后进行搜索"),q.value=[],D.value=0,void(I.value=!1);I.value=!0;try{const e=Array.isArray(F.value)?F.value:[F.value];let a={pageNum:S.value,pageSize:j.value,departmentIds:e,itemName:M.value||void 0,month:T.value||void 0};const l=await function(e){return b({url:"/department-expense/user-view/page",method:"post",data:{page:e.pageNum,size:e.pageSize,departmentIds:e.departmentIds,itemName:e.itemName,date:e.month}})}(a);200===l.code&&l.data?(q.value=l.data.list||l.data.records||[],D.value=l.data.total||0,0===q.value.length&&0===D.value&&i.info("未查询到符合条件的部门开销数据")):(i.error(l.message||"获取部门开销列表失败"),q.value=[],D.value=0)}catch(e){e.response,i.error("网络错误，获取部门开销列表失败，请稍后重试"),q.value=[],D.value=0}finally{I.value=!1}},G=e=>{if(!e)return"";const a=new Date(e);return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}`},H=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},J=()=>{F.value&&0!==F.value.length?(S.value=1,B()):i.warning("请先选择要查询的部门")},O=()=>{M.value="",T.value="",F.value&&F.value.length>0?(S.value=1,B()):i.warning("请先选择要查询的部门")},Q=e=>{j.value=e,B()},R=e=>{S.value=e,B()};return(e,a)=>{const l=o("el-cascader"),i=o("el-icon"),b=o("el-input"),x=o("el-date-picker"),_=o("el-button"),F=o("el-table-column"),A=o("el-table"),K=o("el-pagination"),L=o("el-empty"),B=o("el-card"),W=u("loading");return s(),t("div",N,[n(B,{class:"department-expense-list-card"},{default:r((()=>[d("div",z,[d("div",C,[n(l,{modelValue:E.value,"onUpdate:modelValue":a[0]||(a[0]=e=>E.value=e),options:P.value,props:$,placeholder:"请选择您负责的部门",clearable:"",loading:U.value,onChange:Z,style:{width:"280px","margin-right":"10px"},"collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":2},null,8,["modelValue","options","loading"]),n(b,{modelValue:M.value,"onUpdate:modelValue":a[1]||(a[1]=e=>M.value=e),placeholder:"搜索项目名称",clearable:"",onKeyup:m(J,["enter"]),onClear:J},{prefix:r((()=>[n(i,null,{default:r((()=>[n(v(h))])),_:1})])),_:1},8,["modelValue"]),n(x,{modelValue:T.value,"onUpdate:modelValue":a[2]||(a[2]=e=>T.value=e),type:"month",placeholder:"选择年月",format:"YYYY-MM","value-format":"YYYY-MM",clearable:"",style:{width:"140px","margin-left":"10px","margin-right":"10px"},onChange:J,onClear:J},null,8,["modelValue"]),n(_,{type:"primary",onClick:J},{default:r((()=>a[5]||(a[5]=[g("搜索")]))),_:1}),n(_,{onClick:O},{default:r((()=>[n(i,null,{default:r((()=>[n(v(f))])),_:1}),a[6]||(a[6]=g("重置 "))])),_:1})])]),p((s(),w(A,{data:q.value,border:"","row-key":"id","max-height":"calc(100vh - 220px)",class:"custom-table","header-cell-style":{background:"#f7f7f7",color:"#606266"}},{default:r((()=>[n(F,{type:"index",width:"60",align:"center",label:"序号","class-name":"index-column"}),n(F,{prop:"departmentName",label:"所属部门","min-width":"150","show-overflow-tooltip":""}),n(F,{prop:"itemName",label:"项目名称","min-width":"200","show-overflow-tooltip":""}),n(F,{prop:"amount",label:"金额","min-width":"120",align:"right"},{default:r((e=>{return[g(y((a=e.row.amount,null==a?"¥0.00":new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:2}).format(a))),1)];var a})),_:1}),n(F,{prop:"expenseDate",label:"年月","min-width":"120","show-overflow-tooltip":"",align:"center"},{default:r((e=>[d("span",k,y(G(e.row.expenseDate)),1)])),_:1}),n(F,{prop:"remark",label:"备注","min-width":"200","show-overflow-tooltip":""}),n(F,{prop:"createTime",label:"创建时间","min-width":"180","show-overflow-tooltip":""},{default:r((e=>[g(y(H(e.row.createTime)),1)])),_:1}),n(F,{prop:"updateTime",label:"更新时间","min-width":"180","show-overflow-tooltip":""},{default:r((e=>[g(y(H(e.row.updateTime)),1)])),_:1})])),_:1},8,["data"])),[[W,I.value]]),d("div",Y,[n(K,{background:"",layout:"total, sizes, prev, pager, next, jumper",currentPage:S.value,"onUpdate:currentPage":a[3]||(a[3]=e=>S.value=e),"page-size":j.value,"onUpdate:pageSize":a[4]||(a[4]=e=>j.value=e),total:D.value,"page-sizes":[10,20,50,100],onSizeChange:Q,onCurrentChange:R},null,8,["currentPage","page-size","total"])]),I.value||0!==q.value.length?c("",!0):(s(),t("div",V,[n(L,{description:"暂无部门开销数据"})]))])),_:1})])}}},[["__scopeId","data-v-e77709bb"]]);export{I as default};
