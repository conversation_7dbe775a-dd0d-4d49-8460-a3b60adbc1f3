import{_ as e,a as l,r as a,x as t,o,E as u,c as r,e as n,j as d,i,Y as s,m as c,d as p,Z as m,$ as v,g as f,a0 as y,F as w,A as g,n as h,a1 as b,a2 as _,z as V,t as k,f as U,a3 as C,a4 as x,a5 as S}from"./index-CGqeVPF3.js";import{f as P,g as z,a as j,u as N,b as I}from"./client-0FvGXzol.js";import{u as q}from"./token-Ced5ba2J.js";import"./request-Cm8Ap7dD.js";const R={class:"client-container"},T={class:"toolbar"},F={class:"search-box"},$={class:"operation-buttons"},A={class:"pagination-container"},D={class:"dialog-footer"},E={class:"dialog-footer"},K={class:"approval-confirm-content"},L={class:"dialog-footer"},O={class:"approval-confirm-content"},Y={class:"dialog-footer"},Z=e({__name:"Client",setup(e){const Z=l(!1),B=l(1),G=l(10),H=l(0),J=l(""),M=l(""),Q=l(""),W=l(""),X=l([]),ee=l(!1),le=l(null),ae=l(!1),te=l([{value:"国内-同行",label:"国内-同行"},{value:"国外-同行",label:"国外-同行"},{value:"国内-直客",label:"国内-直客"},{value:"国外-直客",label:"国外-直客"}]),oe=a({name:"",email:"",phone:"",contactPerson:"",nationality:"",category:"海运",clientStatus:"报价中",remark:""}),ue={name:[{required:!0,message:"请输入客户名称",trigger:"blur"},{min:2,max:50,message:"客户名称长度必须在2-50个字符之间",trigger:"blur"}],email:[{required:!0,message:"请输入客户邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"},{max:100,message:"邮箱长度不能超过100个字符",trigger:"blur"}],category:[{required:!0,message:"请选择客户分类",trigger:"change"}]},re=l(!1),ne=l(null),de=l(!1),ie=a({clientId:null,name:"",email:"",phone:"",contactPerson:"",nationality:"",category:"",clientStatus:"",remark:""}),se=l(!1),ce=l(!1),pe=l(null),me=l(!1),ve=l(!1),fe=l(null),ye=q(),we=t((()=>{var e;return(null==(e=ye.user)?void 0:e.name)||""}));o((()=>{ge()}));const ge=async()=>{Z.value=!0;try{const e=await P({pageNum:B.value,pageSize:G.value,name:J.value||void 0,category:M.value||void 0,status:Q.value||void 0,clientStatus:W.value||void 0});200===e.code?(X.value=e.data.list||[],H.value=e.data.total||0):u.error(e.message||"获取客户列表失败")}catch(e){u.error("网络错误，请稍后重试")}finally{Z.value=!1}},he=e=>{switch(e){case"海运":return"primary";case"空运":return"success";case"散货":return"warning";case"快递":return"info";default:return""}},be=e=>{switch(e){case"未审核":return"info";case"审核中":return"warning";case"审核通过":return"success";case"已拒绝":return"danger";default:return""}},_e=e=>{switch(e){case"报价中":return"warning";case"已合作":return"success";default:return"info"}},Ve=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},ke=()=>{B.value=1,ge()},Ue=()=>{J.value="",M.value="",Q.value="",W.value="",B.value=1,ge()},Ce=e=>{G.value=e,ge()},xe=e=>{B.value=e,ge()},Se=()=>{le.value?le.value.resetFields():(oe.name="",oe.email="",oe.phone="",oe.contactPerson="",oe.nationality="",oe.category="海运",oe.clientStatus="报价中",oe.remark=""),ee.value=!0},Pe=()=>{ee.value=!1},ze=async()=>{le.value&&await le.value.validate((async e=>{if(e){ae.value=!0;try{const e=await j({...oe});200===e.code?(u.success("客户添加成功"),ee.value=!1,ge()):400===e.code?(u({type:"warning",message:e.message,duration:5e3}),e.message&&e.message.includes("客户名称")&&e.message.includes("已存在")&&le.value&&setTimeout((()=>{const e=le.value.$el.querySelector('input[placeholder="请输入客户名称"]');e&&(e.focus(),e.select())}),100)):u.error(e.message||"客户添加失败")}catch(l){u.error("网络错误，请稍后重试")}finally{ae.value=!1}}else u.warning("请正确填写表单信息")}))},je=()=>{re.value=!1},Ne=async()=>{ne.value&&await ne.value.validate((async e=>{if(e){de.value=!0;try{const e=await N({...ie});200===e.code?(u.success("客户信息更新成功"),re.value=!1,ge()):u.error(e.message||"客户信息更新失败")}catch(l){u.error("网络错误，请稍后重试")}finally{de.value=!1}}else u.warning("请正确填写表单信息")}))},Ie=e=>{pe.value=e,se.value=!0},qe=async()=>{if(fe.value){ve.value=!0;try{const e=await I(fe.value.clientId,"未审核");200===e.code?(u.success("客户审核已取消"),me.value=!1,ge()):u.error(e.message||"取消审核失败")}catch(e){u.error("网络错误，请稍后重试")}finally{ve.value=!1}}},Re=async()=>{if(pe.value){ce.value=!0;try{const e=await I(pe.value.clientId,"审核中");200===e.code?(u.success("客户已成功提交审核"),se.value=!1,ge()):u.error(e.message||"提交审核失败")}catch(e){u.error("网络错误，请稍后重试")}finally{ce.value=!1}}};return(e,l)=>{const a=i("el-icon"),t=i("el-input"),o=i("el-option"),P=i("el-select"),j=i("el-button"),N=i("el-table-column"),I=i("el-tag"),q=i("el-tooltip"),ye=i("el-table"),ge=i("el-pagination"),Te=i("el-card"),Fe=i("el-form-item"),$e=i("el-form"),Ae=i("el-dialog"),De=s("loading");return c(),r("div",R,[n(Te,{class:"client-list-card"},{default:d((()=>[p("div",T,[p("div",F,[n(t,{modelValue:J.value,"onUpdate:modelValue":l[0]||(l[0]=e=>J.value=e),placeholder:"搜索客户名称",clearable:"",onKeyup:v(ke,["enter"]),onClear:ke},{prefix:d((()=>[n(a,null,{default:d((()=>[n(f(y))])),_:1})])),_:1},8,["modelValue"]),n(P,{modelValue:M.value,"onUpdate:modelValue":l[1]||(l[1]=e=>M.value=e),placeholder:"选择客户分类",clearable:"",style:{width:"140px"}},{default:d((()=>[(c(),r(w,null,g(["海运","空运","散货","快递"],(e=>n(o,{key:e,label:e,value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),n(P,{modelValue:Q.value,"onUpdate:modelValue":l[2]||(l[2]=e=>Q.value=e),placeholder:"选择审批状态",clearable:"",style:{width:"140px"}},{default:d((()=>[(c(),r(w,null,g(["未审核","审核中","审核通过","已拒绝"],(e=>n(o,{key:e,label:e,value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),n(P,{modelValue:W.value,"onUpdate:modelValue":l[3]||(l[3]=e=>W.value=e),placeholder:"选择客户状态",clearable:"",style:{width:"140px"}},{default:d((()=>[(c(),r(w,null,g(["报价中","已合作"],(e=>n(o,{key:e,label:e,value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),n(j,{type:"primary",onClick:ke},{default:d((()=>l[28]||(l[28]=[h("搜索")]))),_:1}),n(j,{onClick:Ue},{default:d((()=>[n(a,null,{default:d((()=>[n(f(b))])),_:1}),l[29]||(l[29]=h("重置 "))])),_:1})]),n(j,{type:"success",onClick:Se,class:"add-btn"},{default:d((()=>[n(a,null,{default:d((()=>[n(f(_))])),_:1}),l[30]||(l[30]=h(" 添加新客户 "))])),_:1})]),m((c(),V(ye,{data:X.value,border:"","row-key":"clientId","max-height":"calc(100vh - 220px)",class:"custom-table","header-cell-style":{background:"#f7f7f7",color:"#606266"}},{default:d((()=>[n(N,{type:"index",width:"60",align:"center",label:"序号","class-name":"index-column"}),n(N,{prop:"name",label:"客户名称","min-width":"120","show-overflow-tooltip":""}),n(N,{prop:"contactPerson",label:"联系人","min-width":"100","show-overflow-tooltip":""},{default:d((e=>[h(k(e.row.contactPerson||"----"),1)])),_:1}),n(N,{prop:"employeeName",label:"负责员工","min-width":"100","show-overflow-tooltip":""},{default:d((e=>[h(k(e.row.employeeName||"----"),1)])),_:1}),n(N,{prop:"email",label:"邮箱","min-width":"150","show-overflow-tooltip":""}),n(N,{prop:"phone",label:"电话","min-width":"130","show-overflow-tooltip":""}),n(N,{prop:"nationality",label:"国籍/地区","min-width":"100","show-overflow-tooltip":""},{default:d((e=>[h(k(e.row.nationality||"----"),1)])),_:1}),n(N,{prop:"category",label:"客户分类","min-width":"100","show-overflow-tooltip":""},{default:d((e=>[n(I,{type:he(e.row.category),effect:"plain"},{default:d((()=>[h(k(e.row.category||"待选择"),1)])),_:2},1032,["type"])])),_:1}),n(N,{prop:"clientStatus",label:"客户状态","min-width":"100","show-overflow-tooltip":""},{default:d((e=>[n(I,{type:_e(e.row.clientStatus),effect:"plain"},{default:d((()=>[h(k(e.row.clientStatus||"报价中"),1)])),_:2},1032,["type"])])),_:1}),n(N,{prop:"status",label:"审批状态","min-width":"100","show-overflow-tooltip":""},{default:d((e=>[n(q,{class:"item",effect:"dark",content:e.row.rejectRemark,placement:"top",disabled:!("已拒绝"===e.row.status&&e.row.rejectRemark&&""!==e.row.rejectRemark.trim())},{default:d((()=>[n(I,{type:be(e.row.status),effect:"light"},{default:d((()=>{return[h(k((l=e.row.status,"已合作"===l?"审核通过":l||"----")),1)];var l})),_:2},1032,["type"])])),_:2},1032,["content","disabled"])])),_:1}),n(N,{prop:"remark",label:"备注","min-width":"150","show-overflow-tooltip":""},{default:d((e=>[h(k(e.row.remark||"----"),1)])),_:1}),n(N,{prop:"operationTime",label:"操单时间","min-width":"180","show-overflow-tooltip":""},{default:d((e=>[h(k(Ve(e.row.operationTime)||"----"),1)])),_:1}),n(N,{prop:"createTime",label:"创建时间","min-width":"180","show-overflow-tooltip":""},{default:d((e=>[h(k(Ve(e.row.createTime)),1)])),_:1}),n(N,{label:"操作",fixed:"right","min-width":"220",align:"center"},{default:d((e=>[p("div",$,["未审核"===e.row.status&&e.row.employeeName===we.value?(c(),V(j,{key:0,type:"primary",size:"small",onClick:l=>(async e=>{try{const l=await z(e.clientId);200===l.code?(Object.assign(ie,l.data),ie.contactPerson=l.data.contactPerson||"",ie.clientStatus=l.data.clientStatus||"报价中",ie.remark=l.data.remark||"",re.value=!0):u.error(l.message||"获取客户信息失败")}catch(l){u.error("网络错误，请稍后重试")}})(e.row),title:"编辑客户"},{default:d((()=>[n(a,null,{default:d((()=>[n(f(C))])),_:1}),l[31]||(l[31]=h("编辑 "))])),_:2},1032,["onClick"])):U("",!0),"未审核"===e.row.status&&e.row.employeeName===we.value?(c(),V(j,{key:1,type:"success",size:"small",onClick:l=>Ie(e.row),title:"提交新客户审核"},{default:d((()=>[n(a,null,{default:d((()=>[n(f(x))])),_:1}),l[32]||(l[32]=h("提交新客户审核 "))])),_:2},1032,["onClick"])):U("",!0),"审核中"===e.row.status&&e.row.employeeName===we.value?(c(),V(j,{key:2,type:"warning",size:"small",onClick:l=>{return a=e.row,fe.value=a,void(me.value=!0);var a},title:"取消审核"},{default:d((()=>[n(a,null,{default:d((()=>[n(f(S))])),_:1}),l[33]||(l[33]=h("取消审核 "))])),_:2},1032,["onClick"])):U("",!0),"已拒绝"===e.row.status&&e.row.employeeName===we.value?(c(),V(j,{key:3,type:"success",size:"small",onClick:l=>Ie(e.row),title:"重新提交审核"},{default:d((()=>[n(a,null,{default:d((()=>[n(f(x))])),_:1}),l[34]||(l[34]=h("重新提交审核 "))])),_:2},1032,["onClick"])):U("",!0)])])),_:1})])),_:1},8,["data"])),[[De,Z.value]]),p("div",A,[n(ge,{background:"",layout:"total, sizes, prev, pager, next, jumper",currentPage:B.value,"onUpdate:currentPage":l[4]||(l[4]=e=>B.value=e),"page-size":G.value,"onUpdate:pageSize":l[5]||(l[5]=e=>G.value=e),total:H.value,"page-sizes":[10,20,50,100],onSizeChange:Ce,onCurrentChange:xe},null,8,["currentPage","page-size","total"])])])),_:1}),n(Ae,{modelValue:ee.value,"onUpdate:modelValue":l[14]||(l[14]=e=>ee.value=e),title:"添加新客户",width:"500px","destroy-on-close":""},{footer:d((()=>[p("div",D,[n(j,{onClick:Pe},{default:d((()=>l[35]||(l[35]=[h("取消")]))),_:1}),n(j,{type:"primary",onClick:ze,loading:ae.value},{default:d((()=>l[36]||(l[36]=[h("确认添加")]))),_:1},8,["loading"])])])),default:d((()=>[n($e,{ref_key:"addFormRef",ref:le,model:oe,rules:ue,"label-width":"100px","label-position":"right","status-icon":""},{default:d((()=>[n(Fe,{label:"客户名称",prop:"name"},{default:d((()=>[n(t,{modelValue:oe.name,"onUpdate:modelValue":l[6]||(l[6]=e=>oe.name=e),placeholder:"请输入客户名称"},null,8,["modelValue"])])),_:1}),n(Fe,{label:"联系人",prop:"contactPerson"},{default:d((()=>[n(t,{modelValue:oe.contactPerson,"onUpdate:modelValue":l[7]||(l[7]=e=>oe.contactPerson=e),placeholder:"请输入联系人姓名"},null,8,["modelValue"])])),_:1}),n(Fe,{label:"客户邮箱",prop:"email"},{default:d((()=>[n(t,{modelValue:oe.email,"onUpdate:modelValue":l[8]||(l[8]=e=>oe.email=e),placeholder:"请输入客户邮箱"},null,8,["modelValue"])])),_:1}),n(Fe,{label:"联系电话",prop:"phone"},{default:d((()=>[n(t,{modelValue:oe.phone,"onUpdate:modelValue":l[9]||(l[9]=e=>oe.phone=e),placeholder:"请输入客户联系电话"},null,8,["modelValue"])])),_:1}),n(Fe,{label:"国籍/地区",prop:"nationality"},{default:d((()=>[n(P,{modelValue:oe.nationality,"onUpdate:modelValue":l[10]||(l[10]=e=>oe.nationality=e),placeholder:"请选择国籍/地区",style:{width:"100%"},clearable:""},{default:d((()=>[(c(!0),r(w,null,g(te.value,(e=>(c(),V(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(Fe,{label:"客户分类",prop:"category"},{default:d((()=>[n(P,{modelValue:oe.category,"onUpdate:modelValue":l[11]||(l[11]=e=>oe.category=e),placeholder:"请选择客户分类",style:{width:"100%"}},{default:d((()=>[n(o,{label:"海运",value:"海运"}),n(o,{label:"空运",value:"空运"}),n(o,{label:"散货",value:"散货"}),n(o,{label:"快递",value:"快递"})])),_:1},8,["modelValue"])])),_:1}),n(Fe,{label:"客户状态",prop:"clientStatus"},{default:d((()=>[n(P,{modelValue:oe.clientStatus,"onUpdate:modelValue":l[12]||(l[12]=e=>oe.clientStatus=e),placeholder:"请选择客户状态",style:{width:"100%"}},{default:d((()=>[n(o,{label:"报价中",value:"报价中"}),n(o,{label:"已合作",value:"已合作"})])),_:1},8,["modelValue"])])),_:1}),n(Fe,{label:"备注",prop:"remark"},{default:d((()=>[n(t,{modelValue:oe.remark,"onUpdate:modelValue":l[13]||(l[13]=e=>oe.remark=e),type:"textarea",placeholder:"请输入备注信息",rows:3},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),n(Ae,{modelValue:re.value,"onUpdate:modelValue":l[23]||(l[23]=e=>re.value=e),title:"编辑客户信息",width:"500px","destroy-on-close":""},{footer:d((()=>[p("div",E,[n(j,{onClick:je},{default:d((()=>l[38]||(l[38]=[h("取消")]))),_:1}),n(j,{type:"primary",onClick:Ne,loading:de.value},{default:d((()=>l[39]||(l[39]=[h("保存修改")]))),_:1},8,["loading"])])])),default:d((()=>[n($e,{ref_key:"editFormRef",ref:ne,model:ie,rules:ue,"label-width":"100px","label-position":"right","status-icon":""},{default:d((()=>[n(Fe,{label:"客户名称",prop:"name"},{default:d((()=>[n(t,{modelValue:ie.name,"onUpdate:modelValue":l[15]||(l[15]=e=>ie.name=e),placeholder:"请输入客户名称",disabled:""},null,8,["modelValue"]),l[37]||(l[37]=p("div",{class:"form-tip"},"客户名称不可修改",-1))])),_:1}),n(Fe,{label:"联系人",prop:"contactPerson"},{default:d((()=>[n(t,{modelValue:ie.contactPerson,"onUpdate:modelValue":l[16]||(l[16]=e=>ie.contactPerson=e),placeholder:"请输入联系人姓名"},null,8,["modelValue"])])),_:1}),n(Fe,{label:"客户邮箱",prop:"email"},{default:d((()=>[n(t,{modelValue:ie.email,"onUpdate:modelValue":l[17]||(l[17]=e=>ie.email=e),placeholder:"请输入客户邮箱"},null,8,["modelValue"])])),_:1}),n(Fe,{label:"联系电话",prop:"phone"},{default:d((()=>[n(t,{modelValue:ie.phone,"onUpdate:modelValue":l[18]||(l[18]=e=>ie.phone=e),placeholder:"请输入客户联系电话"},null,8,["modelValue"])])),_:1}),n(Fe,{label:"国籍/地区",prop:"nationality"},{default:d((()=>[n(P,{modelValue:ie.nationality,"onUpdate:modelValue":l[19]||(l[19]=e=>ie.nationality=e),placeholder:"请选择国籍/地区",style:{width:"100%"},clearable:""},{default:d((()=>[(c(!0),r(w,null,g(te.value,(e=>(c(),V(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(Fe,{label:"客户分类",prop:"category"},{default:d((()=>[n(P,{modelValue:ie.category,"onUpdate:modelValue":l[20]||(l[20]=e=>ie.category=e),placeholder:"请选择客户分类",style:{width:"100%"}},{default:d((()=>[n(o,{label:"海运",value:"海运"}),n(o,{label:"空运",value:"空运"}),n(o,{label:"散货",value:"散货"}),n(o,{label:"快递",value:"快递"})])),_:1},8,["modelValue"])])),_:1}),n(Fe,{label:"客户状态",prop:"clientStatus"},{default:d((()=>[n(P,{modelValue:ie.clientStatus,"onUpdate:modelValue":l[21]||(l[21]=e=>ie.clientStatus=e),placeholder:"请选择客户状态",style:{width:"100%"}},{default:d((()=>[n(o,{label:"报价中",value:"报价中"}),n(o,{label:"已合作",value:"已合作"})])),_:1},8,["modelValue"])])),_:1}),n(Fe,{label:"备注",prop:"remark"},{default:d((()=>[n(t,{modelValue:ie.remark,"onUpdate:modelValue":l[22]||(l[22]=e=>ie.remark=e),type:"textarea",placeholder:"请输入备注信息",rows:3},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),n(Ae,{modelValue:se.value,"onUpdate:modelValue":l[25]||(l[25]=e=>se.value=e),title:"申请审批确认",width:"400px"},{footer:d((()=>[p("div",L,[n(j,{onClick:l[24]||(l[24]=e=>se.value=!1)},{default:d((()=>l[45]||(l[45]=[h("取消")]))),_:1}),n(j,{type:"success",onClick:Re,loading:ce.value},{default:d((()=>l[46]||(l[46]=[h("确认提交")]))),_:1},8,["loading"])])])),default:d((()=>{var e;return[p("div",K,[p("p",null,[l[40]||(l[40]=h("确定要将客户 ")),p("strong",null,k(null==(e=pe.value)?void 0:e.name),1),l[41]||(l[41]=h(" 提交审核吗？"))]),p("p",null,[l[43]||(l[43]=h("提交后客户状态将变更为 ")),n(I,{type:"warning"},{default:d((()=>l[42]||(l[42]=[h("审核中")]))),_:1}),l[44]||(l[44]=h("，等待管理员审核。"))])])]})),_:1},8,["modelValue"]),n(Ae,{modelValue:me.value,"onUpdate:modelValue":l[27]||(l[27]=e=>me.value=e),title:"取消审批确认",width:"400px","append-to-body":"","destroy-on-close":""},{footer:d((()=>[p("div",Y,[n(j,{onClick:l[26]||(l[26]=e=>me.value=!1)},{default:d((()=>l[52]||(l[52]=[h("返回")]))),_:1}),n(j,{type:"warning",onClick:qe,loading:ve.value},{default:d((()=>l[53]||(l[53]=[h("确认取消")]))),_:1},8,["loading"])])])),default:d((()=>{var e;return[p("div",O,[p("p",null,[l[47]||(l[47]=h("确定要取消客户 ")),p("strong",null,k(null==(e=fe.value)?void 0:e.name),1),l[48]||(l[48]=h(" 的审核申请吗？"))]),p("p",null,[l[50]||(l[50]=h("取消后客户状态将变更为 ")),n(I,{type:be("未审核"),effect:"light"},{default:d((()=>l[49]||(l[49]=[h("未审核")]))),_:1},8,["type"]),l[51]||(l[51]=h("。"))])])]})),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-4ee3a728"]]);export{Z as default};
