# 客户统计逻辑修改记录

## 修改时间
2025-06-06 11:35:50 +08:00

## 修改原因
用户需求：调整"距上次新客户天数"的计算逻辑，将审核状态条件从仅"审核通过"扩展为"审核中或审核通过"。

## 修改内容

### 1. 新客户统计条件调整

**修改前的条件：**
- 审核状态：仅 `'审核通过'`
- 客户状态：`'已合作'`
- 时间基准：`create_time`（创建时间）

**修改后的条件：**
- 审核状态：`'审核中'` 或 `'审核通过'`
- 客户状态：`'已合作'`
- 时间基准：`create_time`（创建时间）

### 2. 修改的文件

#### 2.1 SQL映射文件
**文件**: `src/main/resources/mapper/ClientMapper.xml`

**修改的查询：**

1. **年度新客户统计** (`countNewClientsByEmployeeAndYear`)
```sql
-- 修改前
WHERE employee_id = #{employeeId}
  AND status = '审核通过'
  AND client_status = '已合作'
  AND YEAR(create_time) = #{year}

-- 修改后  
WHERE employee_id = #{employeeId}
  AND status IN ('审核中', '审核通过')
  AND client_status = '已合作'
  AND YEAR(create_time) = #{year}
```

2. **月度新客户统计** (`countNewClientsByEmployeeAndMonth`)
```sql
-- 修改前
WHERE employee_id = #{employeeId}
  AND status = '审核通过'
  AND client_status = '已合作'
  AND YEAR(create_time) = #{year}
  AND MONTH(create_time) = #{month}

-- 修改后
WHERE employee_id = #{employeeId}
  AND status IN ('审核中', '审核通过')
  AND client_status = '已合作'
  AND YEAR(create_time) = #{year}
  AND MONTH(create_time) = #{month}
```

3. **距上次新客户天数** (`calculateDaysSinceLastNewClient`)
```sql
-- 修改前
WHERE employee_id = #{employeeId}
  AND status = '审核通过'
  AND client_status = '已合作'

-- 修改后
WHERE employee_id = #{employeeId}
  AND status IN ('审核中', '审核通过')
  AND client_status = '已合作'
```

#### 2.2 Service实现类
**文件**: `src/main/java/org/example/company_management/service/impl/ClientStatisticsServiceImpl.java`

**修改内容：**
- 更新了代码注释，说明新的查询条件
- 更新了修改记录注释

## 业务影响分析

### 3.1 统计数据变化
修改后，以下客户会被计入新客户统计：
- ✅ 审核状态为"审核中" + 客户状态为"已合作"
- ✅ 审核状态为"审核通过" + 客户状态为"已合作"

不会被计入的客户：
- ❌ 审核状态为"未审核"
- ❌ 审核状态为"已拒绝"
- ❌ 客户状态为"报价中"（无论审核状态如何）

### 3.2 业务逻辑合理性
这个修改符合业务实际情况：
1. **审核中的客户**：虽然还在审核过程中，但如果已经开始合作，应该被计为新客户
2. **已合作状态**：确保只有真正开始业务合作的客户才被统计
3. **创建时间基准**：以客户首次录入系统的时间为准，符合"新客户"的定义

### 3.3 数据一致性
所有三个统计指标（年度、月度、距上次新客户天数）都使用相同的条件，确保数据的一致性和可比性。

## 测试建议

### 4.1 功能测试
1. 创建测试客户，状态为"审核中" + "已合作"
2. 验证该客户是否被计入统计
3. 检查年度、月度、距上次新客户天数三个指标的一致性

### 4.2 边界测试
1. 测试"审核中" + "报价中"的客户（应该不被计入）
2. 测试"已拒绝" + "已合作"的客户（应该不被计入）
3. 测试状态变更对统计数据的影响

### 4.3 性能测试
由于查询条件从单一状态改为IN条件，建议验证查询性能是否受影响。

## 缓存影响

### 5.1 缓存失效
修改后，现有的缓存数据可能不准确，建议：
1. 清理相关缓存：`yearlyNewClients`、`monthlyNewClients`、`daysSinceLastNewClient`
2. 或者等待缓存自然过期

### 5.2 缓存策略
当前缓存策略仍然有效：
- 年度统计：按员工ID和年份缓存
- 月度统计：按员工ID、年份和月份缓存  
- 距上次新客户天数：按员工ID缓存

## 部署注意事项

1. **数据库更新**：无需修改数据库结构，仅SQL查询逻辑变更
2. **应用重启**：建议重启应用以清理缓存
3. **数据验证**：部署后验证统计数据的准确性

## 回滚方案

如需回滚，将SQL查询中的：
```sql
AND status IN ('审核中', '审核通过')
```
改回：
```sql
AND status = '审核通过'
```

## 总结

本次修改扩展了新客户的统计范围，使其更符合业务实际情况。修改影响范围有限，主要是SQL查询逻辑的调整，风险较低。建议在测试环境充分验证后再部署到生产环境。
