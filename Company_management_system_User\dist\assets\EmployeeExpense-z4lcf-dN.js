import{_ as e,a,o as l,c as t,e as o,j as n,i as r,E as u,Y as i,m as d,d as s,Z as p,f as c,$ as v,g as m,a0 as h,n as g,a1 as f,z as w,t as y}from"./index-CGqeVPF3.js";import{r as x}from"./request-Cm8Ap7dD.js";import{c as b}from"./department-DD9JCT-L.js";const _={class:"employee-expense-container"},C={class:"toolbar"},V={class:"search-box"},N={class:"date-month"},Y={class:"pagination-container"},k={key:0,class:"empty-data"},z=e({__name:"EmployeeExpense",setup(e){const z=a(!1),I=a(1),S=a(10),U=a(0),j=a(""),D=a(""),M=a(""),T=a(!1),E=a([]),P=a([]),F=a([]),$={checkStrictly:!0,emitPath:!1,expandTrigger:"hover",multiple:!0},A=a([]),K=a([]),q=e=>e&&e.length?e.map((e=>({value:e.departmentId,label:e.departmentName,children:q(e.children||[])}))):[],B=e=>{const a=[],l=(e,a)=>{if(!e||!e.length)return null;for(const t of e){if(t.value===a)return t;if(t.children&&t.children.length){const e=l(t.children,a);if(e)return e}}return null},t=e=>{if(e.children&&e.children.length)for(const l of e.children)a.push(l.value),t(l)},o=l(E.value,e);return o&&t(o),a},L=e=>{if(!e||0===e.length)return F.value=[],P.value=[],I.value=1,void Z();const a=F.value||[],l=e.filter((e=>!a.includes(e))),t=a.filter((a=>!e.includes(a)));let o=[...e],n=[...e];for(const r of l){const e=B(r);e.length>0&&e.forEach((e=>{o.includes(e)||o.push(e),n.includes(e)||n.push(e)}))}for(const r of t){const e=B(r);e.length>0&&(o=o.filter((a=>!e.includes(a))),n=n.filter((a=>!e.includes(a))))}P.value=n,F.value=o,I.value=1,Z()};l((()=>{(async()=>{try{T.value=!0;const e=await b();if(200===e.code)if(E.value=(e.data||[]).map((e=>({value:e.departmentId,label:e.departmentName,children:q(e.children||[])}))),A.value=(e.data||[]).map((e=>e.departmentId)),A.value.length>0){const e=A.value[0],a=B(e);P.value=[e,...a],F.value=[e,...a],await Z()}else u.warning("您没有任何可以查看员工费用的部门");else u.error(e.message||"获取部门信息失败")}catch(e){u.error("加载部门列表失败，请稍后再试")}finally{T.value=!1}})()}));const Z=async()=>{if(!F.value||0===F.value.length)return u.info("请选择要查询的部门后进行搜索"),K.value=[],U.value=0,void(z.value=!1);z.value=!0;try{const e=Array.isArray(F.value)?F.value:[F.value].filter(Boolean);let a={page:I.value,size:S.value,departmentIds:e,employeeName:j.value||void 0,itemName:D.value||void 0,date:M.value||void 0};const l=await function(e){return x({url:"/employee-expense/user-view/page",method:"post",data:e})}(a);200===l.code&&l.data?(K.value=l.data.list||l.data.records||[],U.value=l.data.total||0,0===K.value.length&&0===U.value&&u.info("未查询到符合条件的员工费用数据")):(u.error(l.message||"获取员工费用列表失败"),K.value=[],U.value=0)}catch(e){u.error("网络错误，获取员工费用列表失败，请稍后重试"),K.value=[],U.value=0}finally{z.value=!1}},G=e=>{if(!e)return"";const a=new Date(e);return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}`},H=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},J=()=>{F.value&&0!==F.value.length?(I.value=1,Z()):u.warning("请先选择要查询的部门")},O=()=>{j.value="",D.value="",M.value="",F.value&&F.value.length>0?(I.value=1,Z()):u.warning("请先选择要查询的部门")},Q=e=>{S.value=e,Z()},R=e=>{I.value=e,Z()};return(e,a)=>{const l=r("el-cascader"),u=r("el-icon"),x=r("el-input"),b=r("el-date-picker"),F=r("el-button"),A=r("el-table-column"),q=r("el-table"),B=r("el-pagination"),Z=r("el-empty"),W=r("el-card"),X=i("loading");return d(),t("div",_,[o(W,{class:"employee-expense-list-card"},{default:n((()=>[s("div",C,[s("div",V,[o(l,{modelValue:P.value,"onUpdate:modelValue":a[0]||(a[0]=e=>P.value=e),options:E.value,props:$,placeholder:"请选择您负责的部门",clearable:"",loading:T.value,onChange:L,style:{width:"280px","margin-right":"10px"},"collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":2},null,8,["modelValue","options","loading"]),o(x,{modelValue:j.value,"onUpdate:modelValue":a[1]||(a[1]=e=>j.value=e),placeholder:"搜索员工姓名",clearable:"",onKeyup:v(J,["enter"]),onClear:J,style:{width:"180px"}},{prefix:n((()=>[o(u,null,{default:n((()=>[o(m(h))])),_:1})])),_:1},8,["modelValue"]),o(x,{modelValue:D.value,"onUpdate:modelValue":a[2]||(a[2]=e=>D.value=e),placeholder:"搜索项目名称",clearable:"",onKeyup:v(J,["enter"]),onClear:J,style:{width:"180px","margin-left":"10px"}},{prefix:n((()=>[o(u,null,{default:n((()=>[o(m(h))])),_:1})])),_:1},8,["modelValue"]),o(b,{modelValue:M.value,"onUpdate:modelValue":a[3]||(a[3]=e=>M.value=e),type:"month",placeholder:"选择年月",format:"YYYY-MM","value-format":"YYYY-MM",clearable:"",style:{width:"140px","margin-left":"10px","margin-right":"10px"},onChange:J,onClear:J},null,8,["modelValue"]),o(F,{type:"primary",onClick:J},{default:n((()=>a[6]||(a[6]=[g("搜索")]))),_:1}),o(F,{onClick:O},{default:n((()=>[o(u,null,{default:n((()=>[o(m(f))])),_:1}),a[7]||(a[7]=g("重置 "))])),_:1})])]),p((d(),w(q,{data:K.value,border:"","row-key":"id","max-height":"calc(100vh - 220px)",class:"custom-table","header-cell-style":{background:"#f7f7f7",color:"#606266"}},{default:n((()=>[o(A,{type:"index",width:"60",align:"center",label:"序号","class-name":"index-column"}),o(A,{prop:"employeeName",label:"员工姓名","min-width":"120","show-overflow-tooltip":""}),o(A,{prop:"departmentName",label:"部门","min-width":"150","show-overflow-tooltip":""}),o(A,{prop:"itemName",label:"项目名称","min-width":"200","show-overflow-tooltip":""}),o(A,{prop:"amount",label:"金额","min-width":"120",align:"right"},{default:n((e=>{return[g(y((a=e.row.amount,null==a?"¥0.00":new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:2}).format(a))),1)];var a})),_:1}),o(A,{prop:"expenseDate",label:"年月","min-width":"120","show-overflow-tooltip":"",align:"center"},{default:n((e=>[s("span",N,y(G(e.row.expenseDate)),1)])),_:1}),o(A,{prop:"remark",label:"备注","min-width":"200","show-overflow-tooltip":""}),o(A,{prop:"createTime",label:"创建时间","min-width":"180","show-overflow-tooltip":""},{default:n((e=>[g(y(H(e.row.createTime)),1)])),_:1}),o(A,{prop:"updateTime",label:"更新时间","min-width":"180","show-overflow-tooltip":""},{default:n((e=>[g(y(H(e.row.updateTime)),1)])),_:1})])),_:1},8,["data"])),[[X,z.value]]),s("div",Y,[o(B,{background:"",layout:"total, sizes, prev, pager, next, jumper",currentPage:I.value,"onUpdate:currentPage":a[4]||(a[4]=e=>I.value=e),"page-size":S.value,"onUpdate:pageSize":a[5]||(a[5]=e=>S.value=e),total:U.value,"page-sizes":[10,20,50,100],onSizeChange:Q,onCurrentChange:R},null,8,["currentPage","page-size","total"])]),z.value||0!==K.value.length?c("",!0):(d(),t("div",k,[o(Z,{description:"暂无员工费用数据"})]))])),_:1})])}}},[["__scopeId","data-v-fd00d604"]]);export{z as default};
