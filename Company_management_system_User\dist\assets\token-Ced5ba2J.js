import{q as e,a as t,x as o}from"./index-CGqeVPF3.js";const a=e("auth",(()=>{const e=t(localStorage.getItem("token")||""),a=t(JSON.parse(localStorage.getItem("user")||"{}")),r=t(!1),n=t(null),l=o((()=>!!e.value));return{token:e,user:a,loading:r,error:n,isAuthenticated:l,setToken:function(t){e.value=t,localStorage.setItem("token",t)},setUser:function(e){a.value=e,localStorage.setItem("user",JSON.stringify(e))},setLoading:function(e){r.value=e},setError:function(e){n.value=e},clearAuth:function(){e.value="",a.value={},localStorage.removeItem("token"),localStorage.removeItem("user")}}}));export{a as u};
