import{a0 as e,_ as a,a as l,r as t,D as r,o,c as u,e as n,R as d,f as i,K as s,g as p,k as m,S as c,j as g,T as y,a3 as v,E as f,m as b,h,U as w,I as _,J as V,n as S,V as I,Z as B,W as q,a4 as x,t as k,Y as F,A as U,O as A}from"./index-BDR0Pmj6.js";import{b as N}from"./department-jpUZgat6.js";import{g as D}from"./employee-DbFXp2d5.js";import{I as C}from"./ImportErrorsDialog-DbpGOqA7.js";const E={class:"salary-container"},Y={class:"toolbar"},$={class:"search-box"},O={class:"action-box"},z={class:"total-salary"},M={class:"operation-buttons"},P={class:"pagination-container"},j={class:"empty-text"},T={key:0,class:"el-form-item__help"},R={class:"dialog-footer"},K=a({__name:"Salary",setup(a){const K=l([]),J=l(!0),W=l(""),Z=l(""),G=l(""),H=l([]),L=l([]),Q=l(!1),X=l(!1),ee=l(""),ae=l([]),le=t({page:1,size:10,total:0}),te=l(!1),re=l("add"),oe=l(null),ue=l(!1),ne=t({id:null,employeeId:null,employeeName:"",departmentId:null,date:"",basicSalary:0,performanceBonus:0,fullAttendanceBonus:0,businessOperationBonus:0,reimbursement:0,privateAccount:0,leaveDeduction:0,deduction:0,lateDeduction:0,socialSecurityPersonal:0,providentFund:0,tax:0,waterElectricityFee:0,sumSalary:0,actualSalary:0,totalSalary:0,remark:""}),de={departmentId:[{required:!0,message:"请选择部门",trigger:"change"}],employeeId:[{required:!0,message:"请选择员工",trigger:"change"}],date:[{required:!0,message:"请选择年月",trigger:"change"}],basicSalary:[{required:!0,message:"请输入基本工资",trigger:"blur"},{type:"number",message:"基本工资必须为数字",trigger:"blur"}],performanceBonus:[{required:!0,message:"请输入绩效奖金",trigger:"blur"},{type:"number",message:"绩效奖金必须为数字",trigger:"blur"}],fullAttendanceBonus:[{required:!0,message:"请输入全勤奖金",trigger:"blur"},{type:"number",message:"全勤奖金必须为数字",trigger:"blur"}],businessOperationBonus:[{required:!0,message:"请输入业务奖金",trigger:"blur"},{type:"number",message:"业务奖金必须为数字",trigger:"blur"}],reimbursement:[{required:!0,message:"请输入报销金额",trigger:"blur"},{type:"number",message:"报销金额必须为数字",trigger:"blur"}],privateAccount:[{required:!0,message:"请输入私账金额",trigger:"blur"},{type:"number",message:"私账金额必须为数字",trigger:"blur"}],leaveDeduction:[{required:!0,message:"请输入请假扣款",trigger:"blur"},{type:"number",message:"请假扣款必须为数字",trigger:"blur"}],deduction:[{required:!0,message:"请输入扣款",trigger:"blur"},{type:"number",message:"扣款必须为数字",trigger:"blur"}],lateDeduction:[{required:!0,message:"请输入迟到扣款",trigger:"blur"},{type:"number",message:"迟到扣款必须为数字",trigger:"blur"}],socialSecurityPersonal:[{required:!0,message:"请输入社保个人部分",trigger:"blur"},{type:"number",message:"社保个人部分必须为数字",trigger:"blur"}],providentFund:[{required:!0,message:"请输入公积金",trigger:"blur"},{type:"number",message:"公积金必须为数字",trigger:"blur"}],tax:[{required:!0,message:"请输入个税",trigger:"blur"},{type:"number",message:"个税必须为数字",trigger:"blur"}],waterElectricityFee:[{required:!0,message:"请输入水电费",trigger:"blur"},{type:"number",message:"水电费必须为数字",trigger:"blur"}],sumSalary:[{required:!0,message:"请输入应发金额",trigger:"blur"},{type:"number",message:"应发金额必须为数字",trigger:"blur"}],actualSalary:[{required:!0,message:"请输入实发工资",trigger:"blur"},{type:"number",message:"实发工资必须为数字",trigger:"blur"}],totalSalary:[{required:!0,message:"请输入合计金额",trigger:"blur"},{type:"number",message:"合计金额必须为数字",trigger:"blur"}],remark:[{max:255,message:"备注不能超过255个字符",trigger:"blur"}]},ie=async()=>{Q.value=!0;try{const e=await N();200===e.code?H.value=e.data:f.error(e.message||"获取部门列表失败")}catch(e){f.error("加载部门列表失败: "+(e.message||"未知错误"))}finally{Q.value=!1}},se=async e=>{if(ee.value)if(e){0===H.value.length&&await ie(),X.value=!0;try{const a=await D({pageNum:1,pageSize:10,name:e,departmentId:ee.value});if(200===a.code){let e=[];a.data&&a.data.list?e=a.data.list:Array.isArray(a.data)&&(e=a.data),e.forEach((e=>{if(e.departmentId){const a=H.value.find((a=>a.departmentId===e.departmentId||a.departmentId===parseInt(e.departmentId,10)));e.departmentName=a?a.departmentName:"未知部门"}else e.departmentName="未知部门"})),L.value=e}else f.error(a.message||"搜索员工失败")}catch(a){f.error("搜索员工失败: "+(a.message||"未知错误"))}finally{X.value=!1}}else L.value=[];else f.warning("请先选择部门")},pe=async()=>{J.value=!0,K.value=[];try{const a={page:le.page,size:le.size,employeeName:W.value||void 0,departmentId:Z.value||void 0,yearMonth:G.value||void 0},l=await function(a){return e({url:"/salary/page",method:"get",params:a})}(a);200===l.code?(K.value=l.data.list||[],le.total=l.data.total||0):f.error(l.message||"获取工资数据失败")}catch(a){f.error("加载工资数据失败: "+(a.message||"未知错误"))}finally{J.value=!1}},me=e=>{ne.employeeId=null,ne.employeeName="",ne.departmentId=e},ce=()=>{oe.value&&oe.value.resetFields(),ne.id=null,ne.employeeId=null,ne.employeeName="",ne.departmentId=null,ne.date="",ne.basicSalary=0,ne.performanceBonus=0,ne.fullAttendanceBonus=0,ne.businessOperationBonus=0,ne.reimbursement=0,ne.privateAccount=0,ne.leaveDeduction=0,ne.deduction=0,ne.lateDeduction=0,ne.socialSecurityPersonal=0,ne.providentFund=0,ne.tax=0,ne.waterElectricityFee=0,ne.sumSalary=0,ne.actualSalary=0,ne.totalSalary=0,ne.remark="",ee.value=""},ge=()=>{te.value=!1,ce()},ye=()=>{re.value="add",ce(),te.value=!0},ve=async a=>{re.value="edit",ce();try{0===H.value.length&&await ie();const t=await(l=a.id,e({url:`/salary/${l}`,method:"get"}));if(200===t.code){const e=t.data;if(Object.assign(ne,e),ne.basicSalary=e.basicSalary||0,ne.performanceBonus=e.performanceBonus||0,ne.fullAttendanceBonus=e.fullAttendanceBonus||0,ne.businessOperationBonus=e.businessOperationBonus||0,ne.reimbursement=e.reimbursement||0,ne.privateAccount=e.privateAccount||0,ne.leaveDeduction=e.leaveDeduction||0,ne.deduction=e.deduction||0,ne.lateDeduction=e.lateDeduction||0,ne.socialSecurityPersonal=e.socialSecurityPersonal||0,ne.providentFund=e.providentFund||0,ne.tax=e.tax||0,ne.waterElectricityFee=e.waterElectricityFee||0,ne.sumSalary=e.sumSalary||0,ne.actualSalary=e.actualSalary||0,ne.totalSalary=e.totalSalary||0,e.departmentId)ne.departmentId=e.departmentId,ee.value=e.departmentId;else if(e.department){const a=H.value.find((a=>a.departmentName===e.department));a&&(ne.departmentId=a.departmentId,ee.value=a.departmentId)}e.employeeId&&e.employeeName?L.value=[{employeeId:e.employeeId,name:e.employeeName,positionName:e.position||"",departmentId:ne.departmentId,departmentName:e.department||xe(ne.departmentId)}]:L.value=[],te.value=!0}else f.error(t.message||"获取工资详情失败")}catch(t){f.error("获取工资详情失败: "+(t.message||"未知错误"))}var l},fe=a=>{A.confirm(`确定要删除员工 "${a.employeeName}" 在 "${a.date}" 的工资记录吗？此操作不可撤销！`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{J.value=!0;try{const t=await(l=a.id,e({url:`/salary/${l}`,method:"delete"}));200===t.code?(f({type:"success",message:"删除成功",duration:2e3}),pe()):(f.error(t.message||"删除失败"),J.value=!1)}catch(t){f.error("删除失败: "+(t.message||"未知错误")),J.value=!1}var l})).catch((()=>{}))},be=()=>{if(0===ae.value.length)return void f.warning("请选择要删除的记录");const a=ae.value.map((e=>e.id)),l=ae.value.map((e=>`${e.employeeName}(${e.date})`)).join("、");A.confirm(`确定要删除以下员工的工资记录吗？\n${l}\n此操作不可撤销！`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{J.value=!0;try{const l=await function(a){return e({url:"/salary/batch",method:"delete",data:a})}(a);200===l.code?(f({type:"success",message:"批量删除成功",duration:2e3}),ae.value=[],pe()):(f.error(l.message||"批量删除失败"),J.value=!1)}catch(l){f.error("批量删除失败: "+(l.message||"未知错误")),J.value=!1}})).catch((()=>{}))},he=e=>{const a=L.value.find((a=>a.employeeId===e));a&&(ne.employeeName=a.name)},we=async a=>{a&&await a.validate((async a=>{if(!a)return f.warning("请完善表单信息"),!1;ue.value=!0;try{const a={id:ne.id,employeeId:ne.employeeId,date:ne.date,basicSalary:ne.basicSalary,performanceBonus:ne.performanceBonus,fullAttendanceBonus:ne.fullAttendanceBonus,businessOperationBonus:ne.businessOperationBonus,reimbursement:ne.reimbursement,privateAccount:ne.privateAccount,leaveDeduction:ne.leaveDeduction,deduction:ne.deduction,lateDeduction:ne.lateDeduction,socialSecurityPersonal:ne.socialSecurityPersonal,providentFund:ne.providentFund,tax:ne.tax,waterElectricityFee:ne.waterElectricityFee,sumSalary:ne.sumSalary,actualSalary:ne.actualSalary,totalSalary:ne.totalSalary,remark:ne.remark};let t;t="add"===re.value?await(l=a,e({url:"/salary",method:"post",data:l})):await function(a){return e({url:"/salary",method:"put",data:a})}(a),200===t.code?(f({type:"success",message:"add"===re.value?"添加成功":"更新成功",duration:2e3}),te.value=!1,ce(),pe()):f.error(t.message||("add"===re.value?"添加失败":"更新失败"))}catch(t){f.error("提交失败: "+(t.message||"未知错误"))}finally{ue.value=!1}var l}))},_e=e=>{ae.value=e},Ve=()=>{le.page=1,pe()},Se=()=>{W.value="",Z.value="",G.value="",le.page=1,pe()},Ie=e=>{le.page=e,pe()},Be=e=>{le.size=e,le.page=1,pe()},qe=e=>null==e?"¥0.00":"¥"+parseFloat(e).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),xe=e=>{const a=H.value.find((a=>a.departmentId===e));return a?a.departmentName:""};r((()=>qe(ne.totalSalary))),o((()=>{ie(),pe()}));const ke=l(!1),Fe=l(!1),Ue=l(null),Ae=l("/api/salary/import"),Ne=()=>{ke.value=!0},De=(e,a)=>{"salary"===a&&(Ue.value=e,Fe.value=!0,(e.successCount>0||e.failureCount>0||e.generalErrors&&e.generalErrors.length>0)&&pe())},Ce=e=>`工资数据全部导入成功！共处理 ${e.processedRows||0} 行，成功导入 ${e.successCount||0} 条记录。`,Ee=e=>{let a=`工资数据导入处理完成。共处理 ${e.processedRows||0} 行，成功 ${e.successCount||0} 行，失败/跳过 ${e.failureCount||0} 行。`;return e.generalErrors&&e.generalErrors.length>0&&(a+=` 通用错误: ${e.generalErrors.join("; ")}`),a};return(e,a)=>{const l=g("el-icon"),t=g("el-input"),r=g("el-option"),o=g("el-select"),f=g("el-date-picker"),A=g("el-button"),N=g("el-table-column"),D=g("el-tag"),ie=g("el-tooltip"),pe=g("el-table"),ce=g("el-pagination"),xe=g("el-form-item"),Ye=g("el-input-number"),$e=g("el-form"),Oe=g("el-dialog"),ze=y("loading");return b(),u("div",E,[n("div",Y,[n("div",$,[i(t,{modelValue:W.value,"onUpdate:modelValue":a[0]||(a[0]=e=>W.value=e),placeholder:"搜索员工姓名",clearable:"",onKeyup:c(Ve,["enter"])},{prefix:m((()=>[i(l,null,{default:m((()=>[i(h(w))])),_:1})])),_:1},8,["modelValue"]),i(o,{modelValue:Z.value,"onUpdate:modelValue":a[1]||(a[1]=e=>Z.value=e),placeholder:"选择部门",clearable:"",loading:Q.value},{default:m((()=>[(b(!0),u(_,null,V(H.value,(e=>(b(),s(r,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"]),i(f,{modelValue:G.value,"onUpdate:modelValue":a[2]||(a[2]=e=>G.value=e),type:"month",placeholder:"选择年月",format:"YYYY-MM","value-format":"YYYY-MM"},null,8,["modelValue"]),i(A,{type:"primary",onClick:Ve},{default:m((()=>[i(l,null,{default:m((()=>[i(h(w))])),_:1}),a[27]||(a[27]=S("搜索 "))])),_:1}),i(A,{onClick:Se},{default:m((()=>[i(l,null,{default:m((()=>[i(h(I))])),_:1}),a[28]||(a[28]=S("重置 "))])),_:1})]),n("div",O,[i(A,{type:"danger",disabled:0===ae.value.length,onClick:be},{default:m((()=>[i(l,null,{default:m((()=>[i(h(B))])),_:1}),a[29]||(a[29]=S("批量删除 "))])),_:1},8,["disabled"]),i(A,{type:"primary",class:"add-btn",onClick:ye},{default:m((()=>[i(l,null,{default:m((()=>[i(h(q))])),_:1}),a[30]||(a[30]=S("添加工资记录 "))])),_:1}),i(A,{type:"success",onClick:Ne},{default:m((()=>[i(l,null,{default:m((()=>[i(h(x))])),_:1}),a[31]||(a[31]=S(" 导入工资 "))])),_:1})])]),d((b(),s(pe,{data:K.value,border:"","row-key":"id",onSelectionChange:_e,"max-height":"calc(100vh - 220px)",class:"custom-table"},{default:m((()=>[i(N,{type:"selection",width:"55",align:"center"}),i(N,{label:"员工姓名",prop:"employeeName","min-width":"100","show-overflow-tooltip":""}),i(N,{label:"部门",prop:"department","min-width":"120","show-overflow-tooltip":""}),i(N,{label:"职位",prop:"position","min-width":"120","show-overflow-tooltip":""}),i(N,{label:"年月",prop:"date",width:"100",align:"center"},{default:m((({row:e})=>[i(D,{type:"info"},{default:m((()=>[S(k(e.date),1)])),_:2},1024)])),_:1}),i(N,{label:"基本工资",prop:"basicSalary","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.basicSalary)),1)])),_:1}),i(N,{label:"奖金",prop:"performanceBonus","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.performanceBonus)),1)])),_:1}),i(N,{label:"全勤奖",prop:"fullAttendanceBonus","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.fullAttendanceBonus)),1)])),_:1}),i(N,{prop:"businessOperationBonus","min-width":"120",align:"right"},{header:m((()=>[i(ie,{content:"业务与操作奖金",placement:"top"},{default:m((()=>a[32]||(a[32]=[n("span",null,"业务与操作奖金",-1)]))),_:1})])),default:m((({row:e})=>[S(k(qe(e.businessOperationBonus)),1)])),_:1}),i(N,{label:"实得金额",prop:"sumSalary","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.sumSalary)),1)])),_:1}),i(N,{label:"请假",prop:"leaveDeduction","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.leaveDeduction)),1)])),_:1}),i(N,{label:"扣借款",prop:"deduction","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.deduction)),1)])),_:1}),i(N,{prop:"lateDeduction","min-width":"120",align:"right"},{header:m((()=>[i(ie,{content:"迟到与缺卡",placement:"top"},{default:m((()=>a[33]||(a[33]=[n("span",null,"迟到与缺卡",-1)]))),_:1})])),default:m((({row:e})=>[S(k(qe(e.lateDeduction)),1)])),_:1}),i(N,{prop:"socialSecurityPersonal","min-width":"130",align:"right"},{header:m((()=>[i(ie,{content:"社会保险费个人部分",placement:"top"},{default:m((()=>a[34]||(a[34]=[n("span",null,"社会保险费个人部分",-1)]))),_:1})])),default:m((({row:e})=>[S(k(qe(e.socialSecurityPersonal)),1)])),_:1}),i(N,{label:"公积金",prop:"providentFund","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.providentFund)),1)])),_:1}),i(N,{prop:"tax","min-width":"120",align:"right"},{header:m((()=>[i(ie,{content:"代扣代缴个税",placement:"top"},{default:m((()=>a[35]||(a[35]=[n("span",null,"代扣代缴个税",-1)]))),_:1})])),default:m((({row:e})=>[S(k(qe(e.tax)),1)])),_:1}),i(N,{label:"水电费",prop:"waterElectricityFee","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.waterElectricityFee)),1)])),_:1}),i(N,{label:"实发工资",prop:"actualSalary","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.actualSalary)),1)])),_:1}),i(N,{label:"报销",prop:"reimbursement","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.reimbursement)),1)])),_:1}),i(N,{label:"私帐",prop:"privateAccount","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(qe(e.privateAccount)),1)])),_:1}),i(N,{label:"合计",prop:"totalSalary","min-width":"120",align:"right","show-overflow-tooltip":""},{default:m((({row:e})=>[n("span",z,k(qe(e.totalSalary)),1)])),_:1}),i(N,{label:"备注",prop:"remark","min-width":"150",align:"left","show-overflow-tooltip":""},{default:m((({row:e})=>[S(k(e.remark||"-"),1)])),_:1}),i(N,{label:"操作",width:"150",align:"center",fixed:"right","class-name":"operation-column"},{default:m((({row:e})=>[n("div",M,[i(A,{class:"edit-btn",onClick:a=>ve(e),title:"编辑"},{default:m((()=>[i(l,null,{default:m((()=>[i(h(F))])),_:1})])),_:2},1032,["onClick"]),i(A,{class:"delete-btn",onClick:a=>fe(e),title:"删除"},{default:m((()=>[i(l,null,{default:m((()=>[i(h(B))])),_:1})])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[ze,J.value]]),n("div",P,[i(ce,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":le.page,"page-size":le.size,total:le.total,"page-sizes":[10,20,50,100],onSizeChange:Be,onCurrentChange:Ie},null,8,["current-page","page-size","total"])]),i(Oe,{modelValue:te.value,"onUpdate:modelValue":a[24]||(a[24]=e=>te.value=e),title:"add"===re.value?"添加工资记录":"编辑工资记录",width:"550px","destroy-on-close":"",class:"custom-dialog"},{default:m((()=>[i($e,{ref_key:"formRef",ref:oe,model:ne,rules:de,"label-width":"150px",class:"dialog-form"},{default:m((()=>[i(xe,{label:"部门",prop:"departmentId",required:""},{default:m((()=>[i(o,{modelValue:ee.value,"onUpdate:modelValue":a[3]||(a[3]=e=>ee.value=e),placeholder:"请选择部门",style:{width:"100%"},onChange:me,loading:Q.value},{default:m((()=>[(b(!0),u(_,null,V(H.value,(e=>(b(),s(r,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),i(xe,{label:"员工",prop:"employeeId",required:""},{default:m((()=>[i(o,{modelValue:ne.employeeId,"onUpdate:modelValue":a[4]||(a[4]=e=>ne.employeeId=e),placeholder:"请先选择部门再输入员工名称搜索",disabled:!ee.value,onChange:he,remote:"",filterable:"","remote-method":se,loading:X.value,style:{width:"100%"}},{empty:m((()=>[n("p",j,k(ee.value?"请输入员工名称搜索":"请先选择部门"),1)])),default:m((()=>[(b(!0),u(_,null,V(L.value,(e=>(b(),s(r,{key:e.employeeId,label:e.name+(e.departmentName?` (${e.departmentName}${e.positionName?"-"+e.positionName:""})`:e.positionName?` (${e.positionName})`:""),value:e.employeeId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled","loading"]),ee.value||"add"!==re.value?p("",!0):(b(),u("div",T))])),_:1}),i(xe,{label:"年月",prop:"date",required:""},{default:m((()=>[i(f,{modelValue:ne.date,"onUpdate:modelValue":a[5]||(a[5]=e=>ne.date=e),type:"month",placeholder:"选择年月",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"100%"},disabled:"edit"===re.value},null,8,["modelValue","disabled"])])),_:1}),i(xe,{label:"基本工资",prop:"basicSalary",required:""},{default:m((()=>[i(Ye,{modelValue:ne.basicSalary,"onUpdate:modelValue":a[6]||(a[6]=e=>ne.basicSalary=e),precision:2,step:500,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"奖金",prop:"performanceBonus",required:""},{default:m((()=>[i(Ye,{modelValue:ne.performanceBonus,"onUpdate:modelValue":a[7]||(a[7]=e=>ne.performanceBonus=e),precision:2,step:500,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"全勤奖",prop:"fullAttendanceBonus",required:""},{default:m((()=>[i(Ye,{modelValue:ne.fullAttendanceBonus,"onUpdate:modelValue":a[8]||(a[8]=e=>ne.fullAttendanceBonus=e),precision:2,step:500,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"业务与操作奖金",prop:"businessOperationBonus",required:""},{default:m((()=>[i(Ye,{modelValue:ne.businessOperationBonus,"onUpdate:modelValue":a[9]||(a[9]=e=>ne.businessOperationBonus=e),precision:2,step:500,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"实得金额",prop:"sumSalary",required:""},{default:m((()=>[i(Ye,{modelValue:ne.sumSalary,"onUpdate:modelValue":a[10]||(a[10]=e=>ne.sumSalary=e),precision:2,step:100,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"请假",prop:"leaveDeduction",required:""},{default:m((()=>[i(Ye,{modelValue:ne.leaveDeduction,"onUpdate:modelValue":a[11]||(a[11]=e=>ne.leaveDeduction=e),precision:2,step:100,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"扣借款",prop:"deduction",required:""},{default:m((()=>[i(Ye,{modelValue:ne.deduction,"onUpdate:modelValue":a[12]||(a[12]=e=>ne.deduction=e),precision:2,step:100,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"迟到与缺卡",prop:"lateDeduction",required:""},{default:m((()=>[i(Ye,{modelValue:ne.lateDeduction,"onUpdate:modelValue":a[13]||(a[13]=e=>ne.lateDeduction=e),precision:2,step:100,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"社会保险费个人部分",prop:"socialSecurityPersonal",required:""},{default:m((()=>[i(Ye,{modelValue:ne.socialSecurityPersonal,"onUpdate:modelValue":a[14]||(a[14]=e=>ne.socialSecurityPersonal=e),precision:2,step:50,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"公积金",prop:"providentFund",required:""},{default:m((()=>[i(Ye,{modelValue:ne.providentFund,"onUpdate:modelValue":a[15]||(a[15]=e=>ne.providentFund=e),precision:2,step:50,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"代扣代缴个税",prop:"tax",required:""},{default:m((()=>[i(Ye,{modelValue:ne.tax,"onUpdate:modelValue":a[16]||(a[16]=e=>ne.tax=e),precision:2,step:50,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"水电费",prop:"waterElectricityFee",required:""},{default:m((()=>[i(Ye,{modelValue:ne.waterElectricityFee,"onUpdate:modelValue":a[17]||(a[17]=e=>ne.waterElectricityFee=e),precision:2,step:10,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"实发工资",prop:"actualSalary",required:""},{default:m((()=>[i(Ye,{modelValue:ne.actualSalary,"onUpdate:modelValue":a[18]||(a[18]=e=>ne.actualSalary=e),precision:2,step:100,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"报销",prop:"reimbursement",required:""},{default:m((()=>[i(Ye,{modelValue:ne.reimbursement,"onUpdate:modelValue":a[19]||(a[19]=e=>ne.reimbursement=e),precision:2,step:100,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"私帐",prop:"privateAccount",required:""},{default:m((()=>[i(Ye,{modelValue:ne.privateAccount,"onUpdate:modelValue":a[20]||(a[20]=e=>ne.privateAccount=e),precision:2,step:100,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(xe,{label:"备注",prop:"remark"},{default:m((()=>[i(t,{modelValue:ne.remark,"onUpdate:modelValue":a[21]||(a[21]=e=>ne.remark=e),type:"textarea",rows:2,placeholder:"请输入备注信息",maxlength:"255","show-word-limit":""},null,8,["modelValue"])])),_:1}),i(xe,{label:"合计",prop:"totalSalary",required:""},{default:m((()=>[i(Ye,{modelValue:ne.totalSalary,"onUpdate:modelValue":a[22]||(a[22]=e=>ne.totalSalary=e),precision:2,step:100,style:{width:"100%"}},{prefix:m((()=>[i(l,{class:"salary-icon"},{default:m((()=>[i(h(U))])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"]),n("div",R,[i(A,{onClick:ge},{default:m((()=>a[36]||(a[36]=[S("取消")]))),_:1}),i(A,{type:"primary",loading:ue.value,onClick:a[23]||(a[23]=e=>we(oe.value))},{default:m((()=>a[37]||(a[37]=[S("确定")]))),_:1},8,["loading"])])])),_:1},8,["modelValue","title"]),ke.value?(b(),s(v,{key:0,"model-value":ke.value,"onUpdate:modelValue":a[25]||(a[25]=e=>ke.value=e),importType:"salary",dialogTitle:"导入工资数据",templateFileName:"工资导入模板.xlsx",uploadUrl:Ae.value,maxFileSizeMB:200,onImportSuccess:De,successMessageFormatter:Ce,partialSuccessMessageFormatter:Ee},null,8,["model-value","uploadUrl"])):p("",!0),Fe.value?(b(),s(C,{key:1,"model-value":Fe.value,"onUpdate:modelValue":a[26]||(a[26]=e=>Fe.value=e),importResult:Ue.value,title:"工资导入结果详情"},null,8,["model-value","importResult"])):p("",!0)])}}},[["__scopeId","data-v-2a72ab92"]]);export{K as default};
