import{_ as e,u as a,a as l,r as t,v as o,x as s,y as n,z as d,A as i,B as r,C as u,D as c,o as m,E as p,F as h,c as v,f,k as w,j as g,G as y,b,m as _,e as P,H as V,g as k,I as x,J as C,K as I,L as N,t as M,h as U,M as D,n as E,N as R,O as j,P as A,Q as F,q as z}from"./index-BDR0Pmj6.js";const T={class:"app-container"},O={class:"logo-container"},q={key:0,src:"/assets/logo-FcOHnPPD.jpg",class:"logo-img",alt:"Logo"},B={key:1,class:"logo-text-collapsed"},L={class:"sidebar-footer"},S={class:"header-left"},H={class:"header-right"},G={class:"user-info"},J={class:"user-name"},K={class:"dialog-footer"},Q=e({__name:"Dashboard",setup(e){const Q=b(),Z=y(),W=a(),X=l(null),Y=l(!1),$=l(""),ee=l(null),ae=l([]),le=l(!1),te=l(!1),oe=t({name:"",email:"",phone:"",idCard:"",oldPassword:"",newPassword:"",confirmPassword:"",departmentName:"",positionName:"",entryDate:"",status:"",logisticsRoute:""}),se={name:[{required:!0,message:"请输入姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱格式",trigger:["blur","change"]}],oldPassword:[{validator:(e,a,l)=>{oe.newPassword&&!a?l(new Error("修改密码时需要输入原密码")):a&&!oe.newPassword?l(new Error("请输入新密码")):l()},trigger:"blur"}],newPassword:[{validator:(e,a,l)=>{var t;if(oe.oldPassword&&!a)l(new Error("请输入新密码"));else if(a){if(a.length<8)return void l(new Error("密码长度至少为8位"));const e=/[A-Z]/.test(a),t=/[a-z]/.test(a),o=/\d/.test(a);if(!e||!t||!o)return void l(new Error("密码必须包含大小写字母和数字"));if(!oe.oldPassword)return void l(new Error("请先输入原密码"))}oe.confirmPassword&&(null==(t=ee.value)||t.validateField("confirmPassword")),l()},trigger:"blur"}],confirmPassword:[{validator:(e,a,l)=>{oe.newPassword&&!a?l(new Error("请确认新密码")):a!==oe.newPassword?l(new Error("两次输入的密码不一致")):l()},trigger:"blur"}]},ne=[{id:"dashboard",title:"首页",path:"/",icon:o(s),isMenuItem:!0},{id:"organization",title:"组织管理",icon:o(n),children:[{id:"department",title:"部门管理",path:"/department",isMenuItem:!0},{id:"position",title:"职位管理",path:"/position",isMenuItem:!0}]},{id:"personnel",title:"人员管理",icon:o(d),children:[{id:"employee",title:"员工管理",path:"/employee",isMenuItem:!0},{id:"client",title:"客户管理",path:"/client",isMenuItem:!0}]},{id:"performance",title:"业绩管理",icon:o(r),children:[{id:"performance-analysis",title:"业绩分析",path:"/performance-analysis",isMenuItem:!0},{id:"salary",title:"工资管理",path:"/salary",isMenuItem:!0},{id:"department-expense",title:"部门开销",path:"/department-expense",icon:o(i),isMenuItem:!0},{id:"employee-expense",title:"员工费用",path:"/employee-expense",icon:o(i),isMenuItem:!0}]},{id:"sales-report",title:"销售日报",icon:o(u),children:[{id:"sales-report-management",title:"日报管理",path:"/sales-report-management",isMenuItem:!0}]},{id:"petty-cash",title:"备用金管理",path:"/petty-cash",icon:o(i),isMenuItem:!0}],de=c((()=>{const e=new Set(W.accessibleMenuItems||[]);const a=function e(a){return a.map((a=>{const l={...a};return a.icon&&"object"==typeof a.icon&&(l.icon=o(a.icon.__v_raw||a.icon)),a.children&&(l.children=e(a.children)),l}))}(ne);return function a(l){return l.reduce(((l,t)=>{const o={...t};t.children&&(o.children=a(t.children));const s=e.has(t.id),n=o.children&&o.children.length>0;return(s||n)&&l.push(o),l}),[])}(a)})),ie=c((()=>{const e=[];return function a(l,t=null){l.forEach((l=>{const o={id:l.id,title:l.title,path:l.path};t&&(o.parentId=t.id,o.parentTitle=t.title),e.push(o),l.children&&a(l.children,l)}))}(de.value),e})),re=()=>{const e=Z.path,a=ie.value.find((a=>a.path===e));if(a)$.value=a.id,ae.value=[],a.parentTitle&&ae.value.push({title:a.parentTitle,path:null}),ae.value.push({title:a.title,path:a.path});else{const e=de.value.find((e=>"/"===e.path||"dashboard"===e.id));e?($.value=e.id,ae.value=[{title:e.title,path:e.path}]):($.value="",ae.value=[{title:"首页",path:"/"}])}};m((async()=>{if((!W.user||0===W.accessibleMenuItems.length)&&W.isAuthenticated)try{await W.fetchUserInfoAndSet()}catch(e){"获取用户信息失败"===e.message||e.message.includes("登录已过期")||p.error("加载用户权限失败，请稍后重试。")}X.value=W.user,re()})),h((()=>Z.path),(()=>{re()}),{immediate:!0}),c((()=>X.value?"admin"===X.value.role?"管理员":"普通用户":""));const ue=e=>{Q.push(e)},ce=()=>{Y.value=!Y.value},me=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})},pe=async()=>{try{const e=await z();200===e.code&&(X.value=e.data,Object.assign(oe,{name:X.value.name,email:X.value.email,phone:X.value.phone,idCard:X.value.idCard,oldPassword:"",newPassword:"",confirmPassword:"",departmentName:X.value.departmentName,positionName:X.value.positionName,entryDate:me(X.value.entryDate),status:"Active"===X.value.status?"在职":"离职",logisticsRoute:X.value.logisticsRoute}))}catch(e){p.error("获取用户信息失败")}},he=()=>{te.value=!te.value,te.value||Object.assign(oe,{name:X.value.name,email:X.value.email,phone:X.value.phone,idCard:X.value.idCard,oldPassword:"",newPassword:"",confirmPassword:"",departmentName:X.value.departmentName,positionName:X.value.positionName,entryDate:me(X.value.entryDate),status:"Active"===X.value.status?"在职":"离职",logisticsRoute:X.value.logisticsRoute})};return(e,a)=>{const l=g("el-icon"),t=g("el-menu-item"),o=g("el-sub-menu"),s=g("el-menu"),n=g("el-scrollbar"),r=g("el-aside"),u=g("el-breadcrumb-item"),c=g("el-breadcrumb"),m=g("el-avatar"),h=g("el-dropdown-item"),y=g("el-dropdown-menu"),b=g("el-dropdown"),z=g("el-header"),Z=g("router-view"),ne=g("el-main"),ie=g("el-container"),re=g("el-input"),me=g("el-form-item"),ve=g("el-divider"),fe=g("el-form"),we=g("el-button"),ge=g("el-dialog");return _(),v("div",T,[f(ie,{class:"layout-container"},{default:w((()=>[f(r,{width:Y.value?"64px":"200px",class:"sidebar"},{default:w((()=>[P("div",O,[P("div",{class:V(["logo-content",{collapsed:Y.value}])},[Y.value?k("",!0):(_(),v("img",q)),Y.value?(_(),v("span",B,"中航")):k("",!0)],2)]),f(n,{class:"sidebar-menu-container"},{default:w((()=>[f(s,{"default-active":$.value,class:"sidebar-menu",collapse:Y.value,"collapse-transition":!1,"background-color":"#001529","text-color":"#fff","active-text-color":"#409EFF"},{default:w((()=>[(_(!0),v(x,null,C(de.value,(e=>(_(),v(x,{key:e.id},[!e.children||e.isMenuItem?(_(),I(t,{key:0,index:e.id,onClick:a=>ue(e.path),class:"menu-item-with-transition"},{title:w((()=>[P("span",null,M(e.title),1)])),default:w((()=>[e.icon?(_(),I(l,{key:0},{default:w((()=>[(_(),I(N(e.icon)))])),_:2},1024)):k("",!0)])),_:2},1032,["index","onClick"])):e.children&&e.children.length>0?(_(),I(o,{key:1,index:e.id},{title:w((()=>[e.icon?(_(),I(l,{key:0},{default:w((()=>[(_(),I(N(e.icon)))])),_:2},1024)):k("",!0),P("span",null,M(e.title),1)])),default:w((()=>[(_(!0),v(x,null,C(e.children,(e=>(_(),I(t,{key:e.id,index:e.id,onClick:a=>ue(e.path),class:"menu-item-with-transition"},{default:w((()=>[P("span",null,M(e.title),1)])),_:2},1032,["index","onClick"])))),128))])),_:2},1032,["index"])):k("",!0)],64)))),128))])),_:1},8,["default-active","collapse"])])),_:1}),P("div",L,[f(l,{class:"toggle-button",onClick:ce},{default:w((()=>[f(U(D))])),_:1})])])),_:1},8,["width"]),f(ie,{class:"main-container"},{default:w((()=>[f(z,{class:"main-header"},{default:w((()=>[P("div",S,[f(c,{separator:"/"},{default:w((()=>[(_(!0),v(x,null,C(ae.value,((e,a)=>(_(),I(u,{key:a,to:e.path?{path:e.path}:null},{default:w((()=>[E(M(e.title),1)])),_:2},1032,["to"])))),128))])),_:1})]),P("div",H,[f(b,{trigger:"hover",onCommand:a[0]||(a[0]=e=>"logout"===e?(async()=>{try{j.confirm("确定要退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await A(),W.logout(),Q.push("/login"),p.success("已成功退出登录")}catch(e){p.error("登出时发生错误")}})).catch((()=>{}))}catch(e){}})():(async()=>{await pe(),le.value=!0,te.value=!1})())},{dropdown:w((()=>[f(y,null,{default:w((()=>[f(h,{command:"profile"},{default:w((()=>[f(l,null,{default:w((()=>[f(U(d))])),_:1}),a[17]||(a[17]=E(" 个人信息 "))])),_:1}),f(h,{command:"logout"},{default:w((()=>[f(l,null,{default:w((()=>[f(U(i))])),_:1}),a[18]||(a[18]=E(" 退出登录 "))])),_:1})])),_:1})])),default:w((()=>{var e,a,t;return[P("div",G,[f(m,{size:32,src:(null==(e=X.value)?void 0:e.avatar)||"https://ui-avatars.com/api/?name="+(null==(a=X.value)?void 0:a.name)+"&background=random",class:"user-avatar"},null,8,["src"]),P("span",J,M(null==(t=X.value)?void 0:t.name),1),f(l,{class:"dropdown-icon"},{default:w((()=>[f(U(R))])),_:1})])]})),_:1})])])),_:1}),f(ne,{class:"main-content"},{default:w((()=>[f(Z)])),_:1})])),_:1})])),_:1}),f(ge,{modelValue:le.value,"onUpdate:modelValue":a[15]||(a[15]=e=>le.value=e),title:te.value?"编辑个人信息":"个人信息",width:"500px","close-on-click-modal":!1,onClosed:a[16]||(a[16]=e=>te.value=!1)},{footer:w((()=>[P("span",K,[f(we,{onClick:a[13]||(a[13]=e=>le.value=!1)},{default:w((()=>a[20]||(a[20]=[E("关闭")]))),_:1}),te.value?(_(),v(x,{key:1},[f(we,{onClick:he},{default:w((()=>a[22]||(a[22]=[E("取消")]))),_:1}),f(we,{type:"primary",onClick:a[14]||(a[14]=e=>(async e=>{if(e)try{if(await e.validate()){if(oe.newPassword){if(!oe.oldPassword)return void p.error("修改密码时需要输入原密码");if(oe.newPassword!==oe.confirmPassword)return void p.error("两次输入的新密码不一致")}const e={name:oe.name,phone:oe.phone,email:oe.email,password:oe.newPassword||void 0,oldPassword:oe.oldPassword||void 0},a=await F(e);200===a.code?(p.success("个人信息更新成功"),te.value=!1,await pe(),le.value=!1):p.error(a.message||"更新失败")}}catch(a){a.response?p.error(a.response.data.message||"更新失败"):p.error(a.message||"更新失败")}})(ee.value))},{default:w((()=>a[23]||(a[23]=[E(" 保存 ")]))),_:1})],64)):(_(),I(we,{key:0,type:"primary",onClick:he},{default:w((()=>a[21]||(a[21]=[E(" 编辑信息 ")]))),_:1}))])])),default:w((()=>[f(fe,{ref_key:"profileFormRef",ref:ee,model:oe,rules:se,"label-width":"100px",disabled:!te.value,"status-icon":""},{default:w((()=>[f(me,{label:"姓名",prop:"name"},{default:w((()=>[f(re,{modelValue:oe.name,"onUpdate:modelValue":a[1]||(a[1]=e=>oe.name=e)},null,8,["modelValue"])])),_:1}),f(me,{label:"手机号",prop:te.value?"phone":""},{default:w((()=>[f(re,{modelValue:oe.phone,"onUpdate:modelValue":a[2]||(a[2]=e=>oe.phone=e),disabled:!te.value},null,8,["modelValue","disabled"])])),_:1},8,["prop"]),f(me,{label:"邮箱",prop:"email"},{default:w((()=>[f(re,{modelValue:oe.email,"onUpdate:modelValue":a[3]||(a[3]=e=>oe.email=e)},null,8,["modelValue"])])),_:1}),te.value?k("",!0):(_(),I(me,{key:0,label:"身份证号"},{default:w((()=>[f(re,{modelValue:oe.idCard,"onUpdate:modelValue":a[4]||(a[4]=e=>oe.idCard=e),disabled:""},null,8,["modelValue"])])),_:1})),te.value?k("",!0):(_(),v(x,{key:1},[f(me,{label:"所属部门"},{default:w((()=>[f(re,{modelValue:oe.departmentName,"onUpdate:modelValue":a[5]||(a[5]=e=>oe.departmentName=e),disabled:""},null,8,["modelValue"])])),_:1}),f(me,{label:"职位"},{default:w((()=>[f(re,{modelValue:oe.positionName,"onUpdate:modelValue":a[6]||(a[6]=e=>oe.positionName=e),disabled:""},null,8,["modelValue"])])),_:1}),f(me,{label:"入职日期"},{default:w((()=>[f(re,{modelValue:oe.entryDate,"onUpdate:modelValue":a[7]||(a[7]=e=>oe.entryDate=e),disabled:""},null,8,["modelValue"])])),_:1}),f(me,{label:"在职状态"},{default:w((()=>[f(re,{modelValue:oe.status,"onUpdate:modelValue":a[8]||(a[8]=e=>oe.status=e),disabled:""},null,8,["modelValue"])])),_:1}),f(me,{label:"物流航线"},{default:w((()=>[f(re,{modelValue:oe.logisticsRoute,"onUpdate:modelValue":a[9]||(a[9]=e=>oe.logisticsRoute=e),disabled:""},null,8,["modelValue"])])),_:1})],64)),te.value?(_(),v(x,{key:2},[f(ve,{"content-position":"center"},{default:w((()=>a[19]||(a[19]=[E("修改密码（选填）")]))),_:1}),f(me,{label:"原密码",prop:"oldPassword"},{default:w((()=>[f(re,{modelValue:oe.oldPassword,"onUpdate:modelValue":a[10]||(a[10]=e=>oe.oldPassword=e),type:"password","show-password":"",placeholder:"修改密码时必填"},null,8,["modelValue"])])),_:1}),f(me,{label:"新密码",prop:"newPassword"},{default:w((()=>[f(re,{modelValue:oe.newPassword,"onUpdate:modelValue":a[11]||(a[11]=e=>oe.newPassword=e),type:"password","show-password":"",placeholder:"至少8位，必须包含大小写字母和数字"},null,8,["modelValue"])])),_:1}),f(me,{label:"确认密码",prop:"confirmPassword"},{default:w((()=>[f(re,{modelValue:oe.confirmPassword,"onUpdate:modelValue":a[12]||(a[12]=e=>oe.confirmPassword=e),type:"password","show-password":"",placeholder:"请再次输入新密码"},null,8,["modelValue"])])),_:1})],64)):k("",!0)])),_:1},8,["model","disabled"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-1ad179c6"]]);export{Q as default};
