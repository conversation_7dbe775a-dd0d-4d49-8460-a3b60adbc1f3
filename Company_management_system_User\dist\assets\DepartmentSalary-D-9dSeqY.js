import{_ as e,a,r as t,o as l,c as r,d as n,Z as i,f as o,e as d,i as u,g as s,a7 as p,j as c,$ as m,Y as g,z as f,E as h,m as v,a0 as w,n as y,a1 as b,t as _,F as k}from"./index-CGqeVPF3.js";import{b as x}from"./salary-VxckMG12.js";import{c as N}from"./department-DD9JCT-L.js";import"./request-Cm8Ap7dD.js";const z={class:"department-salary-container"},S={class:"toolbar"},$={class:"filter-actions"},D={key:0,class:"date-format"},V={class:"total-salary"},Y={class:"pagination-container"},A={key:0,class:"empty-data"},C=e({__name:"DepartmentSalary",setup(e){const C=a([]),j=a(!1),B=a([]),F=a(""),I=a(null),M=a(!1),E=a([]),P=a([]),U=a([]),O={checkStrictly:!0,emitPath:!1,expandTrigger:"hover",multiple:!0},q=a([]),K=e=>{if(q.value.includes(e))return!0;for(const a of E.value)if(q.value.includes(a.value)){const t=(e,a)=>{if(!e||0===e.length)return!1;for(const l of e){if(l.value===a)return!0;if(l.children&&t(l.children,a))return!0}return!1};if(t(a.children,e))return!0}return!1},T=t({page:1,size:10,total:0}),Z=async()=>{if(!U.value||0===U.value.length)return void h.warning("请先选择要查看的部门");let e=!1;for(const t of U.value)if(!K(t)){e=!0;break}if(e)return h.warning("您只能查看自己负责的部门及其下级部门的工资数据"),C.value=[],void(T.total=0);try{j.value=!0;let e={page:T.page,size:T.size,departmentIds:U.value};B.value&&2===B.value.length&&(e.startDate=B.value[0].substring(0,7),e.endDate=B.value[1].substring(0,7)),F.value&&""!==F.value.trim()&&(e.employeeName=F.value.trim());const a=await x(e);if(200===a.code){let e=[];a.data&&"object"==typeof a.data&&(Array.isArray(a.data.records)?(e=a.data.records.filter((e=>null!=e)),e=e.map(((e,a)=>(e.id||(e.id=`${e.date}_${e.departmentId||U.value[0]}_${a}`),!e.departmentName&&e.department?e.departmentName=e.department:e.departmentName||(e.departmentName=I.value?I.value.name:"未知部门"),e))),T.total=a.data.total||0):Array.isArray(a.data)?(e=a.data.filter((e=>null!=e)),e=e.map(((e,a)=>(e.id||(e.id=`${e.date}_${e.departmentId||U.value[0]}_${a}`),!e.departmentName&&e.department?e.departmentName=e.department:e.departmentName||(e.departmentName=I.value?I.value.name:"未知部门"),e))),T.total=e.length||0):(e=[],T.total=0)),C.value=e,e.length}else h.error(a.message||"获取部门工资数据失败")}catch(a){h.error("获取部门工资列表失败，请稍后再试")}finally{j.value=!1}},G=e=>e&&e.length?e.map((e=>({value:e.departmentId,label:e.departmentName,children:G(e.children||[])}))):[],H=e=>{if(!e||0===e.length)return U.value=[],P.value=[],T.page=1,void Z();const a=U.value||[],t=e.filter((e=>!a.includes(e))),l=a.filter((a=>!e.includes(a)));let r=[...e],n=[...e];for(const i of t){const e=ee(i);if(e.length>0)for(const a of e)r.includes(a)||r.push(a),n.includes(a)||n.push(a)}for(const i of l){const e=ee(i);e.length>0&&(r=r.filter((a=>!e.includes(a))),n=n.filter((a=>!e.includes(a))))}P.value=n,U.value=r,T.page=1,Z()},J=async()=>{T.page=1,await Z()},L=async()=>{B.value=[],F.value="",T.page=1,await Z()},Q=e=>{T.page=e,Z()},R=e=>{T.size=e,T.page=1,Z()},W=e=>null==e?"¥0.00":"¥"+parseFloat(e).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),X=e=>{if(!e)return"";if(/^\d{4}-\d{2}$/.test(e)){const[a,t]=e.split("-");return`${a}年${t}月`}return e},ee=e=>{const a=[],t=(e,a)=>{if(!e||!e.length)return null;for(const l of e){if(l.value===a)return l;if(l.children&&l.children.length){const e=t(l.children,a);if(e)return e}}return null},l=e=>{if(e.children&&e.children.length)for(const t of e.children)a.push(t.value),l(t)},r=t(E.value,e);return r&&l(r),a};return l((async()=>{await(async()=>{try{M.value=!0;const e=await N();if(200===e.code){const a=e.data;if(E.value=a.map((e=>({value:e.departmentId,label:e.departmentName,children:G(e.children||[])}))),q.value=a.map((e=>e.departmentId)),q.value.length>0){const e=q.value[0],a=ee(e);P.value=[e,...a],U.value=[e,...a],await Z()}else h.warning("您没有任何可以查看工资的部门")}else h.error(e.message||"获取部门信息失败")}catch(e){h.error("加载部门信息失败，请稍后再试")}finally{M.value=!1}})()})),(e,a)=>{const t=u("el-cascader"),l=u("el-date-picker"),h=u("el-button"),x=u("el-input"),N=u("el-icon"),I=u("el-tag"),U=u("el-table-column"),q=u("el-table"),K=u("el-pagination"),Z=u("el-empty"),G=g("loading");return v(),r("div",z,[n("div",S,[n("div",$,[d(t,{modelValue:P.value,"onUpdate:modelValue":a[0]||(a[0]=e=>P.value=e),options:E.value,props:O,placeholder:"请选择您负责的部门",clearable:"",loading:M.value,onChange:H,style:{width:"280px","margin-right":"10px"},"collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":3},null,8,["modelValue","options","loading"]),d(l,{modelValue:B.value,"onUpdate:modelValue":a[1]||(a[1]=e=>B.value=e),type:"monthrange","range-separator":"至","start-placeholder":"开始月份","end-placeholder":"结束月份",format:"YYYY-MM","value-format":"YYYY-MM-DD","prefix-icon":s(p),onChange:J,clearable:"",style:{"margin-right":"10px"}},null,8,["modelValue","prefix-icon"]),d(x,{modelValue:F.value,"onUpdate:modelValue":a[2]||(a[2]=e=>F.value=e),placeholder:"请输入员工姓名",clearable:"",onKeyup:m(J,["enter"]),onClear:J,style:{width:"200px","margin-right":"10px"}},{append:c((()=>[d(h,{icon:s(w),onClick:J},null,8,["icon"])])),_:1},8,["modelValue"]),d(h,{onClick:L},{default:c((()=>[d(N,null,{default:c((()=>[d(s(b))])),_:1}),a[3]||(a[3]=y(" 重置 "))])),_:1})])]),i((v(),f(q,{data:C.value,border:"",stripe:"",style:{width:"100%"},"row-key":"id","max-height":"calc(100vh - 280px)",class:"custom-table"},{default:c((()=>[d(U,{label:"年月",prop:"date",width:"120",align:"center"},{default:c((({row:e})=>[e.date?(v(),r("span",D,_(X(e.date)),1)):(v(),f(I,{key:1,type:"info",size:"small"},{default:c((()=>a[4]||(a[4]=[y("暂无数据")]))),_:1}))])),_:1}),d(U,{label:"部门",prop:"departmentName","min-width":"150",align:"center"},{default:c((({row:e})=>[y(_(e.departmentName||e.department||"总计"),1)])),_:1}),d(U,{label:"员工姓名",prop:"employeeName","min-width":"120",align:"center"},{default:c((({row:e})=>[e.employeeName?(v(),r(k,{key:0},[y(_(e.employeeName),1)],64)):(v(),f(I,{key:1,type:"info",size:"small"},{default:c((()=>a[5]||(a[5]=[y("暂无数据")]))),_:1}))])),_:1}),d(U,{label:"职位",prop:"position","min-width":"120",align:"center"},{default:c((({row:e})=>[e.position?(v(),r(k,{key:0},[y(_(e.position),1)],64)):(v(),f(I,{key:1,type:"info",size:"small"},{default:c((()=>a[6]||(a[6]=[y("暂无数据")]))),_:1}))])),_:1}),d(U,{label:"基本工资",prop:"basicSalary","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.basicSalary)),1)])),_:1}),d(U,{label:"奖金",prop:"performanceBonus","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.performanceBonus)),1)])),_:1}),d(U,{label:"全勤奖",prop:"fullAttendanceBonus","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.fullAttendanceBonus)),1)])),_:1}),d(U,{label:"业务与操作奖金",prop:"businessOperationBonus","min-width":"160",align:"right"},{default:c((({row:e})=>[y(_(W(e.businessOperationBonus)),1)])),_:1}),d(U,{label:"实得金额",prop:"sumSalary","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.sumSalary)),1)])),_:1}),d(U,{label:"请假",prop:"leaveDeduction","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.leaveDeduction)),1)])),_:1}),d(U,{label:"扣款",prop:"deduction","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.deduction)),1)])),_:1}),d(U,{label:"迟到与缺卡",prop:"lateDeduction","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.lateDeduction)),1)])),_:1}),d(U,{label:"社会保险费个人部分",prop:"socialSecurityPersonal","min-width":"180",align:"right"},{default:c((({row:e})=>[y(_(W(e.socialSecurityPersonal)),1)])),_:1}),d(U,{label:"公积金",prop:"providentFund","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.providentFund)),1)])),_:1}),d(U,{label:"代扣代缴个税",prop:"tax","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.tax)),1)])),_:1}),d(U,{label:"水电费",prop:"waterElectricityFee","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.waterElectricityFee)),1)])),_:1}),d(U,{label:"实发工资",prop:"actualSalary","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.actualSalary)),1)])),_:1}),d(U,{label:"报销",prop:"reimbursement","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.reimbursement)),1)])),_:1}),d(U,{label:"私帐",prop:"privateAccount","min-width":"150",align:"right"},{default:c((({row:e})=>[y(_(W(e.privateAccount)),1)])),_:1}),d(U,{label:"合计",prop:"totalSalary","min-width":"180",align:"right"},{default:c((({row:e})=>[n("span",V,_(W(e.totalSalary)),1)])),_:1}),d(U,{label:"备注",prop:"remark","min-width":"180",align:"left","show-overflow-tooltip":""},{default:c((({row:e})=>[y(_(e.remark||"-"),1)])),_:1})])),_:1},8,["data"])),[[G,j.value]]),n("div",Y,[d(K,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":T.page,"page-size":T.size,total:T.total,"page-sizes":[10,20,50,100],onSizeChange:R,onCurrentChange:Q},null,8,["current-page","page-size","total"])]),j.value||0!==C.value.length?o("",!0):(v(),r("div",A,[d(Z,{description:"暂无工资数据"})]))])}}},[["__scopeId","data-v-2def5c27"]]);export{C as default};
