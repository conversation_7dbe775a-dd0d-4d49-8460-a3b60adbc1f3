import{a0 as t}from"./index-BDR0Pmj6.js";function o(o){return t({url:"/position/page",method:"get",params:o})}function n(o){return t({url:`/position/department/${o}`,method:"get"})}function e(o){return t({url:"/position/add",method:"post",data:o})}function r(o){return t({url:"/position/update",method:"put",data:o})}function a(o){return t({url:`/position/${o}`,method:"delete"})}export{e as a,n as b,a as d,o as g,r as u};
