import{_ as e,K as t,k as s,c as l,f as r,h as a,a5 as o,g as i,e as n,I as u,J as d,t as p,a6 as c,a7 as m,a8 as g,a9 as R,n as f,aa as h,ab as y,m as v}from"./index-BDR0Pmj6.js";const w={key:0},_={key:1,class:"errors-content"},b={key:0,class:"general-errors-section"},k={key:1,class:"failed-rows-section"},E=["innerHTML"],M={key:2,class:"success-info-section"},x={class:"dialog-footer"},C=e({__name:"ImportErrorsDialog",props:{modelValue:{type:Boolean,required:!0},importResult:{type:Object,default:()=>null},title:{type:String,default:"导入结果详情"}},emits:["update:modelValue"],setup(e,{emit:C}){const I=C,V=()=>{I("update:modelValue",!1)};return(C,I)=>(v(),t(a(y),{"model-value":e.modelValue,title:e.title,width:"800px",onClose:V,"destroy-on-close":"",class:"import-errors-dialog"},{footer:s((()=>[n("div",x,[r(a(h),{type:"primary",onClick:V},{default:s((()=>I[1]||(I[1]=[f("关闭")]))),_:1})])])),default:s((()=>{var f,h;return[e.importResult&&((null==(f=e.importResult.generalErrors)?void 0:f.length)||(null==(h=e.importResult.failedRows)?void 0:h.length))?(v(),l("div",_,[e.importResult.generalErrors&&e.importResult.generalErrors.length>0?(v(),l("div",b,[I[0]||(I[0]=n("h4",null,"通用错误/信息：",-1)),(v(!0),l(u,null,d(e.importResult.generalErrors,((e,s)=>(v(),t(a(R),{key:"gen_err_"+s,title:e,type:"error",closable:!1,"show-icon":"",class:"error-item"},null,8,["title"])))),128))])):i("",!0),e.importResult.failedRows&&e.importResult.failedRows.length>0?(v(),l("div",k,[n("h4",null,"失败/跳过的行记录：(共 "+p(e.importResult.failureCount)+" 条)",1),r(a(g),{data:e.importResult.failedRows,style:{width:"100%"},border:"","max-height":"300px"},{default:s((()=>[r(a(c),{label:"详细信息","min-width":"700"},{default:s((({row:e})=>[r(a(m),{effect:"dark",content:e.errorMessage,placement:"top",disabled:!e.errorMessage||e.errorMessage.length<30},{default:s((()=>{return[n("div",{class:"error-message-cell",innerHTML:(t=e.errorMessage,t?String(t).replace(/\n/g,"<br />"):"")},null,8,E)];var t})),_:2},1032,["content","disabled"])])),_:1})])),_:1},8,["data"])])):i("",!0),!(e.importResult.successCount>0)||e.importResult.failedRows&&0!==e.importResult.failedRows.length||e.importResult.generalErrors&&0!==e.importResult.generalErrors.length?i("",!0):(v(),l("div",M,[r(a(R),{title:`成功导入 ${e.importResult.successCount} 条记录。`,type:"success",closable:!1,"show-icon":""},null,8,["title"])]))])):(v(),l("div",w,[r(a(o),{description:"暂无导入错误或警告信息"})]))]})),_:1},8,["model-value","title"]))}},[["__scopeId","data-v-64ad632c"]]);export{C as I};
