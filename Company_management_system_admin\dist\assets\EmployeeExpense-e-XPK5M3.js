import{a0 as e,_ as a,a as l,r as t,o,c as r,e as n,R as d,f as u,k as s,S as i,j as m,T as p,K as c,E as g,m as v,h as y,z as h,U as f,n as b,V as w,Z as x,W as _,ac as N,t as k,Y as I,g as V,I as D,J as M,O as $}from"./index-BDR0Pmj6.js";import{g as C}from"./employee-DbFXp2d5.js";import{B as Y}from"./BatchExtendDialog-CsIAK1fn.js";const E="/employee-expense";function S(a){return e({url:E,method:"post",data:a})}const z={class:"expense-container"},U={class:"toolbar"},A={class:"search-box"},q={class:"action-box"},T={class:"operation-buttons"},B={class:"pagination-container"},j={class:"dialog-footer"},F=a({__name:"EmployeeExpense",setup(a){const F=l([]),L=l(!0),W=l(""),K=l(""),R=l(""),O=l([]),H=l(!1),J=l([]),Z=l([]),G=l(!1),P=l([]),Q=l(null),X=l([{label:"员工",prop:"employeeName",minWidth:"100px"},{label:"原费用年月",prop:"originalExpenseDate",width:"110px",align:"center"},{label:"项目名称",prop:"itemName",minWidth:"140px"},{label:"金额",prop:"amount",width:"100px",align:"right",isCurrency:!0},{label:"备注",prop:"remark",minWidth:"110px"}]),ee=t({page:1,size:10,total:0}),ae=l(!1),le=l("add"),te=l(null),oe=l(!1),re=t({id:null,employeeId:null,employeeIds:[],employeeName:"",expenseDate:"",selectedMonths:[],itemName:"",amount:0,remark:""}),ne={employeeId:[{required:!0,message:"请选择员工",trigger:"change"}],employeeIds:[{type:"array",required:!0,message:"请至少选择一个员工",trigger:"change"},{validator:(e,a,l)=>{a&&0!==a.length?l():l(new Error("请至少选择一个员工"))},trigger:"change"}],expenseDate:[{required:!0,message:"请选择月份",trigger:"change"}],selectedMonths:[{type:"array",required:!0,message:"请至少选择一个月份",trigger:"change"},{validator:(e,a,l)=>{a&&0!==a.length?l():l(new Error("请至少选择一个月份"))},trigger:"change"}],itemName:[{required:!0,message:"请输入项目名称",trigger:"blur"},{max:255,message:"项目名称不能超过255个字符",trigger:"blur"}],amount:[{required:!0,message:"请输入金额",trigger:"blur"},{type:"number",message:"金额必须为数字",trigger:"blur"},{validator:(e,a,l)=>{null==a?l(new Error("金额不能为空")):a<=0?l(new Error("金额必须大于0")):l()},trigger:"blur"}],remark:[{max:255,message:"备注不能超过255个字符",trigger:"blur"}]},de=()=>{const e=new Map,a=new Date;for(let l=0;l<12;l++){const t=new Date(a);t.setDate(1),t.setMonth(a.getMonth()-l);const o=`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}`;e.has(o)||e.set(o,{value:o,label:o})}for(let l=1;l<=12;l++){const t=new Date(a);t.setDate(1),t.setMonth(a.getMonth()+l);const o=`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}`;e.has(o)||e.set(o,{value:o,label:o})}J.value=Array.from(e.values()).sort(((e,a)=>a.value.localeCompare(e.value)))},ue=async e=>{if(e){H.value=!0;try{const a=await C({name:e,pageSize:20,pageNum:1});200===a.code?Array.isArray(a.data)?O.value=a.data:a.data&&Array.isArray(a.data.list)?O.value=a.data.list:a.data&&Array.isArray(a.data.records)?O.value=a.data.records:O.value=[]:g.error(a.message||"获取员工列表失败")}catch(a){g.error("加载员工列表失败: "+(a.message||"未知错误"))}finally{H.value=!1}}},se=async()=>{L.value=!0;try{let a=null,l=null;if(K.value){const e=K.value.substring(0,4),t=K.value.substring(5,7);a=`${e}-${t}-01`;l=`${e}-${t}-${new Date(e,t,0).getDate()}`}const t={pageNum:ee.page,pageSize:ee.size,employeeName:W.value||void 0,startDate:a||void 0,endDate:l||void 0,itemName:R.value||void 0},o=await function(a){return e({url:`${E}/page`,method:"get",params:a})}(t);200===o.code?(F.value=o.data.records||o.data.list||[],ee.total=o.data.total||0):g.error(o.message||"获取员工费用数据失败")}catch(a){g.error("加载员工费用数据失败: "+(a.message||"未知错误"))}finally{L.value=!1}},ie=()=>{te.value&&te.value.resetFields(),re.id=null,re.employeeId=null,re.employeeIds=[],re.employeeName="",re.expenseDate="",re.selectedMonths=[],re.itemName="",re.amount=0,re.remark="",O.value=[]},me=()=>{ae.value=!1,ie()},pe=()=>{le.value="add",ie(),(async()=>{H.value=!0;try{const e=await C({pageSize:100,pageNum:1});200===e.code?Array.isArray(e.data)?O.value=e.data:e.data&&Array.isArray(e.data.list)?O.value=e.data.list:e.data&&Array.isArray(e.data.records)?O.value=e.data.records:O.value=[]:g.error(e.message||"获取员工列表失败")}catch(e){g.error("加载员工列表失败: "+(e.message||"未知错误"))}finally{H.value=!1}})(),0===J.value.length&&de(),ae.value=!0},ce=async a=>{le.value="edit",ie();try{const t=await(l=a.id,e({url:`${E}/${l}`,method:"get"}));200===t.code&&t.data?(Object.assign(re,t.data),re.amount=Number(t.data.amount)||0,t.data.employeeId&&t.data.employeeName&&(O.value=[{employeeId:t.data.employeeId,name:t.data.employeeName}]),ae.value=!0):g.error(t.message||"获取费用详情失败")}catch(t){g.error("获取费用详情失败: "+(t.message||"未知错误"))}var l},ge=a=>{$.confirm(`确定要删除员工 "${a.employeeName}" 在 "${a.expenseDate}" 关于 "${a.itemName}" 的费用记录吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{L.value=!0;try{const t=await(l=a.id,e({url:`${E}/${l}`,method:"delete"}));200===t.code?(g.success("删除成功"),se()):g.error(t.message||"删除失败")}catch(t){g.error("删除失败: "+(t.message||"未知错误"))}finally{L.value=!1}var l})).catch((()=>{}))},ve=()=>{if(0===Z.value.length)return void g.warning("请选择要删除的记录");const a=Z.value.map((e=>e.id));$.confirm(`确定要批量删除选中的 ${a.length} 条费用记录吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{L.value=!0;try{const l=await function(a){return e({url:`${E}/batch-delete`,method:"post",data:a})}(a);200===l.code?(g.success("批量删除成功"),Z.value=[],se()):g.error(l.message||"批量删除失败")}catch(l){g.error("批量删除失败: "+(l.message||"未知错误"))}finally{L.value=!1}})).catch((()=>{}))},ye=async a=>{a&&await a.validate((async a=>{if(!a)return g.warning("请完善表单信息"),!1;oe.value=!0;try{let a;if("add"===le.value||"template"===le.value)if(re.employeeIds.length>0&&re.selectedMonths.length>0){const e={employeeIds:re.employeeIds,expenseMonths:re.selectedMonths,itemName:re.itemName,amount:Number(re.amount),remark:re.remark};a=await S(e)}else{const e={employeeId:re.employeeId,expenseDate:re.expenseDate,itemName:re.itemName,amount:Number(re.amount),remark:re.remark};a=await S(e)}else{const t={...re,amount:Number(re.amount)};a=await(l=t,e({url:E,method:"put",data:l}))}200===a.code?(g.success("edit"===le.value?"更新成功":"template"===le.value?"延用成功":"添加成功"),ae.value=!1,se()):g.error(a.message||("edit"===le.value?"更新失败":"template"===le.value?"延用失败":"添加失败"))}catch(t){g.error("提交失败: "+(t.message||"未知错误"))}finally{oe.value=!1}var l}))},he=e=>{Z.value=e},fe=()=>{ee.page=1,se()},be=()=>{W.value="",K.value="",R.value="",ee.page=1,se()},we=e=>{ee.page=e,se()},xe=e=>{ee.size=e,ee.page=1,se()},_e=e=>{if(!e)return"-";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},Ne=()=>{if(0===Z.value.length)return void g.warning("请至少选择一条费用记录进行延用");const e=new Set;for(const a of Z.value){const l=`${a.employeeId}_${a.itemName}`;if(e.has(l))return void g.error("不能同时选择相同员工、相同项目的不同月份费用进行延用。");e.add(l)}P.value=Z.value.map((e=>({originalId:e.id,employeeId:e.employeeId,employeeName:e.employeeName,originalExpenseDate:e.expenseDate?e.expenseDate.substring(0,7):"-",itemName:e.itemName,amount:Number(e.amount)||0,remark:e.remark||""}))),0===J.value.length&&de(),G.value=!0},ke=async a=>{if(!Q.value)return void g.error("批量延用组件引用错误");const l=[];var t;if(a.selectedItems.forEach((e=>{a.targetMonths.forEach((a=>{const t=`${a}-01`;l.push({employeeId:e.employeeId,employeeName:e.employeeName,expenseDate:t,itemName:e.itemName,amount:e.amount,remark:e.remark})}))})),0!==l.length)try{const a=await(t={items:l},e({url:`${E}/extend-batch`,method:"post",data:t}));if(200===a.code&&a.data){const{successfullyExtendedItems:e,skippedDuplicateItems:l}=a.data,t=e?e.length:0,o=l?l.length:0,r=`批量延用处理完成：成功 ${t} 项，失败/跳过 ${o} 项。`;if(t>0&&0===o?g.success(r):g.warning(r),o>0&&l){let e="<strong>被跳过或延用失败的项目详情：</strong><br/>";l.forEach((a=>{e+=`- ${a}<br/>`})),$.alert(e,"跳过项目详情",{confirmButtonText:"我知道了",type:"warning",dangerouslyUseHTMLString:!0,callback:()=>{}})}G.value=!1,se(),Z.value=[]}else g.error(a.message||"批量延用失败，未返回有效数据")}catch(o){g.error("批量延用操作失败: "+(o.message||"未知错误"))}finally{Q.value&&"function"==typeof Q.value.resetLoadingState&&Q.value.resetLoadingState()}else g.warning("未能构建任何有效的延用数据")};return o((()=>{se(),de()})),(e,a)=>{const l=m("el-icon"),t=m("el-input"),o=m("el-date-picker"),g=m("el-button"),$=m("el-table-column"),C=m("el-tag"),E=m("el-table"),S=m("el-pagination"),de=m("el-option"),se=m("el-select"),ie=m("el-form-item"),Ie=m("el-input-number"),Ve=m("el-form"),De=m("el-dialog"),Me=p("loading");return v(),r("div",z,[n("div",U,[n("div",A,[u(t,{modelValue:W.value,"onUpdate:modelValue":a[0]||(a[0]=e=>W.value=e),placeholder:"搜索员工姓名",clearable:"",onKeyup:i(fe,["enter"])},{prefix:s((()=>[u(l,null,{default:s((()=>[u(y(h))])),_:1})])),_:1},8,["modelValue"]),u(o,{modelValue:K.value,"onUpdate:modelValue":a[1]||(a[1]=e=>K.value=e),type:"month",placeholder:"选择月份",format:"YYYY-MM","value-format":"YYYY-MM",clearable:"",onChange:fe},null,8,["modelValue"]),u(t,{modelValue:R.value,"onUpdate:modelValue":a[2]||(a[2]=e=>R.value=e),placeholder:"搜索项目名称",clearable:"",onKeyup:i(fe,["enter"])},{prefix:s((()=>[u(l,null,{default:s((()=>[u(y(f))])),_:1})])),_:1},8,["modelValue"]),u(g,{type:"primary",onClick:fe},{default:s((()=>[u(l,null,{default:s((()=>[u(y(f))])),_:1}),a[14]||(a[14]=b("搜索"))])),_:1}),u(g,{onClick:be},{default:s((()=>[u(l,null,{default:s((()=>[u(y(w))])),_:1}),a[15]||(a[15]=b("重置"))])),_:1})]),n("div",q,[u(g,{type:"danger",disabled:0===Z.value.length,onClick:ve},{default:s((()=>[u(l,null,{default:s((()=>[u(y(x))])),_:1}),a[16]||(a[16]=b("批量删除 "))])),_:1},8,["disabled"]),u(g,{type:"primary",class:"add-btn",onClick:pe},{default:s((()=>[u(l,null,{default:s((()=>[u(y(_))])),_:1}),a[17]||(a[17]=b("添加费用 "))])),_:1}),u(g,{type:"success",disabled:0===Z.value.length,onClick:Ne},{default:s((()=>[u(l,null,{default:s((()=>[u(y(N))])),_:1}),a[18]||(a[18]=b("延用选中费用 "))])),_:1},8,["disabled"])])]),d((v(),c(E,{data:F.value,border:"","row-key":"id",onSelectionChange:he,"max-height":"calc(100vh - 220px)",class:"custom-table"},{default:s((()=>[u($,{type:"selection",width:"55",align:"center"}),u($,{label:"员工",prop:"employeeName","min-width":"150","show-overflow-tooltip":""}),u($,{label:"部门",prop:"departmentName","min-width":"150","show-overflow-tooltip":""}),u($,{label:"年月",prop:"expenseDate",width:"120",align:"center"},{default:s((({row:e})=>[u(C,{type:"info"},{default:s((()=>[b(k(e.expenseDate?e.expenseDate.substring(0,7):"-"),1)])),_:2},1024)])),_:1}),u($,{label:"项目名称",prop:"itemName","min-width":"200","show-overflow-tooltip":""}),u($,{label:"金额",prop:"amount","min-width":"120",align:"right","show-overflow-tooltip":""},{default:s((({row:e})=>{return[b(k((a=e.amount,null==a?"¥0.00":"¥"+parseFloat(a).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","))),1)];var a})),_:1}),u($,{label:"备注",prop:"remark","min-width":"180","show-overflow-tooltip":""},{default:s((({row:e})=>[b(k(e.remark||"-"),1)])),_:1}),u($,{label:"创建时间",prop:"createTime",width:"180",align:"center","show-overflow-tooltip":""},{default:s((({row:e})=>[b(k(_e(e.createTime)),1)])),_:1}),u($,{label:"更新时间",prop:"updateTime",width:"180",align:"center","show-overflow-tooltip":""},{default:s((({row:e})=>[b(k(_e(e.updateTime)),1)])),_:1}),u($,{label:"操作",width:"190",align:"center",fixed:"right","class-name":"operation-column"},{default:s((({row:e})=>[n("div",T,[u(g,{class:"edit-btn",onClick:a=>ce(e),title:"编辑"},{default:s((()=>[u(l,null,{default:s((()=>[u(y(I))])),_:1})])),_:2},1032,["onClick"]),u(g,{class:"delete-btn",onClick:a=>ge(e),title:"删除"},{default:s((()=>[u(l,null,{default:s((()=>[u(y(x))])),_:1})])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[Me,L.value]]),n("div",B,[u(S,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":ee.page,"page-size":ee.size,total:ee.total,"page-sizes":[10,20,50,100],onSizeChange:xe,onCurrentChange:we},null,8,["current-page","page-size","total"])]),u(De,{modelValue:ae.value,"onUpdate:modelValue":a[12]||(a[12]=e=>ae.value=e),title:"add"===le.value||"template"===le.value?"添加员工费用":"编辑员工费用",width:"500px","destroy-on-close":"",class:"custom-dialog","close-on-click-modal":!1},{default:s((()=>[u(Ve,{ref_key:"formRef",ref:te,model:re,rules:ne,"label-width":"100px",class:"dialog-form"},{default:s((()=>["edit"===le.value?(v(),c(ie,{key:0,label:"选择员工",prop:"employeeId",required:""},{default:s((()=>[u(se,{modelValue:re.employeeId,"onUpdate:modelValue":a[3]||(a[3]=e=>re.employeeId=e),placeholder:"输入员工姓名搜索",filterable:"",remote:"","remote-method":ue,loading:H.value,style:{width:"100%"},onChange:a[4]||(a[4]=e=>{const a=O.value.find((a=>a.employeeId===e));a&&(re.employeeName=a.name)})},{default:s((()=>[(v(!0),r(D,null,M(O.value,(e=>(v(),c(de,{key:e.employeeId,label:e.name+(e.departmentName?" ("+e.departmentName+")":""),value:e.employeeId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})):V("",!0),"add"===le.value||"template"===le.value?(v(),c(ie,{key:1,label:"选择员工",prop:"employeeIds",required:""},{default:s((()=>[u(se,{modelValue:re.employeeIds,"onUpdate:modelValue":a[5]||(a[5]=e=>re.employeeIds=e),placeholder:"请选择一个或多个员工",filterable:"",multiple:"",clearable:"",loading:H.value,style:{width:"100%"}},{default:s((()=>[(v(!0),r(D,null,M(O.value,(e=>(v(),c(de,{key:e.employeeId,label:e.name+(e.departmentName?" ("+e.departmentName+")":""),value:e.employeeId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})):V("",!0),"edit"===le.value?(v(),c(ie,{key:2,label:"月份",prop:"expenseDate",required:""},{default:s((()=>[u(o,{modelValue:re.expenseDate,"onUpdate:modelValue":a[6]||(a[6]=e=>re.expenseDate=e),type:"month",placeholder:"选择月份",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"100%"},disabled:"edit"===le.value&&null!==re.id},null,8,["modelValue","disabled"])])),_:1})):V("",!0),"add"===le.value||"template"===le.value?(v(),c(ie,{key:3,label:"月份",prop:"selectedMonths",required:""},{default:s((()=>[u(se,{modelValue:re.selectedMonths,"onUpdate:modelValue":a[7]||(a[7]=e=>re.selectedMonths=e),multiple:"",filterable:"",placeholder:"请选择一个或多个月份",style:{width:"100%"},clearable:""},{default:s((()=>[(v(!0),r(D,null,M(J.value,(e=>(v(),c(de,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})):V("",!0),u(ie,{label:"项目名称",prop:"itemName",required:""},{default:s((()=>[u(t,{modelValue:re.itemName,"onUpdate:modelValue":a[8]||(a[8]=e=>re.itemName=e),placeholder:"请输入项目名称"},null,8,["modelValue"])])),_:1}),u(ie,{label:"金额",prop:"amount",required:""},{default:s((()=>[u(Ie,{modelValue:re.amount,"onUpdate:modelValue":a[9]||(a[9]=e=>re.amount=e),precision:2,step:50,min:.01,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),u(ie,{label:"备注",prop:"remark"},{default:s((()=>[u(t,{modelValue:re.remark,"onUpdate:modelValue":a[10]||(a[10]=e=>re.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息 (可选)",maxlength:"255","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),n("div",j,[u(g,{onClick:me},{default:s((()=>a[19]||(a[19]=[b("取消")]))),_:1}),u(g,{type:"primary",loading:oe.value,onClick:a[11]||(a[11]=e=>ye(te.value))},{default:s((()=>a[20]||(a[20]=[b("确定")]))),_:1},8,["loading"])])])),_:1},8,["modelValue","title"]),u(Y,{ref_key:"batchExtendDialogRef",ref:Q,isVisible:G.value,"onUpdate:isVisible":a[13]||(a[13]=e=>G.value=e),"items-to-extend":P.value,"month-options":J.value,"entity-name":"员工费用","item-key-field":"originalId","item-display-fields":X.value,onSubmitExtend:ke},null,8,["isVisible","items-to-extend","month-options","item-display-fields"])])}}},[["__scopeId","data-v-f518890b"]]);export{F as default};
