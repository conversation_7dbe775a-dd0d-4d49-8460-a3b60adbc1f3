import{_ as s,r as a,a as e,o,u as t,c as r,b as i,d as l,w as n,e as d,f as p,g as c,h as u,i as v,t as h,l as m,j as g,k as f,m as w,n as b,E as y,p as V}from"./index-CGqeVPF3.js";import{e as x,t as j}from"./notification-DJy6sAcV.js";import{u as k}from"./token-Ced5ba2J.js";import"./request-Cm8Ap7dD.js";const H={class:"login-container"},_={class:"login-box"},z={class:"input-group"},L={key:0,class:"error-message"},q={class:"input-group"},B={key:0,class:"error-message"},C=s({__name:"Login",setup(s){const C=t(),I=f(),M=k(),A=a({phone:"",password:""}),E=a({phone:!1,password:!1,form:!1}),T=e(!1),U=e(!1),Z=s=>{const a=/[A-Z]/.test(s),e=/[a-z]/.test(s),o=/\d/.test(s);return s.length>=8&&a&&e&&o},S=()=>(E.phone=!0,A.phone?"":"请输入手机号"),D=()=>(E.password=!0,A.password?Z(A.password)?"":"密码必须至少8位，包含大小写字母和数字":"请输入密码"),F=()=>E.phone?S():"",G=()=>E.password?D():"",J=async()=>{if((()=>{E.form=!0,E.phone=!0,E.password=!0,A.phone;const s=Z(A.password);return U.value=s,U.value})()){T.value=!0,M.setLoading(!0),M.setError(null);try{const s=await x(A);200===s.code?(M.setToken(s.data),j().then((()=>{})).catch((s=>{})),y({message:"登录成功，欢迎回来！",type:"success",duration:3e3,showClose:!0}),V({title:"登录成功",message:"欢迎回到中航信息系统！",type:"success",duration:4e3,position:"top-right"}),setTimeout((()=>{const s=I.query.redirect||"/dashboard";C.replace({path:s,query:{fromLogin:"true"}})}),1e3)):y.error(s.message||"登录失败，请检查账号密码")}catch(s){y.error(s.message||"登录失败，请检查网络连接或账号密码")}finally{T.value=!1,M.setLoading(!1)}}else y.error("请正确填写所有必填项")};return o((()=>{M.isAuthenticated&&C.push("/dashboard")})),(s,a)=>{const e=v("el-input"),o=v("el-button");return w(),r("div",H,[a[8]||(a[8]=i('<div class="bg-animation" data-v-4a847988><div class="cube" data-v-4a847988></div><div class="cube" data-v-4a847988></div><div class="cube" data-v-4a847988></div><div class="cube" data-v-4a847988></div><div class="cube" data-v-4a847988></div></div>',1)),l("div",_,[a[5]||(a[5]=l("div",{class:"logo"},[l("svg",{viewBox:"0 0 24 24",width:"48",height:"48",fill:"currentColor"},[l("path",{d:"M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z"})])],-1)),a[6]||(a[6]=l("h1",{class:"title"},"中航信息平台",-1)),l("form",{onSubmit:n(J,["prevent"]),class:"login-form"},[l("div",z,[d(e,{modelValue:A.phone,"onUpdate:modelValue":a[0]||(a[0]=s=>A.phone=s),placeholder:"手机号","prefix-icon":c(u),size:"large",onInput:S,onBlur:a[1]||(a[1]=s=>E.phone=!0),clearable:""},null,8,["modelValue","prefix-icon"]),F()?(w(),r("div",L,h(F()),1)):p("",!0)]),l("div",q,[d(e,{modelValue:A.password,"onUpdate:modelValue":a[2]||(a[2]=s=>A.password=s),type:"password",placeholder:"密码","prefix-icon":c(m),"show-password":"",size:"large",onInput:D,onBlur:a[3]||(a[3]=s=>E.password=!0),clearable:""},null,8,["modelValue","prefix-icon"]),G()?(w(),r("div",B,h(G()),1)):p("",!0)]),d(o,{type:"primary","native-type":"submit",class:"login-button",size:"large",loading:T.value},{default:g((()=>a[4]||(a[4]=[b("登录")]))),_:1},8,["loading"])],32),a[7]||(a[7]=l("div",{class:"footer-text"}," Copyright © 2025 中航信息系统 ",-1))])])}}},[["__scopeId","data-v-4a847988"]]);export{C as default};
