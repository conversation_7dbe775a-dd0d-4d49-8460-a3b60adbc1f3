import{a0 as e,_ as l,a,r as t,o as i,c as n,e as s,R as o,f as u,k as r,S as d,j as c,T as v,K as m,E as p,m as y,h as g,U as h,n as f,V as w,Z as b,t as k,X as _,Y as C,g as V,I as D,J as x,a1 as I,O as N}from"./index-BDR0Pmj6.js";function R(l={}){return e({url:"/sales-report/my-clients",method:"get",params:l})}function L(l,a={}){return e({url:`/sales-report/admin/employee-clients/${l}`,method:"get",params:a})}function U(l){return e({url:`/sales-report/admin/report-detail/${l}`,method:"get"})}const S={class:"department-content-container"},z={class:"toolbar"},E={class:"search-section"},q={class:"search-row"},O={class:"action-buttons"},T={class:"client-stats"},Y={class:"stat-row"},M={class:"value"},$={class:"value"},B={class:"stat-row"},J={class:"value"},j={key:0,class:"evaluation-text"},P={key:1,class:"no-evaluation"},K={class:"operation-buttons"},F={class:"pagination-container"},X={key:0,class:"report-detail"},Z={class:"detail-section"},A={class:"info-grid"},G={class:"info-item"},H={class:"info-item"},Q={class:"info-item"},W={class:"info-item"},ee={class:"detail-section"},le={class:"stats-row"},ae={class:"stat-box"},te={class:"stat-number"},ie={class:"stat-box"},ne={class:"stat-number"},se={class:"stat-box"},oe={class:"stat-number"},ue={class:"detail-section"},re={class:"client-info"},de={class:"client-category"},ce={class:"client-list"},ve={key:0,class:"no-data"},me={class:"client-category"},pe={class:"client-list"},ye={key:0,class:"no-data"},ge={class:"client-category"},he={class:"client-list"},fe={key:0,class:"no-data"},we={key:0,class:"detail-section"},be={class:"checklist-display"},ke={class:"checklist-items"},_e={class:"detail-section"},Ce={class:"work-records"},Ve={class:"record-item"},De={class:"record-content"},xe={class:"record-item"},Ie={class:"record-content"},Ne={class:"record-item"},Re={class:"record-content"},Le={class:"detail-section"},Ue={class:"evaluation-content"},Se={key:0,class:"evaluation-text"},ze={key:1,class:"evaluation-info"},Ee={class:"evaluation-time"},qe={key:0,class:"evaluator-name"},Oe={key:2,class:"no-evaluation"},Te={class:"form-section"},Ye={class:"form-section"},Me={class:"form-section"},$e={class:"form-section"},Be={class:"form-section"},Je={class:"form-section"},je={class:"dialog-footer"},Pe=l({__name:"SalesReportManagementBasic",setup(l){const Pe=a(!1),Ke=a([]),Fe=a(!1),Xe=a(null),Ze=a([]),Ae=a(!1),Ge=a(null),He=a(null),Qe=a(!1),We=a([]),el=a(!1),ll=new Map,al=a(1),tl=a(10),il=a(0),nl=t({employeeName:"",dateRange:[],responsibilityLevel:""}),sl={responsibilityLevel:[{required:!0,message:"请选择责任心评级",trigger:"change"}],dailyResults:[{min:1,max:2e3,message:"今日效果长度必须在1-2000字符之间",trigger:"blur"}],meetingReport:[{min:1,max:2e3,message:"会议报告长度必须在1-2000字符之间",trigger:"blur"}],workDiary:[{min:1,max:2e3,message:"工作日记长度必须在1-2000字符之间",trigger:"blur"}]},ol=async()=>{try{Pe.value=!0;const l={pageNum:al.value,pageSize:tl.value};nl.employeeName&&(l.employeeName=nl.employeeName),nl.dateRange&&2===nl.dateRange.length&&(l.startDate=nl.dateRange[0],l.endDate=nl.dateRange[1]),nl.responsibilityLevel&&(l.responsibilityLevel=nl.responsibilityLevel);const a=await function(l){return e({url:"/sales-report/page",method:"get",params:l})}(l);200===a.code&&(Ke.value=a.data.list||[],il.value=a.data.total||0)}catch(l){p.error("获取日报列表失败")}finally{Pe.value=!1}},ul=e=>{switch(e){case"优秀":return"success";case"中等":return"warning";case"差":return"danger";default:return"info"}},rl=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric"}),dl=e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),cl=()=>{al.value=1,ol()},vl=()=>{nl.employeeName="",nl.dateRange=[],nl.responsibilityLevel="",al.value=1,ol()},ml=e=>{tl.value=e,al.value=1,ol()},pl=e=>{al.value=e,ol()},yl=e=>{Ze.value=e},gl=async l=>{try{await N.confirm(`确定要删除 ${l.employeeName} 在 ${t=l.reportDate,new Date(t).toLocaleDateString("zh-CN",{month:"short",day:"numeric"})} 的日报吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});200===(await(a=l.id,e({url:`/sales-report/${a}`,method:"delete"}))).code&&(p.success("删除成功"),ol())}catch(i){"cancel"!==i&&p.error("删除失败")}var a,t},hl=async()=>{try{await N.confirm(`确定要删除选中的 ${Ze.value.length} 条日报吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=Ze.value.map((e=>e.id)),a=await function(l){return e({url:"/sales-report/batch-delete",method:"delete",data:{ids:l}})}(l);200===a.code&&(p.success("批量删除成功"),Ze.value=[],ol())}catch(l){"cancel"!==l&&p.error("批量删除失败")}},fl=async(e=null)=>{const l=e||"current";if(ll.has(l))We.value=ll.get(l);else try{let a;if(el.value=!0,a=e?await L(e,{limit:100}):await R({limit:100}),200===a.code){const e=a.data||[];We.value=e,ll.set(l,e)}}catch(a){p.error("获取客户列表失败")}finally{el.value=!1}},wl=async(e,l=null)=>{if(!e){const e=l||"current";return void(ll.has(e)&&(We.value=ll.get(e)))}const a=l||"current";if(ll.has(a)){const l=ll.get(a).filter((l=>l.name&&l.name.toLowerCase().includes(e.toLowerCase())));if(l.length>0)return void(We.value=l)}try{let a;el.value=!0,a=l?await L(l,{keyword:e,limit:10}):await R({keyword:e,limit:10}),200===a.code&&(We.value=a.data||[])}catch(t){}finally{el.value=!1}},bl=async()=>{try{await He.value.validate(),Qe.value=!0;const a={id:Ge.value.id,employeeId:Ge.value.employeeId,reportDate:Ge.value.reportDate,inquiryClientIds:Ge.value.inquiryClientIds,shippingClientIds:Ge.value.shippingClientIds,keyDevelopmentClientIds:Ge.value.keyDevelopmentClientIds,responsibilityLevel:Ge.value.responsibilityLevel,endOfDayChecklist:Ge.value.endOfDayChecklistItems,dailyResults:Ge.value.dailyResults,meetingReport:Ge.value.meetingReport,workDiary:Ge.value.workDiary,managerEvaluation:Ge.value.managerEvaluation},t=await(l=a,e({url:"/sales-report/department/edit",method:"put",data:l}));200===t.code?(p.success("日报编辑成功"),Ae.value=!1,ol()):p.error(t.message||"编辑失败")}catch(a){p.error("编辑失败")}finally{Qe.value=!1}var l},kl=e=>{try{if(!e)return[];return("string"==typeof e?JSON.parse(e):e).clientIds||[]}catch(l){return[]}},_l=e=>{try{if(!e)return[];return("string"==typeof e?JSON.parse(e):e).items||[]}catch(l){return[]}},Cl=(e,l)=>{if(!e)return[];let a=[];try{switch(l){case"inquiry":a=kl(e.inquiryClients);break;case"shipping":a=kl(e.shippingClients);break;case"keyDevelopment":a=kl(e.keyDevelopmentClients)}return a.map((e=>{const l=We.value.find((l=>l.id===e));return l?{id:e,name:l.name}:{id:e,name:null}}))}catch(t){return[]}},Vl=e=>e?(e=>{try{return e&&JSON.parse(e).items||[]}catch(l){return[]}})(e.endOfDayChecklist):[];return i((()=>{ol()})),(e,l)=>{const a=c("el-icon"),t=c("el-input"),i=c("el-date-picker"),N=c("el-option"),R=c("el-select"),L=c("el-button"),ll=c("el-table-column"),ol=c("el-tag"),Dl=c("el-table"),xl=c("el-pagination"),Il=c("el-dialog"),Nl=c("el-form-item"),Rl=c("el-col"),Ll=c("el-row"),Ul=c("el-radio"),Sl=c("el-radio-group"),zl=c("el-checkbox"),El=c("el-checkbox-group"),ql=c("el-form"),Ol=v("loading");return y(),n("div",S,[s("div",z,[s("div",E,[s("div",q,[u(t,{modelValue:nl.employeeName,"onUpdate:modelValue":l[0]||(l[0]=e=>nl.employeeName=e),placeholder:"搜索员工姓名",clearable:"",style:{width:"160px"},onKeyup:d(cl,["enter"]),onClear:cl},{prefix:r((()=>[u(a,null,{default:r((()=>[u(g(h))])),_:1})])),_:1},8,["modelValue"]),u(i,{modelValue:nl.dateRange,"onUpdate:modelValue":l[1]||(l[1]=e=>nl.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"220px"},onChange:cl},null,8,["modelValue"]),u(R,{modelValue:nl.responsibilityLevel,"onUpdate:modelValue":l[2]||(l[2]=e=>nl.responsibilityLevel=e),placeholder:"责任心评级",clearable:"",style:{width:"120px"},onChange:cl},{default:r((()=>[u(N,{label:"优秀",value:"优秀"}),u(N,{label:"中等",value:"中等"}),u(N,{label:"差",value:"差"})])),_:1},8,["modelValue"]),u(L,{type:"primary",onClick:cl},{default:r((()=>[u(a,null,{default:r((()=>[u(g(h))])),_:1}),l[19]||(l[19]=f("搜索 "))])),_:1}),u(L,{onClick:vl},{default:r((()=>[u(a,null,{default:r((()=>[u(g(w))])),_:1}),l[20]||(l[20]=f("重置 "))])),_:1})])]),s("div",O,[u(L,{type:"danger",onClick:hl,disabled:0===Ze.value.length},{default:r((()=>[u(a,null,{default:r((()=>[u(g(b))])),_:1}),l[21]||(l[21]=f(" 批量删除 "))])),_:1},8,["disabled"])])]),o((y(),m(Dl,{data:Ke.value,border:"","row-key":"id","max-height":"calc(100vh - 220px)",class:"custom-table","header-cell-style":{background:"#f7f7f7",color:"#606266"},onSelectionChange:yl},{default:r((()=>[u(ll,{type:"selection",width:"55",align:"center"}),u(ll,{type:"index",width:"60",align:"center",label:"序号","class-name":"index-column"}),u(ll,{prop:"employeeName",label:"员工姓名",width:"100",align:"center"}),u(ll,{prop:"departmentName",label:"部门",width:"120",align:"center"}),u(ll,{prop:"reportDate",label:"日报日期",width:"140",align:"center",sortable:""},{default:r((({row:e})=>[u(ol,{type:"info",effect:"plain"},{default:r((()=>[f(k(rl(e.reportDate)),1)])),_:2},1024)])),_:1}),u(ll,{label:"客户统计",width:"180",align:"center"},{default:r((({row:e})=>[s("div",T,[s("div",Y,[l[22]||(l[22]=s("span",{class:"label"},"年度:",-1)),s("span",M,k(e.yearlyNewClients||0),1),l[23]||(l[23]=s("span",{class:"label"},"月度:",-1)),s("span",$,k(e.monthlyNewClients||0),1)]),s("div",B,[l[24]||(l[24]=s("span",{class:"label"},"距上次:",-1)),s("span",J,k(e.daysSinceLastNewClient||0)+"天",1)])])])),_:1}),u(ll,{prop:"responsibilityLevel",label:"责任心评级",width:"120",align:"center"},{default:r((({row:e})=>[u(ol,{type:ul(e.responsibilityLevel),effect:"light"},{default:r((()=>[f(k(e.responsibilityLevel),1)])),_:2},1032,["type"])])),_:1}),u(ll,{prop:"dailyResults",label:"今日效果","min-width":"200","show-overflow-tooltip":"",align:"center"}),u(ll,{prop:"managerEvaluation",label:"评价","min-width":"150","show-overflow-tooltip":"",align:"center"},{default:r((({row:e})=>[e.managerEvaluation?(y(),n("span",j,k(e.managerEvaluation),1)):(y(),n("span",P,"暂无评价"))])),_:1}),u(ll,{prop:"createTime",label:"提交时间",width:"180",align:"center",sortable:""},{default:r((({row:e})=>[f(k(dl(e.createTime)),1)])),_:1}),u(ll,{label:"操作",fixed:"right","min-width":"220",align:"center"},{default:r((({row:e})=>[s("div",K,[u(L,{type:"primary",size:"small",onClick:l=>(async e=>{try{await fl(e.employeeId);const l=await U(e.id);200===l.code?Xe.value=l.data:Xe.value=e,Fe.value=!0}catch(l){p.error("获取日报详情失败"),Xe.value=e,Fe.value=!0}})(e),title:"查看详情"},{default:r((()=>[u(a,null,{default:r((()=>[u(g(_))])),_:1}),l[25]||(l[25]=f("查看 "))])),_:2},1032,["onClick"]),u(L,{type:"warning",size:"small",onClick:l=>(async e=>{try{await fl(e.employeeId);let a=e;try{const l=await U(e.id);200===l.code&&(a=l.data)}catch(l){}Ge.value={id:a.id,employeeId:a.employeeId,employeeName:a.employeeName,reportDate:a.reportDate,inquiryClientIds:kl(a.inquiryClients),shippingClientIds:kl(a.shippingClients),keyDevelopmentClientIds:kl(a.keyDevelopmentClients),responsibilityLevel:a.responsibilityLevel,endOfDayChecklistItems:_l(a.endOfDayChecklist),dailyResults:a.dailyResults,meetingReport:a.meetingReport,workDiary:a.workDiary,managerEvaluation:a.managerEvaluation||""},Ae.value=!0}catch(l){p.error("编辑日报初始化失败")}})(e),title:"编辑日报"},{default:r((()=>[u(a,null,{default:r((()=>[u(g(C))])),_:1}),l[26]||(l[26]=f("编辑 "))])),_:2},1032,["onClick"]),u(L,{type:"danger",size:"small",onClick:l=>gl(e),title:"删除日报"},{default:r((()=>[u(a,null,{default:r((()=>[u(g(b))])),_:1}),l[27]||(l[27]=f("删除 "))])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[Ol,Pe.value]]),s("div",F,[u(xl,{background:"",layout:"total, sizes, prev, pager, next, jumper",currentPage:al.value,"onUpdate:currentPage":l[3]||(l[3]=e=>al.value=e),"page-size":tl.value,"onUpdate:pageSize":l[4]||(l[4]=e=>tl.value=e),total:il.value,"page-sizes":[10,20,50,100],onSizeChange:ml,onCurrentChange:pl},null,8,["currentPage","page-size","total"])]),u(Il,{modelValue:Fe.value,"onUpdate:modelValue":l[5]||(l[5]=e=>Fe.value=e),title:"日报详情",width:"800px","destroy-on-close":"",class:"report-detail-dialog"},{default:r((()=>[Xe.value?(y(),n("div",X,[s("div",Z,[l[32]||(l[32]=s("h3",null,"员工信息",-1)),s("div",A,[s("div",G,[l[28]||(l[28]=s("label",null,"员工姓名:",-1)),s("span",null,k(Xe.value.employeeName),1)]),s("div",H,[l[29]||(l[29]=s("label",null,"日报日期:",-1)),s("span",null,k(rl(Xe.value.reportDate)),1)]),s("div",Q,[l[30]||(l[30]=s("label",null,"责任心评级:",-1)),u(ol,{type:ul(Xe.value.responsibilityLevel)},{default:r((()=>[f(k(Xe.value.responsibilityLevel),1)])),_:1},8,["type"])]),s("div",W,[l[31]||(l[31]=s("label",null,"提交时间:",-1)),s("span",null,k(dl(Xe.value.createTime)),1)])])]),s("div",ee,[l[36]||(l[36]=s("h3",null,"客户统计",-1)),s("div",le,[s("div",ae,[s("div",te,k(Xe.value.yearlyNewClients||0),1),l[33]||(l[33]=s("div",{class:"stat-desc"},"年度新客户",-1))]),s("div",ie,[s("div",ne,k(Xe.value.monthlyNewClients||0),1),l[34]||(l[34]=s("div",{class:"stat-desc"},"当月新客户",-1))]),s("div",se,[s("div",oe,k(Xe.value.daysSinceLastNewClient||0),1),l[35]||(l[35]=s("div",{class:"stat-desc"},"距上次新客户天数",-1))])])]),s("div",ue,[l[40]||(l[40]=s("h3",null,"客户信息",-1)),s("div",re,[s("div",de,[l[37]||(l[37]=s("h4",null,"询价客户",-1)),s("div",ce,[(y(!0),n(D,null,x(Cl(Xe.value,"inquiry"),(e=>(y(),m(ol,{key:e.id,type:"info",class:"client-tag"},{default:r((()=>[f(k(e.name||e.id),1)])),_:2},1024)))),128)),Cl(Xe.value,"inquiry").length?V("",!0):(y(),n("span",ve,"暂无"))])]),s("div",me,[l[38]||(l[38]=s("h4",null,"出货客户",-1)),s("div",pe,[(y(!0),n(D,null,x(Cl(Xe.value,"shipping"),(e=>(y(),m(ol,{key:e.id,type:"success",class:"client-tag"},{default:r((()=>[f(k(e.name||e.id),1)])),_:2},1024)))),128)),Cl(Xe.value,"shipping").length?V("",!0):(y(),n("span",ye,"暂无"))])]),s("div",ge,[l[39]||(l[39]=s("h4",null,"重点开发客户",-1)),s("div",he,[(y(!0),n(D,null,x(Cl(Xe.value,"keyDevelopment"),(e=>(y(),m(ol,{key:e.id,type:"warning",class:"client-tag"},{default:r((()=>[f(k(e.name||e.id),1)])),_:2},1024)))),128)),Cl(Xe.value,"keyDevelopment").length?V("",!0):(y(),n("span",fe,"暂无"))])])])]),Vl(Xe.value).length>0?(y(),n("div",we,[l[41]||(l[41]=s("h3",null,"下班准备工作",-1)),s("div",be,[s("div",ke,[(y(!0),n(D,null,x(Vl(Xe.value),(e=>(y(),n("div",{key:e,class:"checklist-item"},[u(a,{class:"check-icon"},{default:r((()=>[u(g(I))])),_:1}),s("span",null,k(e),1)])))),128))])])])):V("",!0),s("div",_e,[l[45]||(l[45]=s("h3",null,"工作记录",-1)),s("div",Ce,[s("div",Ve,[l[42]||(l[42]=s("h4",null,"今日效果",-1)),s("div",De,k(Xe.value.dailyResults||"暂无记录"),1)]),s("div",xe,[l[43]||(l[43]=s("h4",null,"会议报告",-1)),s("div",Ie,k(Xe.value.meetingReport||"暂无记录"),1)]),s("div",Ne,[l[44]||(l[44]=s("h4",null,"工作日记",-1)),s("div",Re,k(Xe.value.workDiary||"暂无记录"),1)])])]),s("div",Le,[l[46]||(l[46]=s("h3",null,"领导评价",-1)),s("div",Ue,[Xe.value.managerEvaluation?(y(),n("div",Se,k(Xe.value.managerEvaluation),1)):V("",!0),Xe.value.evaluationTime?(y(),n("div",ze,[s("span",Ee,"评价时间："+k(dl(Xe.value.evaluationTime)),1),Xe.value.evaluatorName?(y(),n("span",qe,"评价人："+k(Xe.value.evaluatorName),1)):V("",!0)])):V("",!0),Xe.value.managerEvaluation?V("",!0):(y(),n("div",Oe," 暂无评价 "))])])])):V("",!0)])),_:1},8,["modelValue"]),u(Il,{modelValue:Ae.value,"onUpdate:modelValue":l[18]||(l[18]=e=>Ae.value=e),title:"编辑日报",width:"900px","destroy-on-close":"",class:"edit-report-dialog"},{footer:r((()=>[s("div",je,[u(L,{onClick:l[17]||(l[17]=e=>Ae.value=!1)},{default:r((()=>l[61]||(l[61]=[f("取消")]))),_:1}),u(L,{type:"primary",onClick:bl,loading:Qe.value},{default:r((()=>l[62]||(l[62]=[f(" 保存 ")]))),_:1},8,["loading"])])])),default:r((()=>[Ge.value?(y(),m(ql,{key:0,ref_key:"editFormRef",ref:He,model:Ge.value,rules:sl,"label-width":"120px",class:"edit-form"},{default:r((()=>[s("div",Te,[l[47]||(l[47]=s("h3",null,"基本信息",-1)),u(Ll,{gutter:20},{default:r((()=>[u(Rl,{span:12},{default:r((()=>[u(Nl,{label:"员工姓名"},{default:r((()=>[u(t,{modelValue:Ge.value.employeeName,"onUpdate:modelValue":l[6]||(l[6]=e=>Ge.value.employeeName=e),disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),u(Rl,{span:12},{default:r((()=>[u(Nl,{label:"日报日期"},{default:r((()=>[u(t,{modelValue:Ge.value.reportDate,"onUpdate:modelValue":l[7]||(l[7]=e=>Ge.value.reportDate=e),disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})]),s("div",Ye,[l[48]||(l[48]=s("h3",null,"客户选择",-1)),u(Ll,{gutter:20},{default:r((()=>[u(Rl,{span:8},{default:r((()=>[u(Nl,{label:"询价客户"},{default:r((()=>[u(R,{modelValue:Ge.value.inquiryClientIds,"onUpdate:modelValue":l[8]||(l[8]=e=>Ge.value.inquiryClientIds=e),multiple:"",filterable:"",remote:"",placeholder:"选择询价客户","remote-method":e=>wl(e,Ge.value.employeeId),loading:el.value,style:{width:"100%"}},{default:r((()=>[(y(!0),n(D,null,x(We.value,(e=>(y(),m(N,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","remote-method","loading"])])),_:1})])),_:1}),u(Rl,{span:8},{default:r((()=>[u(Nl,{label:"出货客户"},{default:r((()=>[u(R,{modelValue:Ge.value.shippingClientIds,"onUpdate:modelValue":l[9]||(l[9]=e=>Ge.value.shippingClientIds=e),multiple:"",filterable:"",remote:"",placeholder:"选择出货客户","remote-method":e=>wl(e,Ge.value.employeeId),loading:el.value,style:{width:"100%"}},{default:r((()=>[(y(!0),n(D,null,x(We.value,(e=>(y(),m(N,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","remote-method","loading"])])),_:1})])),_:1}),u(Rl,{span:8},{default:r((()=>[u(Nl,{label:"重点开发客户"},{default:r((()=>[u(R,{modelValue:Ge.value.keyDevelopmentClientIds,"onUpdate:modelValue":l[10]||(l[10]=e=>Ge.value.keyDevelopmentClientIds=e),multiple:"",filterable:"",remote:"",placeholder:"选择重点开发客户","remote-method":e=>wl(e,Ge.value.employeeId),loading:el.value,style:{width:"100%"}},{default:r((()=>[(y(!0),n(D,null,x(We.value,(e=>(y(),m(N,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","remote-method","loading"])])),_:1})])),_:1})])),_:1})]),s("div",Me,[l[52]||(l[52]=s("h3",null,"责任心评级",-1)),u(Nl,{label:"责任心评级",prop:"responsibilityLevel"},{default:r((()=>[u(Sl,{modelValue:Ge.value.responsibilityLevel,"onUpdate:modelValue":l[11]||(l[11]=e=>Ge.value.responsibilityLevel=e)},{default:r((()=>[u(Ul,{value:"优秀"},{default:r((()=>l[49]||(l[49]=[f("优秀")]))),_:1}),u(Ul,{value:"中等"},{default:r((()=>l[50]||(l[50]=[f("中等")]))),_:1}),u(Ul,{value:"差"},{default:r((()=>l[51]||(l[51]=[f("差")]))),_:1})])),_:1},8,["modelValue"])])),_:1})]),s("div",$e,[l[58]||(l[58]=s("h3",null,"下班准备",-1)),u(Nl,{label:"检查清单"},{default:r((()=>[u(El,{modelValue:Ge.value.endOfDayChecklistItems,"onUpdate:modelValue":l[12]||(l[12]=e=>Ge.value.endOfDayChecklistItems=e)},{default:r((()=>[u(zl,{value:"整理完桌面"},{default:r((()=>l[53]||(l[53]=[f("整理完桌面")]))),_:1}),u(zl,{value:"整理完50通预计电话邮件上级"},{default:r((()=>l[54]||(l[54]=[f("整理完50通预计电话邮件上级")]))),_:1}),u(zl,{value:"会议已开完"},{default:r((()=>l[55]||(l[55]=[f("会议已开完")]))),_:1}),u(zl,{value:"准备好明天工作资料"},{default:r((()=>l[56]||(l[56]=[f("准备好明天工作资料")]))),_:1}),u(zl,{value:"问候领导后打卡离开"},{default:r((()=>l[57]||(l[57]=[f("问候领导后打卡离开")]))),_:1})])),_:1},8,["modelValue"])])),_:1})]),s("div",Be,[l[59]||(l[59]=s("h3",null,"工作记录",-1)),u(Nl,{label:"今日效果",prop:"dailyResults"},{default:r((()=>[u(t,{modelValue:Ge.value.dailyResults,"onUpdate:modelValue":l[13]||(l[13]=e=>Ge.value.dailyResults=e),type:"textarea",rows:3,placeholder:"请输入今日工作效果",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])])),_:1}),u(Nl,{label:"会议报告",prop:"meetingReport"},{default:r((()=>[u(t,{modelValue:Ge.value.meetingReport,"onUpdate:modelValue":l[14]||(l[14]=e=>Ge.value.meetingReport=e),type:"textarea",rows:3,placeholder:"请输入会议报告",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])])),_:1}),u(Nl,{label:"工作日记",prop:"workDiary"},{default:r((()=>[u(t,{modelValue:Ge.value.workDiary,"onUpdate:modelValue":l[15]||(l[15]=e=>Ge.value.workDiary=e),type:"textarea",rows:3,placeholder:"请输入工作日记",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])])),_:1})]),s("div",Je,[l[60]||(l[60]=s("h3",null,"评价",-1)),u(Nl,{label:"评价内容"},{default:r((()=>[u(t,{modelValue:Ge.value.managerEvaluation,"onUpdate:modelValue":l[16]||(l[16]=e=>Ge.value.managerEvaluation=e),type:"textarea",rows:4,placeholder:"请输入对该员工的评价",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])])),_:1})])])),_:1},8,["model"])):V("",!0)])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-2a63753a"]]);export{Pe as default};
