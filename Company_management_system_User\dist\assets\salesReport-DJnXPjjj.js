import{r as t}from"./request-Cm8Ap7dD.js";function e(e){return t({url:"/sales-report",method:"post",data:e})}function r(e){return t({url:`/sales-report/detail/${e}`,method:"get"})}function s(e){return t({url:"/sales-report/my-reports",method:"get",params:e})}function a(e={}){return t({url:"/sales-report/my-clients",method:"get",params:e})}function o(){return t({url:"/sales-report/my-client-statistics",method:"get"})}function n(e){return t({url:"/sales-report/page",method:"get",params:e})}function u(e){return t({url:"/sales-report/department/edit",method:"put",data:e})}function l(e,r={}){return t({url:`/sales-report/department/employee-clients/${e}`,method:"get",params:r})}function p(e){return t({url:`/sales-report/department/report-detail/${e}`,method:"get"})}export{r as a,a as b,o as c,n as d,p as e,l as f,s as g,u as h,e as s};
