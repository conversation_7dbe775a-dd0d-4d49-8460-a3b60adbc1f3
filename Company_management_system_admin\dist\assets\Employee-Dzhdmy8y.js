import{v as e,x as a,y as l,z as t,B as o,C as d,A as i,_ as r,a as s,r as n,o as u,c as p,e as c,R as m,f as g,k as h,S as v,j as y,T as _,K as f,E as w,m as b,h as V,U as I,I as x,J as k,n as M,V as C,W as D,t as q,Y,Z as U,g as z,O as N}from"./index-BDR0Pmj6.js";import{g as S,d as A,a as J,u as P}from"./employee-DbFXp2d5.js";import{b as j}from"./department-jpUZgat6.js";import{b as E}from"./position-94XStkZ1.js";const T=[{id:"dashboard",title:"首页",icon:e(a)},{id:"organization",title:"组织管理",icon:e(l),children:[{id:"department",title:"部门管理"},{id:"position",title:"职位管理"}]},{id:"personnel",title:"人员管理",icon:e(t),children:[{id:"employee",title:"员工管理"},{id:"client",title:"客户管理"}]},{id:"performance",title:"业绩管理",icon:e(o),children:[{id:"performance-analysis",title:"业绩分析"},{id:"salary",title:"工资管理"},{id:"department-expense",title:"部门开销"},{id:"employee-expense",title:"员工费用"}]},{id:"sales-report",title:"销售日报",icon:e(d),children:[{id:"sales-report-management",title:"日报管理"}]},{id:"petty-cash",title:"备用金管理",icon:e(i)}],O={class:"employee-container"},R={class:"toolbar"},B={class:"search-box"},K={class:"action-box"},L={class:"operation-buttons"},Z={class:"pagination-container"},$={class:"dialog-footer"},F=r({__name:"Employee",setup(e){const a=s([]),l=s(!0),t=s(""),o=s(""),d=s([]),i=s([]),r=s(!1),F=s(!1),W=n({currentPage:1,pageSize:10,total:0}),G=s(!1),H=s("add"),Q=s(null),X=s(!1),ee=n({employee_id:null,name:"",email:"",phone:"",password:"",entry_date:"",exit_date:"",id_card:"",department_id:null,position_id:null,logistics_route:"",role:"employee",accessibleMenuIds:[],status:"Active"}),ae={name:[{required:!0,message:"请输入员工姓名",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"}],password:[{required:()=>"add"===H.value,message:"请输入密码，需包含大小写字母和数字，长度至少8位",trigger:"blur",validator:function(e,a,l){"edit"!==H.value||a?"add"!==H.value||a?a&&a.length<8?l(new Error("密码长度不能少于8个字符")):a&&!/[A-Z]/.test(a)?l(new Error("密码必须包含大写字母")):a&&!/[a-z]/.test(a)?l(new Error("密码必须包含小写字母")):a&&!/[0-9]/.test(a)?l(new Error("密码必须包含数字")):l():l(new Error("请输入密码，需包含大小写字母和数字，长度至少8位")):l()}}],entry_date:[{required:!0,message:"请选择入职日期",trigger:"change"}],id_card:[{required:!0,message:"请输入身份证号",trigger:"blur"}],department_id:[{required:!0,message:"请选择部门",trigger:"change"}],position_id:[{required:!0,message:"请选择职位",trigger:"change"}],role:[{required:!0,message:"请选择角色",trigger:"change"}]};const le=async e=>{if(e){F.value=!0;try{const a=await E(e);200===a.code?i.value=a.data||[]:w.error(a.msg||"获取职位列表失败")}catch(a){w.error("加载职位列表失败: "+(a.message||"未知错误"))}finally{F.value=!1}}else i.value=[]},te=async e=>{if(ee.position_id=null,e)try{await le(e),0===i.value.length&&w({type:"warning",message:"当前选择的部门暂无职位，请先添加职位",duration:3e3})}catch(a){w.error("加载职位数据失败")}else i.value=[]},oe=async()=>{l.value=!0,a.value=[];try{const e=await S({pageNum:W.currentPage,pageSize:W.pageSize,name:t.value||void 0,departmentId:o.value||void 0});200===e.code?(a.value=e.data.list||[],W.total=e.data.total||0):w.error(e.msg||"获取员工数据失败")}catch(e){w.error("加载员工数据失败: "+(e.message||"未知错误"))}finally{l.value=!1}},de=()=>{Q.value&&Q.value.resetFields(),ee.employee_id=null,ee.name="",ee.email="",ee.phone="",ee.password="",ee.entry_date="",ee.exit_date="",ee.id_card="",ee.department_id=null,ee.position_id=null,ee.logistics_route="",ee.role="employee",ee.accessibleMenuIds=[],ee.status="Active",i.value=[]},ie=()=>{G.value=!1,de()},re=()=>{H.value="add",de(),G.value=!0},se=()=>{W.currentPage=1,oe()},ne=()=>{t.value="",o.value="",W.currentPage=1,oe()},ue=e=>{W.currentPage=e,oe()},pe=e=>{W.pageSize=e,W.currentPage=1,oe()},ce=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}).replace(/\//g,"-")},me=e=>{if(!e)return"";const a=new Date(e);return isNaN(a.getTime())?e:a.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")},ge=e=>{const a=d.value.find((a=>a.departmentId===e));return a?a.departmentName:""};return u((()=>{(async()=>{r.value=!0;try{const e=await j();200===e.code?d.value=e.data||[]:w.error(e.msg||"获取部门列表失败")}catch(e){w.error("加载部门列表失败: "+(e.message||"未知错误"))}finally{r.value=!1}})(),oe()})),(e,s)=>{const n=y("el-icon"),u=y("el-input"),S=y("el-option"),j=y("el-select"),E=y("el-button"),he=y("el-table-column"),ve=y("el-tag"),ye=y("el-tooltip"),_e=y("el-table"),fe=y("el-pagination"),we=y("el-form-item"),be=y("el-date-picker"),Ve=y("el-tree-select"),Ie=y("el-form"),xe=y("el-dialog"),ke=_("loading");return b(),p("div",O,[c("div",R,[c("div",B,[g(u,{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=e=>t.value=e),placeholder:"搜索员工姓名",clearable:"",onKeyup:v(se,["enter"])},{prefix:h((()=>[g(n,null,{default:h((()=>[g(V(I))])),_:1})])),_:1},8,["modelValue"]),g(j,{modelValue:o.value,"onUpdate:modelValue":s[1]||(s[1]=e=>o.value=e),placeholder:"选择部门",clearable:"",style:{width:"220px"}},{default:h((()=>[(b(!0),p(x,null,k(d.value,(e=>(b(),f(S,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),g(E,{type:"primary",onClick:se},{default:h((()=>s[16]||(s[16]=[M("搜索")]))),_:1}),g(E,{onClick:ne},{default:h((()=>[g(n,null,{default:h((()=>[g(V(C))])),_:1}),s[17]||(s[17]=M("重置 "))])),_:1})]),c("div",K,[g(E,{type:"primary",onClick:re,class:"add-btn"},{default:h((()=>[g(n,null,{default:h((()=>[g(V(D))])),_:1}),s[18]||(s[18]=M("添加员工 "))])),_:1})])]),m((b(),f(_e,{data:a.value,border:"","row-key":"employeeId","max-height":"calc(100vh - 220px)",class:"custom-table"},{default:h((()=>[g(he,{type:"index",width:"60",align:"center",label:"序号",fixed:"","class-name":"index-column"}),g(he,{prop:"name",label:"姓名","min-width":"100","show-overflow-tooltip":""}),g(he,{prop:"email",label:"邮箱","min-width":"150","show-overflow-tooltip":""}),g(he,{prop:"phone",label:"手机号","min-width":"120","show-overflow-tooltip":""}),g(he,{prop:"idCard",label:"身份证号","min-width":"180","show-overflow-tooltip":""}),g(he,{label:"部门","min-width":"120","show-overflow-tooltip":""},{default:h((({row:e})=>[M(q(ge(e.departmentId)),1)])),_:1}),g(he,{label:"职位","min-width":"120","show-overflow-tooltip":""},{default:h((e=>[M(q(e.row.positionName||"----"),1)])),_:1}),g(he,{label:"物流航线","min-width":"120","show-overflow-tooltip":""},{default:h((e=>[M(q(e.row.logisticsRoute||"----"),1)])),_:1}),g(he,{label:"入职日期","min-width":"120","show-overflow-tooltip":""},{default:h((e=>[M(q(ce(e.row.entryDate)),1)])),_:1}),g(he,{label:"状态",width:"100",align:"center","show-overflow-tooltip":""},{default:h((e=>[e.row.exitDate?(b(),f(ye,{key:0,content:`离职日期: ${ce(e.row.exitDate)}`,placement:"top"},{default:h((()=>[g(ve,{type:"Active"===e.row.status?"success":"danger",class:"status-tag"},{default:h((()=>[M(q("Active"===e.row.status?"在职":"离职"),1)])),_:2},1032,["type"])])),_:2},1032,["content"])):(b(),f(ve,{key:1,type:"Active"===e.row.status?"success":"danger",class:"status-tag"},{default:h((()=>[M(q("Active"===e.row.status?"在职":"离职"),1)])),_:2},1032,["type"]))])),_:1}),g(he,{label:"创建时间","min-width":"160","show-overflow-tooltip":""},{default:h((e=>[M(q(me(e.row.createTime)),1)])),_:1}),g(he,{label:"更新时间","min-width":"160","show-overflow-tooltip":""},{default:h((e=>[M(q(me(e.row.updateTime)),1)])),_:1}),g(he,{label:"操作",width:"180",align:"center",fixed:"right","class-name":"operation-column"},{default:h((({row:e})=>[c("div",L,[g(E,{class:"edit-btn",onClick:a=>(async e=>{if(H.value="edit",de(),ee.employee_id=e.employeeId,ee.name=e.name,ee.email=e.email,ee.phone=e.phone||"",ee.password="",ee.entry_date=e.entryDate,ee.exit_date=e.exitDate||"",ee.id_card=e.idCard,ee.department_id=e.departmentId,ee.role=e.role,ee.logistics_route=e.logisticsRoute||"",ee.status=e.status,e.accessibleMenuIdsJson)try{const a=JSON.parse(e.accessibleMenuIdsJson);Array.isArray(a)?ee.accessibleMenuIds=a:ee.accessibleMenuIds=[]}catch(a){ee.accessibleMenuIds=[]}else ee.accessibleMenuIds=[];try{await le(e.departmentId);const a=i.value.some((a=>a.positionId===e.positionId));ee.position_id=a?e.positionId:null}catch(l){w.error("加载职位数据失败")}G.value=!0})(e),title:"编辑"},{default:h((()=>[g(n,null,{default:h((()=>[g(V(Y))])),_:1})])),_:2},1032,["onClick"]),g(E,{class:"delete-btn",onClick:a=>(e=>{N.confirm(`确定要删除员工 "${e.name}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{l.value=!0;try{const a=await A(e.employeeId);200===a.code?(w({type:"success",message:"删除员工成功",duration:2e3}),oe()):(w({type:"error",message:a.msg||"删除失败",duration:3e3}),l.value=!1)}catch(a){w({type:"error",message:"删除失败: "+(a.message||"未知错误"),duration:3e3}),l.value=!1}})).catch((()=>{}))})(e),title:"删除"},{default:h((()=>[g(n,null,{default:h((()=>[g(V(U))])),_:1})])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[ke,l.value]]),c("div",Z,[g(fe,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":W.currentPage,"page-size":W.pageSize,total:W.total,"page-sizes":[10,20,50,100],onSizeChange:pe,onCurrentChange:ue},null,8,["current-page","page-size","total"])]),g(xe,{modelValue:G.value,"onUpdate:modelValue":s[15]||(s[15]=e=>G.value=e),title:"add"===H.value?"添加员工":"编辑员工",width:"600px","destroy-on-close":"",class:"custom-dialog"},{default:h((()=>[g(Ie,{ref_key:"formRef",ref:Q,model:ee,rules:ae,"label-position":"left","label-width":"100px",class:"dialog-form"},{default:h((()=>[g(we,{label:"姓名",prop:"name",required:""},{default:h((()=>[g(u,{modelValue:ee.name,"onUpdate:modelValue":s[2]||(s[2]=e=>ee.name=e),placeholder:"请输入员工姓名"},null,8,["modelValue"])])),_:1}),g(we,{label:"邮箱",prop:"email"},{default:h((()=>[g(u,{modelValue:ee.email,"onUpdate:modelValue":s[3]||(s[3]=e=>ee.email=e),placeholder:"请输入邮箱地址"},null,8,["modelValue"])])),_:1}),g(we,{label:"手机号",prop:"phone",required:""},{default:h((()=>[g(u,{modelValue:ee.phone,"onUpdate:modelValue":s[4]||(s[4]=e=>ee.phone=e),placeholder:"请输入手机号码"},null,8,["modelValue"])])),_:1}),g(we,{label:"密码",prop:"password",required:"add"===H.value},{default:h((()=>[g(u,{modelValue:ee.password,"onUpdate:modelValue":s[5]||(s[5]=e=>ee.password=e),type:"password","show-password":"",placeholder:"edit"===H.value?"不填写则不修改密码，填写需包含大小写字母和数字，长度至少8位":"密码需包含大小写字母和数字，长度至少8位"},null,8,["modelValue","placeholder"])])),_:1},8,["required"]),g(we,{label:"身份证号",prop:"id_card",required:""},{default:h((()=>[g(u,{modelValue:ee.id_card,"onUpdate:modelValue":s[6]||(s[6]=e=>ee.id_card=e),placeholder:"请输入身份证号"},null,8,["modelValue"])])),_:1}),g(we,{label:"入职日期",prop:"entry_date",required:""},{default:h((()=>[g(be,{modelValue:ee.entry_date,"onUpdate:modelValue":s[7]||(s[7]=e=>ee.entry_date=e),type:"date",placeholder:"选择入职日期",style:{width:"100%"},format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),g(we,{label:"离职日期",prop:"exit_date"},{default:h((()=>[g(be,{modelValue:ee.exit_date,"onUpdate:modelValue":s[8]||(s[8]=e=>ee.exit_date=e),type:"date",placeholder:"选择离职日期（可选）",style:{width:"100%"},format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),g(we,{label:"部门",prop:"department_id",required:""},{default:h((()=>[g(j,{modelValue:ee.department_id,"onUpdate:modelValue":s[9]||(s[9]=e=>ee.department_id=e),placeholder:"请选择部门",style:{width:"100%"},loading:r.value,onChange:te},{default:h((()=>[(b(!0),p(x,null,k(d.value,(e=>(b(),f(S,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),g(we,{label:"职位",prop:"position_id",required:""},{default:h((()=>[g(j,{modelValue:ee.position_id,"onUpdate:modelValue":s[10]||(s[10]=e=>ee.position_id=e),placeholder:"请选择职位",style:{width:"100%"},loading:F.value,disabled:!ee.department_id},{default:h((()=>[0===i.value.length&&!F.value&&ee.department_id?(b(),f(S,{key:0,label:"当前部门暂无职位",value:"",disabled:""})):z("",!0),(b(!0),p(x,null,k(i.value,(e=>(b(),f(S,{key:e.positionId,label:e.positionName,value:e.positionId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading","disabled"])])),_:1}),g(we,{label:"物流航线",prop:"logistics_route"},{default:h((()=>[g(u,{modelValue:ee.logistics_route,"onUpdate:modelValue":s[11]||(s[11]=e=>ee.logistics_route=e),placeholder:"请输入物流航线（可选）"},null,8,["modelValue"])])),_:1}),g(we,{label:"角色",prop:"role",required:""},{default:h((()=>[g(j,{modelValue:ee.role,"onUpdate:modelValue":s[12]||(s[12]=e=>ee.role=e),placeholder:"请选择角色",style:{width:"100%"}},{default:h((()=>[g(S,{label:"员工",value:"employee"}),g(S,{label:"部门负责人",value:"manager"}),g(S,{label:"管理员",value:"admin"})])),_:1},8,["modelValue"])])),_:1}),"admin"===ee.role?(b(),f(we,{label:"后台菜单权限",prop:"accessibleMenuIds",key:"admin-menu-permission-item",class:"admin-menu-permission-item"},{default:h((()=>[g(Ve,{modelValue:ee.accessibleMenuIds,"onUpdate:modelValue":s[13]||(s[13]=e=>ee.accessibleMenuIds=e),data:V(T),multiple:"","show-checkbox":"","check-strictly":!1,"node-key":"id",props:{label:"title",children:"children",value:"id"},placeholder:"选择后台菜单权限",style:{width:"100%"},"check-on-click-node":"","default-expand-all":"",class:"admin-menu-tree-select"},null,8,["modelValue","data"])])),_:1})):z("",!0)])),_:1},8,["model"]),c("div",$,[g(E,{onClick:ie},{default:h((()=>s[19]||(s[19]=[M("取消")]))),_:1}),g(E,{type:"primary",loading:X.value,onClick:s[14]||(s[14]=e=>(async e=>{e&&await e.validate((async(e,a)=>{if(!e){const e=[];for(const l in a)if(a.hasOwnProperty(l)){const t=a[l];t&&t.length>0&&e.push(t[0].message)}return w({type:"warning",message:"请完善表单信息："+e.join("；"),duration:5e3}),!1}X.value=!0;try{const e={employeeId:ee.employee_id,name:ee.name,email:ee.email,phone:ee.phone,entryDate:ee.entry_date,exitDate:ee.exit_date||null,idCard:ee.id_card,departmentId:ee.department_id,positionId:ee.position_id,logisticsRoute:ee.logistics_route||null,role:ee.role,accessibleMenuIdsJson:null};if(ee.password&&(e.password=ee.password),"admin"===ee.role){const a=ee.accessibleMenuIds||[];e.accessibleMenuIdsJson=JSON.stringify(a)}else e.accessibleMenuIdsJson=JSON.stringify([]);let a;a="add"===H.value?await J(e):await P(e),200===a.code&&(w({type:"success",message:"add"===H.value?"添加员工成功":"更新员工成功",duration:2e3}),G.value=!1,de(),oe())}catch(l){}finally{X.value=!1}}))})(Q.value))},{default:h((()=>s[20]||(s[20]=[M("确定")]))),_:1},8,["loading"])])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-4fe48702"]]);export{F as default};
