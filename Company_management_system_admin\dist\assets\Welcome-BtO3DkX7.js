import{a0 as a,_ as e,u as s,a as l,v as t,y as d,z as i,ad as o,B as c,A as r,ae as n,af as u,ag as m,D as v,o as p,c as h,e as f,t as y,h as _,f as g,k as x,ah as b,K as k,a5 as w,E as q,j,T as C,m as D,ai as I,R as z,aj as L,ak as S,al as A,I as B,J as E,am as F,L as J}from"./index-BDR0Pmj6.js";const K={class:"welcome-container"},M={class:"welcome-header"},N={class:"date"},R={class:"user-greeting"},T={class:"dashboard-summary"},W={class:"dashboard-detail"},$={class:"dashboard-detail"},G={class:"quick-access"},H={class:"quick-title"},O=e({__name:"Welcome",setup(e){const O=s(),P=l(null),Q=l(!1),U=(new Date).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"}),V=l({employees:{total:0,active:0,inactive:0},departments:0,positions:0,performance:{total:0,average:0}}),X=l([{id:"department",title:"部门管理",path:"/department",icon:t(d)},{id:"employee",title:"员工管理",path:"/employee",icon:t(i)},{id:"client",title:"客户管理",path:"/client",icon:t(o)},{id:"performance-analysis",title:"业绩分析",path:"/performance-analysis",icon:t(c)},{id:"salary",title:"工资管理",path:"/salary",icon:t(r)},{id:"petty-cash",title:"备用金管理",path:"/petty-cash",icon:t(n)},{id:"department-expense",title:"部门开销",path:"/department-expense",icon:t(u)},{id:"employee-expense",title:"员工费用",path:"/employee-expense",icon:t(m)}]),Y=v((()=>{const a=new Set(O.accessibleMenuItems||[]);return X.value.filter((e=>a.has(e.id)))})),Z=async()=>{Q.value=!0;try{const e=await a({url:"/dashboard/stats",method:"get"});200===e.code?V.value=e.data:q.error(e.msg||"获取仪表盘数据失败")}catch(e){e&&e.message&&(e.message.includes("登录已过期，请重新登录")||e.message.includes("无法连接到服务器")||e.message.includes("token无效或已过期"))||q.error("加载仪表盘数据失败: "+(e.message||"未知错误"))}finally{Q.value=!1}};return p((()=>{P.value=O.user,Z()})),(a,e)=>{var s;const l=j("el-icon"),t=C("loading");return D(),h("div",K,[f("div",M,[e[0]||(e[0]=f("h2",null,"欢迎来到中航物流管理系统",-1)),f("p",N,y(_(U)),1)]),f("div",R,[f("h3",null,"您好，"+y((null==(s=P.value)?void 0:s.name)||"管理员")+"！",1),e[1]||(e[1]=f("p",null,"欢迎回到管理系统，祝您工作顺利。",-1))]),f("div",T,[g(_(b),{gutter:20},{default:x((()=>[g(_(I),{xs:24,sm:12,md:6},{default:x((()=>[z((D(),k(_(L),{shadow:"hover",class:"dashboard-card"},{default:x((()=>[g(_(S),{value:V.value.employees.total,title:"员工总数"},{prefix:x((()=>[g(l,{class:"dashboard-icon"},{default:x((()=>[g(_(i))])),_:1})])),_:1},8,["value"]),f("div",W," 在职: "+y(V.value.employees.active)+" | 离职: "+y(V.value.employees.inactive),1)])),_:1})),[[t,Q.value]])])),_:1}),g(_(I),{xs:24,sm:12,md:6},{default:x((()=>[z((D(),k(_(L),{shadow:"hover",class:"dashboard-card"},{default:x((()=>[g(_(S),{value:V.value.departments,title:"部门数量"},{prefix:x((()=>[g(l,{class:"dashboard-icon"},{default:x((()=>[g(_(d))])),_:1})])),_:1},8,["value"])])),_:1})),[[t,Q.value]])])),_:1}),g(_(I),{xs:24,sm:12,md:6},{default:x((()=>[z((D(),k(_(L),{shadow:"hover",class:"dashboard-card"},{default:x((()=>[g(_(S),{value:V.value.positions,title:"职位数量"},{prefix:x((()=>[g(l,{class:"dashboard-icon"},{default:x((()=>[g(_(A))])),_:1})])),_:1},8,["value"])])),_:1})),[[t,Q.value]])])),_:1}),g(_(I),{xs:24,sm:12,md:6},{default:x((()=>[z((D(),k(_(L),{shadow:"hover",class:"dashboard-card"},{default:x((()=>[g(_(S),{value:V.value.performance.total,precision:2,title:"总业绩(元)"},{prefix:x((()=>[g(l,{class:"dashboard-icon"},{default:x((()=>[g(_(c))])),_:1})])),_:1},8,["value"]),f("div",$," 平均: "+y(V.value.performance.average.toFixed(2))+"元/人 ",1)])),_:1})),[[t,Q.value]])])),_:1})])),_:1})]),f("div",G,[e[2]||(e[2]=f("h3",null,"快速访问",-1)),Y.value.length>0?(D(),k(_(b),{key:0,gutter:20},{default:x((()=>[(D(!0),h(B,null,E(Y.value,(e=>(D(),k(_(I),{key:e.id,xs:24,sm:12,md:8,lg:6},{default:x((()=>[g(_(L),{shadow:"hover",class:"quick-card",onClick:s=>a.$router.push(e.path)},{default:x((()=>[f("div",{class:"quick-icon",style:F({color:e.color})},[(D(),k(J(e.icon)))],4),f("div",H,y(e.title),1)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1})):(D(),k(_(w),{key:1,description:"暂无可用的快速访问项"}))])])}}},[["__scopeId","data-v-c03c9568"]]);export{O as default};
