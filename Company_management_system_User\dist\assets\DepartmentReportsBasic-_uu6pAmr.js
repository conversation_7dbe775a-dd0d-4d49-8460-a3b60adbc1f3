import{_ as e,a as l,r as a,o as t,c as i,d as n,Z as s,e as o,i as u,j as r,$ as d,Y as c,z as v,E as p,m,g as y,a0 as h,n as g,a1 as f,t as b,aq as w,a3 as k,f as _,F as C,A as V,a4 as D}from"./index-CGqeVPF3.js";import{d as I,e as x,f as N,b as R,h as L}from"./salesReport-DJnXPjjj.js";import{c as U}from"./department-DD9JCT-L.js";import"./request-Cm8Ap7dD.js";const q={class:"department-content-container"},E={class:"toolbar"},S={class:"search-section"},z={class:"search-row"},O={class:"client-stats"},j={class:"stat-row"},Y={class:"value"},T={class:"value"},M={class:"stat-row"},P={class:"value"},A={key:0,class:"evaluation-text"},J={key:1,class:"no-evaluation"},F={class:"operation-buttons"},$={class:"pagination-container"},B={key:0,class:"report-detail"},K={class:"detail-section"},Z={class:"info-grid"},G={class:"info-item"},H={class:"info-item"},Q={class:"info-item"},W={class:"info-item"},X={class:"detail-section"},ee={class:"stats-row"},le={class:"stat-box"},ae={class:"stat-number"},te={class:"stat-box"},ie={class:"stat-number"},ne={class:"stat-box"},se={class:"stat-number"},oe={class:"detail-section"},ue={class:"client-info"},re={class:"client-category"},de={class:"client-list"},ce={key:0,class:"no-data"},ve={class:"client-category"},pe={class:"client-list"},me={key:0,class:"no-data"},ye={class:"client-category"},he={class:"client-list"},ge={key:0,class:"no-data"},fe={key:0,class:"detail-section"},be={class:"checklist-display"},we={class:"checklist-items"},ke={class:"detail-section"},_e={class:"work-records"},Ce={class:"record-item"},Ve={class:"record-content"},De={class:"record-item"},Ie={class:"record-content"},xe={class:"record-item"},Ne={class:"record-content"},Re={class:"detail-section"},Le={class:"evaluation-content"},Ue={key:0,class:"evaluation-text"},qe={key:1,class:"evaluation-info"},Ee={class:"evaluation-time"},Se={key:0,class:"evaluator-name"},ze={key:2,class:"no-evaluation"},Oe={class:"form-section"},je={class:"form-section"},Ye={class:"form-section"},Te={class:"form-section"},Me={class:"form-section"},Pe={class:"form-section"},Ae={class:"dialog-footer"},Je=e({__name:"DepartmentReportsBasic",setup(e){const Je=l(!1),Fe=l([]),$e=l(!1),Be=l(null),Ke=l(!1),Ze=l(null),Ge=l(null),He=l(!1),Qe=l([]),We=l(!1),Xe=l([]),el=l([]),ll=l([]),al={value:"value",label:"label",children:"children",checkStrictly:!0,emitPath:!1,expandTrigger:"hover",multiple:!0},tl=l([]),il=l(!1),nl=new Map,sl=l(1),ol=l(10),ul=l(0),rl=a({departmentIds:[],employeeName:"",dateRange:[],responsibilityLevel:""}),dl={responsibilityLevel:[{required:!0,message:"请选择责任心评级",trigger:"change"}],dailyResults:[{min:1,max:2e3,message:"今日效果长度必须在1-2000字符之间",trigger:"blur"}],meetingReport:[{min:1,max:2e3,message:"会议报告长度必须在1-2000字符之间",trigger:"blur"}],workDiary:[{min:1,max:2e3,message:"工作日记长度必须在1-2000字符之间",trigger:"blur"}]},cl=async()=>{try{Je.value=!0;const e={pageNum:sl.value,pageSize:ol.value};rl.employeeName&&(e.employeeName=rl.employeeName),rl.dateRange&&2===rl.dateRange.length&&(e.startDate=rl.dateRange[0],e.endDate=rl.dateRange[1]),rl.responsibilityLevel&&(e.responsibilityLevel=rl.responsibilityLevel),rl.departmentIds&&rl.departmentIds.length>0&&(e.departmentIds=rl.departmentIds.join(","));const l=await I(e);200===l.code&&(Fe.value=l.data.list||[],ul.value=l.data.total||0)}catch(e){p.error("获取部门日报列表失败")}finally{Je.value=!1}},vl=e=>{switch(e){case"优秀":return"success";case"中等":return"warning";case"差":return"danger";default:return"info"}},pl=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric"}),ml=e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),yl=()=>{sl.value=1,cl()},hl=()=>{Xe.value=[],el.value=[],rl.departmentIds=[],rl.employeeName="",rl.dateRange=[],rl.responsibilityLevel="",sl.value=1,cl()},gl=e=>{ol.value=e,sl.value=1,cl()},fl=e=>{sl.value=e,cl()},bl=async()=>{try{await Ge.value.validate(),He.value=!0;const e={id:Ze.value.id,employeeId:Ze.value.employeeId,reportDate:Ze.value.reportDate,inquiryClientIds:Ze.value.inquiryClientIds,shippingClientIds:Ze.value.shippingClientIds,keyDevelopmentClientIds:Ze.value.keyDevelopmentClientIds,responsibilityLevel:Ze.value.responsibilityLevel,endOfDayChecklist:Ze.value.endOfDayChecklistItems,dailyResults:Ze.value.dailyResults,meetingReport:Ze.value.meetingReport,workDiary:Ze.value.workDiary,managerEvaluation:Ze.value.managerEvaluation},l=await L(e);200===l.code?(p.success("日报编辑成功"),Ke.value=!1,cl()):p.error(l.message||"编辑失败")}catch(e){p.error("编辑失败")}finally{He.value=!1}},wl=e=>{const l=[],a=(e,l)=>{for(const t of e){if(t.value===l)return t;if(t.children&&t.children.length){const e=a(t.children,l);if(e)return e}}return null},t=e=>{if(e.children&&e.children.length)for(const a of e.children)l.push(a.value),t(a)},i=a(Qe.value,e);return i&&t(i),l},kl=e=>e&&e.length?e.map((e=>({value:e.departmentId,label:e.departmentName,children:kl(e.children||[])}))):[],_l=e=>{if(!e||0===e.length)return el.value=[],Xe.value=[],rl.departmentIds=[],void yl();const l=el.value||[],a=e.filter((e=>!l.includes(e))),t=l.filter((l=>!e.includes(l)));let i=[...e],n=[...e];for(const s of a){const e=wl(s);e.length>0&&e.forEach((e=>{i.includes(e)||i.push(e),n.includes(e)||n.push(e)}))}for(const s of t){const e=wl(s);e.length>0&&(i=i.filter((l=>!e.includes(l))),n=n.filter((l=>!e.includes(l))))}Xe.value=n,el.value=i,rl.departmentIds=i,yl()},Cl=async(e=null)=>{const l=e||"current";if(nl.has(l))tl.value=nl.get(l);else try{let a;if(il.value=!0,a=e?await N(e,{limit:100}):await R({limit:100}),200===a.code){const e=a.data||[];tl.value=e,nl.set(l,e)}}catch(a){p.error("获取客户列表失败")}finally{il.value=!1}},Vl=async(e,l=null)=>{if(!e){const e=l||"current";return void(nl.has(e)&&(tl.value=nl.get(e)))}const a=l||"current";if(nl.has(a)){const l=nl.get(a).filter((l=>l.name&&l.name.toLowerCase().includes(e.toLowerCase())));if(l.length>0)return void(tl.value=l)}try{let a;il.value=!0,a=l?await N(l,{keyword:e,limit:10}):await R({keyword:e,limit:10}),200===a.code&&(tl.value=a.data||[])}catch(t){}finally{il.value=!1}},Dl=e=>{if(!e)return[];try{return("string"==typeof e?JSON.parse(e):e).clientIds||[]}catch(l){return[]}},Il=e=>{if(!e)return[];try{const l="string"==typeof e?JSON.parse(e):e;return l.items&&Array.isArray(l.items)?l.items:l.items&&"object"==typeof l.items?Object.keys(l.items).filter((e=>l.items[e])):[]}catch(l){return[]}},xl=(e,l)=>{const a=e[{inquiry:"inquiryClients",shipping:"shippingClients",keyDevelopment:"keyDevelopmentClients"}[l]];return Dl(a).map((e=>{const l=tl.value.find((l=>l.id===e));return{id:e,name:l?l.name:`客户ID: ${e}`}}))},Nl=e=>e?(e=>{try{return e&&JSON.parse(e).items||[]}catch(l){return[]}})(e.endOfDayChecklist):[];return t((()=>{(async()=>{try{We.value=!0;const e=await U();if(200===e.code)if(Qe.value=(e.data||[]).map((e=>({value:e.departmentId,label:e.departmentName,children:kl(e.children||[])}))),ll.value=(e.data||[]).map((e=>e.departmentId)),ll.value.length>0){let e=[...ll.value];for(const l of ll.value){const a=wl(l);e=e.concat(a)}Xe.value=e,el.value=e,rl.departmentIds=e,yl()}else p.warning("您没有任何可以查看日报的部门"),Fe.value=[],ul.value=0}catch(e){p.error("加载部门信息失败")}finally{We.value=!1}})()})),(e,l)=>{const a=u("el-cascader"),t=u("el-icon"),I=u("el-input"),N=u("el-date-picker"),R=u("el-option"),L=u("el-select"),U=u("el-button"),el=u("el-table-column"),ll=u("el-tag"),nl=u("el-table"),cl=u("el-pagination"),wl=u("el-dialog"),kl=u("el-form-item"),Rl=u("el-col"),Ll=u("el-row"),Ul=u("el-radio"),ql=u("el-radio-group"),El=u("el-checkbox"),Sl=u("el-checkbox-group"),zl=u("el-form"),Ol=c("loading");return m(),i("div",q,[n("div",E,[n("div",S,[n("div",z,[o(a,{modelValue:Xe.value,"onUpdate:modelValue":l[0]||(l[0]=e=>Xe.value=e),options:Qe.value,props:al,placeholder:"请选择您负责的部门",clearable:"",loading:We.value,style:{width:"280px","margin-right":"10px"},"collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":2,onChange:_l},null,8,["modelValue","options","loading"]),o(I,{modelValue:rl.employeeName,"onUpdate:modelValue":l[1]||(l[1]=e=>rl.employeeName=e),placeholder:"搜索员工姓名",clearable:"",style:{width:"160px"},onKeyup:d(yl,["enter"]),onClear:yl},{prefix:r((()=>[o(t,null,{default:r((()=>[o(y(h))])),_:1})])),_:1},8,["modelValue"]),o(N,{modelValue:rl.dateRange,"onUpdate:modelValue":l[2]||(l[2]=e=>rl.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"220px"},onChange:yl},null,8,["modelValue"]),o(L,{modelValue:rl.responsibilityLevel,"onUpdate:modelValue":l[3]||(l[3]=e=>rl.responsibilityLevel=e),placeholder:"责任心评级",clearable:"",style:{width:"120px"},onChange:yl},{default:r((()=>[o(R,{label:"优秀",value:"优秀"}),o(R,{label:"中等",value:"中等"}),o(R,{label:"差",value:"差"})])),_:1},8,["modelValue"]),o(U,{type:"primary",onClick:yl},{default:r((()=>[o(t,null,{default:r((()=>[o(y(h))])),_:1}),l[20]||(l[20]=g("搜索 "))])),_:1}),o(U,{onClick:hl},{default:r((()=>[o(t,null,{default:r((()=>[o(y(f))])),_:1}),l[21]||(l[21]=g("重置 "))])),_:1})])])]),s((m(),v(nl,{data:Fe.value,border:"","row-key":"id","max-height":"calc(100vh - 220px)",class:"custom-table","header-cell-style":{background:"#f7f7f7",color:"#606266"}},{default:r((()=>[o(el,{type:"index",width:"60",align:"center",label:"序号","class-name":"index-column"}),o(el,{prop:"employeeName",label:"员工姓名",width:"100",align:"center"}),o(el,{prop:"departmentName",label:"部门",width:"120",align:"center"}),o(el,{prop:"reportDate",label:"日报日期",width:"140",align:"center",sortable:""},{default:r((({row:e})=>{return[o(ll,{type:(l=e.reportDate,l===(new Date).toISOString().split("T")[0]?"success":"info"),effect:"plain"},{default:r((()=>[g(b(pl(e.reportDate)),1)])),_:2},1032,["type"])];var l})),_:1}),o(el,{label:"客户统计",width:"180",align:"center"},{default:r((({row:e})=>[n("div",O,[n("div",j,[l[22]||(l[22]=n("span",{class:"label"},"年度:",-1)),n("span",Y,b(e.yearlyNewClients||0),1),l[23]||(l[23]=n("span",{class:"label"},"月度:",-1)),n("span",T,b(e.monthlyNewClients||0),1)]),n("div",M,[l[24]||(l[24]=n("span",{class:"label"},"距上次:",-1)),n("span",P,b(e.daysSinceLastNewClient||0)+"天",1)])])])),_:1}),o(el,{prop:"responsibilityLevel",label:"责任心评级",width:"120",align:"center"},{default:r((({row:e})=>[o(ll,{type:vl(e.responsibilityLevel),effect:"light"},{default:r((()=>[g(b(e.responsibilityLevel),1)])),_:2},1032,["type"])])),_:1}),o(el,{prop:"dailyResults",label:"今日效果","min-width":"200","show-overflow-tooltip":"",align:"center"}),o(el,{prop:"managerEvaluation",label:"评价","min-width":"150","show-overflow-tooltip":"",align:"center"},{default:r((({row:e})=>[e.managerEvaluation?(m(),i("span",A,b(e.managerEvaluation),1)):(m(),i("span",J,"暂无评价"))])),_:1}),o(el,{prop:"createTime",label:"提交时间",width:"180",align:"center",sortable:""},{default:r((({row:e})=>[g(b(ml(e.createTime)),1)])),_:1}),o(el,{label:"操作",fixed:"right","min-width":"160",align:"center"},{default:r((({row:e})=>[n("div",F,[o(U,{type:"primary",size:"small",onClick:l=>(async e=>{try{await Cl(e.employeeId);const l=await x(e.id);200===l.code?Be.value=l.data:Be.value=e,$e.value=!0}catch(l){p.error("获取日报详情失败"),Be.value=e,$e.value=!0}})(e),title:"查看详情"},{default:r((()=>[o(t,null,{default:r((()=>[o(y(w))])),_:1}),l[25]||(l[25]=g("查看 "))])),_:2},1032,["onClick"]),o(U,{type:"warning",size:"small",onClick:l=>(async e=>{try{await Cl(e.employeeId);let a=e;try{const l=await x(e.id);200===l.code&&(a=l.data)}catch(l){}Ze.value={id:a.id,employeeId:a.employeeId,employeeName:a.employeeName,reportDate:a.reportDate,inquiryClientIds:Dl(a.inquiryClients),shippingClientIds:Dl(a.shippingClients),keyDevelopmentClientIds:Dl(a.keyDevelopmentClients),responsibilityLevel:a.responsibilityLevel,endOfDayChecklistItems:Il(a.endOfDayChecklist),dailyResults:a.dailyResults,meetingReport:a.meetingReport,workDiary:a.workDiary,managerEvaluation:a.managerEvaluation||""},Ke.value=!0}catch(l){p.error("编辑日报初始化失败")}})(e),title:"编辑日报"},{default:r((()=>[o(t,null,{default:r((()=>[o(y(k))])),_:1}),l[26]||(l[26]=g("编辑 "))])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[Ol,Je.value]]),n("div",$,[o(cl,{background:"",layout:"total, sizes, prev, pager, next, jumper",currentPage:sl.value,"onUpdate:currentPage":l[4]||(l[4]=e=>sl.value=e),"page-size":ol.value,"onUpdate:pageSize":l[5]||(l[5]=e=>ol.value=e),total:ul.value,"page-sizes":[10,20,50,100],onSizeChange:gl,onCurrentChange:fl},null,8,["currentPage","page-size","total"])]),o(wl,{modelValue:$e.value,"onUpdate:modelValue":l[6]||(l[6]=e=>$e.value=e),title:"日报详情",width:"800px","destroy-on-close":"",class:"report-detail-dialog"},{default:r((()=>[Be.value?(m(),i("div",B,[n("div",K,[l[31]||(l[31]=n("h3",null,"员工信息",-1)),n("div",Z,[n("div",G,[l[27]||(l[27]=n("label",null,"员工姓名:",-1)),n("span",null,b(Be.value.employeeName),1)]),n("div",H,[l[28]||(l[28]=n("label",null,"日报日期:",-1)),n("span",null,b(pl(Be.value.reportDate)),1)]),n("div",Q,[l[29]||(l[29]=n("label",null,"责任心评级:",-1)),o(ll,{type:vl(Be.value.responsibilityLevel)},{default:r((()=>[g(b(Be.value.responsibilityLevel),1)])),_:1},8,["type"])]),n("div",W,[l[30]||(l[30]=n("label",null,"提交时间:",-1)),n("span",null,b(ml(Be.value.createTime)),1)])])]),n("div",X,[l[35]||(l[35]=n("h3",null,"客户统计",-1)),n("div",ee,[n("div",le,[n("div",ae,b(Be.value.yearlyNewClients||0),1),l[32]||(l[32]=n("div",{class:"stat-desc"},"年度新客户",-1))]),n("div",te,[n("div",ie,b(Be.value.monthlyNewClients||0),1),l[33]||(l[33]=n("div",{class:"stat-desc"},"当月新客户",-1))]),n("div",ne,[n("div",se,b(Be.value.daysSinceLastNewClient||0),1),l[34]||(l[34]=n("div",{class:"stat-desc"},"距上次新客户天数",-1))])])]),n("div",oe,[l[39]||(l[39]=n("h3",null,"客户信息",-1)),n("div",ue,[n("div",re,[l[36]||(l[36]=n("h4",null,"询价客户",-1)),n("div",de,[(m(!0),i(C,null,V(xl(Be.value,"inquiry"),(e=>(m(),v(ll,{key:e.id,type:"info",class:"client-tag"},{default:r((()=>[g(b(e.name),1)])),_:2},1024)))),128)),xl(Be.value,"inquiry").length?_("",!0):(m(),i("span",ce,"暂无"))])]),n("div",ve,[l[37]||(l[37]=n("h4",null,"出货客户",-1)),n("div",pe,[(m(!0),i(C,null,V(xl(Be.value,"shipping"),(e=>(m(),v(ll,{key:e.id,type:"success",class:"client-tag"},{default:r((()=>[g(b(e.name),1)])),_:2},1024)))),128)),xl(Be.value,"shipping").length?_("",!0):(m(),i("span",me,"暂无"))])]),n("div",ye,[l[38]||(l[38]=n("h4",null,"重点开发客户",-1)),n("div",he,[(m(!0),i(C,null,V(xl(Be.value,"keyDevelopment"),(e=>(m(),v(ll,{key:e.id,type:"warning",class:"client-tag"},{default:r((()=>[g(b(e.name),1)])),_:2},1024)))),128)),xl(Be.value,"keyDevelopment").length?_("",!0):(m(),i("span",ge,"暂无"))])])])]),Nl(Be.value).length>0?(m(),i("div",fe,[l[40]||(l[40]=n("h3",null,"下班准备工作",-1)),n("div",be,[n("div",we,[(m(!0),i(C,null,V(Nl(Be.value),(e=>(m(),i("div",{key:e,class:"checklist-item"},[o(t,{class:"check-icon"},{default:r((()=>[o(y(D))])),_:1}),n("span",null,b(e),1)])))),128))])])])):_("",!0),n("div",ke,[l[44]||(l[44]=n("h3",null,"工作记录",-1)),n("div",_e,[n("div",Ce,[l[41]||(l[41]=n("h4",null,"今日效果",-1)),n("div",Ve,b(Be.value.dailyResults||"暂无记录"),1)]),n("div",De,[l[42]||(l[42]=n("h4",null,"会议报告",-1)),n("div",Ie,b(Be.value.meetingReport||"暂无记录"),1)]),n("div",xe,[l[43]||(l[43]=n("h4",null,"工作日记",-1)),n("div",Ne,b(Be.value.workDiary||"暂无记录"),1)])])]),n("div",Re,[l[45]||(l[45]=n("h3",null,"领导评价",-1)),n("div",Le,[Be.value.managerEvaluation?(m(),i("div",Ue,b(Be.value.managerEvaluation),1)):_("",!0),Be.value.evaluationTime?(m(),i("div",qe,[n("span",Ee,"评价时间："+b(ml(Be.value.evaluationTime)),1),Be.value.evaluatorName?(m(),i("span",Se,"评价人："+b(Be.value.evaluatorName),1)):_("",!0)])):_("",!0),Be.value.managerEvaluation?_("",!0):(m(),i("div",ze," 暂无评价 "))])])])):_("",!0)])),_:1},8,["modelValue"]),o(wl,{modelValue:Ke.value,"onUpdate:modelValue":l[19]||(l[19]=e=>Ke.value=e),title:"编辑日报",width:"900px","destroy-on-close":"",class:"edit-report-dialog"},{footer:r((()=>[n("div",Ae,[o(U,{onClick:l[18]||(l[18]=e=>Ke.value=!1)},{default:r((()=>l[60]||(l[60]=[g("取消")]))),_:1}),o(U,{type:"primary",onClick:bl,loading:He.value},{default:r((()=>l[61]||(l[61]=[g(" 保存 ")]))),_:1},8,["loading"])])])),default:r((()=>[Ze.value?(m(),v(zl,{key:0,ref_key:"editFormRef",ref:Ge,model:Ze.value,rules:dl,"label-width":"120px",class:"edit-form"},{default:r((()=>[n("div",Oe,[l[46]||(l[46]=n("h3",null,"基本信息",-1)),o(Ll,{gutter:20},{default:r((()=>[o(Rl,{span:12},{default:r((()=>[o(kl,{label:"员工姓名"},{default:r((()=>[o(I,{modelValue:Ze.value.employeeName,"onUpdate:modelValue":l[7]||(l[7]=e=>Ze.value.employeeName=e),disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),o(Rl,{span:12},{default:r((()=>[o(kl,{label:"日报日期"},{default:r((()=>[o(I,{modelValue:Ze.value.reportDate,"onUpdate:modelValue":l[8]||(l[8]=e=>Ze.value.reportDate=e),disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})]),n("div",je,[l[47]||(l[47]=n("h3",null,"客户选择",-1)),o(Ll,{gutter:20},{default:r((()=>[o(Rl,{span:8},{default:r((()=>[o(kl,{label:"询价客户"},{default:r((()=>[o(L,{modelValue:Ze.value.inquiryClientIds,"onUpdate:modelValue":l[9]||(l[9]=e=>Ze.value.inquiryClientIds=e),multiple:"",filterable:"",remote:"",placeholder:"选择询价客户","remote-method":e=>Vl(e,Ze.value.employeeId),loading:il.value,style:{width:"100%"}},{default:r((()=>[(m(!0),i(C,null,V(tl.value,(e=>(m(),v(R,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","remote-method","loading"])])),_:1})])),_:1}),o(Rl,{span:8},{default:r((()=>[o(kl,{label:"出货客户"},{default:r((()=>[o(L,{modelValue:Ze.value.shippingClientIds,"onUpdate:modelValue":l[10]||(l[10]=e=>Ze.value.shippingClientIds=e),multiple:"",filterable:"",remote:"",placeholder:"选择出货客户","remote-method":e=>Vl(e,Ze.value.employeeId),loading:il.value,style:{width:"100%"}},{default:r((()=>[(m(!0),i(C,null,V(tl.value,(e=>(m(),v(R,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","remote-method","loading"])])),_:1})])),_:1}),o(Rl,{span:8},{default:r((()=>[o(kl,{label:"重点开发客户"},{default:r((()=>[o(L,{modelValue:Ze.value.keyDevelopmentClientIds,"onUpdate:modelValue":l[11]||(l[11]=e=>Ze.value.keyDevelopmentClientIds=e),multiple:"",filterable:"",remote:"",placeholder:"选择重点开发客户","remote-method":e=>Vl(e,Ze.value.employeeId),loading:il.value,style:{width:"100%"}},{default:r((()=>[(m(!0),i(C,null,V(tl.value,(e=>(m(),v(R,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","remote-method","loading"])])),_:1})])),_:1})])),_:1})]),n("div",Ye,[l[51]||(l[51]=n("h3",null,"责任心评级",-1)),o(kl,{label:"责任心评级",prop:"responsibilityLevel"},{default:r((()=>[o(ql,{modelValue:Ze.value.responsibilityLevel,"onUpdate:modelValue":l[12]||(l[12]=e=>Ze.value.responsibilityLevel=e)},{default:r((()=>[o(Ul,{value:"优秀"},{default:r((()=>l[48]||(l[48]=[g("优秀")]))),_:1}),o(Ul,{value:"中等"},{default:r((()=>l[49]||(l[49]=[g("中等")]))),_:1}),o(Ul,{value:"差"},{default:r((()=>l[50]||(l[50]=[g("差")]))),_:1})])),_:1},8,["modelValue"])])),_:1})]),n("div",Te,[l[57]||(l[57]=n("h3",null,"下班准备",-1)),o(kl,{label:"检查清单"},{default:r((()=>[o(Sl,{modelValue:Ze.value.endOfDayChecklistItems,"onUpdate:modelValue":l[13]||(l[13]=e=>Ze.value.endOfDayChecklistItems=e)},{default:r((()=>[o(El,{value:"整理完桌面"},{default:r((()=>l[52]||(l[52]=[g("整理完桌面")]))),_:1}),o(El,{value:"整理完50通预计电话邮件上级"},{default:r((()=>l[53]||(l[53]=[g("整理完50通预计电话邮件上级")]))),_:1}),o(El,{value:"会议已开完"},{default:r((()=>l[54]||(l[54]=[g("会议已开完")]))),_:1}),o(El,{value:"准备好明天工作资料"},{default:r((()=>l[55]||(l[55]=[g("准备好明天工作资料")]))),_:1}),o(El,{value:"问候领导后打卡离开"},{default:r((()=>l[56]||(l[56]=[g("问候领导后打卡离开")]))),_:1})])),_:1},8,["modelValue"])])),_:1})]),n("div",Me,[l[58]||(l[58]=n("h3",null,"工作记录",-1)),o(kl,{label:"今日效果",prop:"dailyResults"},{default:r((()=>[o(I,{modelValue:Ze.value.dailyResults,"onUpdate:modelValue":l[14]||(l[14]=e=>Ze.value.dailyResults=e),type:"textarea",rows:3,placeholder:"请输入今日工作效果",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])])),_:1}),o(kl,{label:"会议报告",prop:"meetingReport"},{default:r((()=>[o(I,{modelValue:Ze.value.meetingReport,"onUpdate:modelValue":l[15]||(l[15]=e=>Ze.value.meetingReport=e),type:"textarea",rows:3,placeholder:"请输入会议报告",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])])),_:1}),o(kl,{label:"工作日记",prop:"workDiary"},{default:r((()=>[o(I,{modelValue:Ze.value.workDiary,"onUpdate:modelValue":l[16]||(l[16]=e=>Ze.value.workDiary=e),type:"textarea",rows:3,placeholder:"请输入工作日记",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])])),_:1})]),n("div",Pe,[l[59]||(l[59]=n("h3",null,"评价",-1)),o(kl,{label:"评价内容"},{default:r((()=>[o(I,{modelValue:Ze.value.managerEvaluation,"onUpdate:modelValue":l[17]||(l[17]=e=>Ze.value.managerEvaluation=e),type:"textarea",rows:4,placeholder:"请输入对该员工的评价",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])])),_:1})])])),_:1},8,["model"])):_("",!0)])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-4b8681a5"]]);export{Je as default};
