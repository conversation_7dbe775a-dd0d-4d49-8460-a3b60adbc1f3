import{r as t}from"./request-Cm8Ap7dD.js";function o(o){return t({url:"/auth/employee/login",method:"post",data:o})}function i(){return t({url:"/auth/info",method:"get"})}function s(o){return t({url:"/auth/profile",method:"put",data:o})}function a(){return t({url:"/auth/logout",method:"post"})}const e=()=>t({url:"/notifications/popup",method:"get"}),n=()=>t({url:"/notifications/bell",method:"get"}),u=o=>t({url:`/notifications/${o}/dismiss`,method:"post"}),r=o=>t({url:`/notifications/${o}/read-in-bell`,method:"post"}),l=o=>t({url:"/notifications/dismiss-multiple",method:"post",data:{ids:o}}),d=()=>t({url:"/notifications/trigger-analysis",method:"post"});export{u as a,e as b,i as c,l as d,o as e,n as g,a as l,r as m,d as t,s as u};
