import{E as e,ar as t}from"./index-CGqeVPF3.js";function n(e,t){return function(){return e.apply(t,arguments)}}const{toString:r}=Object.prototype,{getPrototypeOf:o}=Object,s=(e=>t=>{const n=r.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),i=e=>(e=e.toLowerCase(),t=>s(t)===e),a=e=>t=>typeof t===e,{isArray:c}=Array,l=a("undefined");const u=i("ArrayBuffer");const f=a("string"),d=a("function"),p=a("number"),h=e=>null!==e&&"object"==typeof e,m=e=>{if("object"!==s(e))return!1;const t=o(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},y=i("Date"),b=i("File"),g=i("Blob"),w=i("FileList"),E=i("URLSearchParams"),[R,O,S,T]=["ReadableStream","Request","Response","Headers"].map(i);function A(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,o;if("object"!=typeof e&&(e=[e]),c(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let i;for(r=0;r<s;r++)i=o[r],t.call(null,e[i],i,e)}}function v(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const x="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,C=e=>!l(e)&&e!==x;const j=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&o(Uint8Array)),N=i("HTMLFormElement"),P=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),U=i("RegExp"),_=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};A(n,((n,o)=>{let s;!1!==(s=t(n,o,e))&&(r[o]=s||n)})),Object.defineProperties(e,r)};const F=i("AsyncFunction"),L=(B="function"==typeof setImmediate,k=d(x.postMessage),B?setImmediate:k?(D=`axios@${Math.random()}`,q=[],x.addEventListener("message",(({source:e,data:t})=>{e===x&&t===D&&q.length&&q.shift()()}),!1),e=>{q.push(e),x.postMessage(D,"*")}):e=>setTimeout(e));var B,k,D,q;const I="undefined"!=typeof queueMicrotask?queueMicrotask.bind(x):"undefined"!=typeof process&&process.nextTick||L,M={isArray:c,isArrayBuffer:u,isBuffer:function(e){return null!==e&&!l(e)&&null!==e.constructor&&!l(e.constructor)&&d(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||d(e.append)&&("formdata"===(t=s(e))||"object"===t&&d(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&u(e.buffer),t},isString:f,isNumber:p,isBoolean:e=>!0===e||!1===e,isObject:h,isPlainObject:m,isReadableStream:R,isRequest:O,isResponse:S,isHeaders:T,isUndefined:l,isDate:y,isFile:b,isBlob:g,isRegExp:U,isFunction:d,isStream:e=>h(e)&&d(e.pipe),isURLSearchParams:E,isTypedArray:j,isFileList:w,forEach:A,merge:function e(){const{caseless:t}=C(this)&&this||{},n={},r=(r,o)=>{const s=t&&v(n,o)||o;m(n[s])&&m(r)?n[s]=e(n[s],r):m(r)?n[s]=e({},r):c(r)?n[s]=r.slice():n[s]=r};for(let o=0,s=arguments.length;o<s;o++)arguments[o]&&A(arguments[o],r);return n},extend:(e,t,r,{allOwnKeys:o}={})=>(A(t,((t,o)=>{r&&d(t)?e[o]=n(t,r):e[o]=t}),{allOwnKeys:o}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let s,i,a;const c={};if(t=t||{},null==e)return t;do{for(s=Object.getOwnPropertyNames(e),i=s.length;i-- >0;)a=s[i],r&&!r(a,e,t)||c[a]||(t[a]=e[a],c[a]=!0);e=!1!==n&&o(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:s,kindOfTest:i,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(c(e))return e;let t=e.length;if(!p(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:N,hasOwnProperty:P,hasOwnProp:P,reduceDescriptors:_,freezeMethods:e=>{_(e,((t,n)=>{if(d(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];d(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return c(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:v,global:x,isContextDefined:C,isSpecCompliantForm:function(e){return!!(e&&d(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(h(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=c(e)?[]:{};return A(e,((e,t)=>{const s=n(e,r+1);!l(s)&&(o[t]=s)})),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:F,isThenable:e=>e&&(h(e)||d(e))&&d(e.then)&&d(e.catch),setImmediate:L,asap:I};function z(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}M.inherits(z,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:M.toJSONObject(this.config),code:this.code,status:this.status}}});const H=z.prototype,J={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{J[e]={value:e}})),Object.defineProperties(z,J),Object.defineProperty(H,"isAxiosError",{value:!0}),z.from=(e,t,n,r,o,s)=>{const i=Object.create(H);return M.toFlatObject(e,i,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),z.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};function W(e){return M.isPlainObject(e)||M.isArray(e)}function K(e){return M.endsWith(e,"[]")?e.slice(0,-2):e}function V(e,t,n){return e?e.concat(t).map((function(e,t){return e=K(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const $=M.toFlatObject(M,{},null,(function(e){return/^is[A-Z]/.test(e)}));function X(e,t,n){if(!M.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=M.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!M.isUndefined(t[e])}))).metaTokens,o=n.visitor||l,s=n.dots,i=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&M.isSpecCompliantForm(t);if(!M.isFunction(o))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(M.isDate(e))return e.toISOString();if(!a&&M.isBlob(e))throw new z("Blob is not supported. Use a Buffer instead.");return M.isArrayBuffer(e)||M.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,n,o){let a=e;if(e&&!o&&"object"==typeof e)if(M.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(M.isArray(e)&&function(e){return M.isArray(e)&&!e.some(W)}(e)||(M.isFileList(e)||M.endsWith(n,"[]"))&&(a=M.toArray(e)))return n=K(n),a.forEach((function(e,r){!M.isUndefined(e)&&null!==e&&t.append(!0===i?V([n],r,s):null===i?n:n+"[]",c(e))})),!1;return!!W(e)||(t.append(V(o,n,s),c(e)),!1)}const u=[],f=Object.assign($,{defaultVisitor:l,convertValue:c,isVisitable:W});if(!M.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!M.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),M.forEach(n,(function(n,s){!0===(!(M.isUndefined(n)||null===n)&&o.call(t,n,M.isString(s)?s.trim():s,r,f))&&e(n,r?r.concat(s):[s])})),u.pop()}}(e),t}function G(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function Q(e,t){this._pairs=[],e&&X(e,this,t)}const Z=Q.prototype;function Y(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ee(e,t,n){if(!t)return e;const r=n&&n.encode||Y;M.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(s=o?o(t,n):M.isURLSearchParams(t)?t.toString():new Q(t,n).toString(r),s){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}Z.append=function(e,t){this._pairs.push([e,t])},Z.toString=function(e){const t=e?function(t){return e.call(this,t,G)}:G;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};class te{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){M.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}const ne={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},re={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:Q,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},oe="undefined"!=typeof window&&"undefined"!=typeof document,se="object"==typeof navigator&&navigator||void 0,ie=oe&&(!se||["ReactNative","NativeScript","NS"].indexOf(se.product)<0),ae="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ce=oe&&window.location.href||"http://localhost",le={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:oe,hasStandardBrowserEnv:ie,hasStandardBrowserWebWorkerEnv:ae,navigator:se,origin:ce},Symbol.toStringTag,{value:"Module"})),...re};function ue(e){function t(e,n,r,o){let s=e[o++];if("__proto__"===s)return!0;const i=Number.isFinite(+s),a=o>=e.length;if(s=!s&&M.isArray(r)?r.length:s,a)return M.hasOwnProp(r,s)?r[s]=[r[s],n]:r[s]=n,!i;r[s]&&M.isObject(r[s])||(r[s]=[]);return t(e,n,r[s],o)&&M.isArray(r[s])&&(r[s]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}(r[s])),!i}if(M.isFormData(e)&&M.isFunction(e.entries)){const n={};return M.forEachEntry(e,((e,r)=>{t(function(e){return M.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null}const fe={transitional:ne,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=M.isObject(e);o&&M.isHTMLForm(e)&&(e=new FormData(e));if(M.isFormData(e))return r?JSON.stringify(ue(e)):e;if(M.isArrayBuffer(e)||M.isBuffer(e)||M.isStream(e)||M.isFile(e)||M.isBlob(e)||M.isReadableStream(e))return e;if(M.isArrayBufferView(e))return e.buffer;if(M.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return X(e,new le.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return le.isNode&&M.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((s=M.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return X(s?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(M.isString(e))try{return(t||JSON.parse)(e),M.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||fe.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(M.isResponse(e)||M.isReadableStream(e))return e;if(e&&M.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(o){if(n){if("SyntaxError"===o.name)throw z.from(o,z.ERR_BAD_RESPONSE,this,null,this.response);throw o}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:le.classes.FormData,Blob:le.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};M.forEach(["delete","get","head","post","put","patch"],(e=>{fe.headers[e]={}}));const de=M.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),pe=Symbol("internals");function he(e){return e&&String(e).trim().toLowerCase()}function me(e){return!1===e||null==e?e:M.isArray(e)?e.map(me):String(e)}function ye(e,t,n,r,o){return M.isFunction(r)?r.call(this,t,n):(o&&(t=n),M.isString(t)?M.isString(r)?-1!==t.indexOf(r):M.isRegExp(r)?r.test(t):void 0:void 0)}let be=class{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=he(t);if(!o)throw new Error("header name must be a non-empty string");const s=M.findKey(r,o);(!s||void 0===r[s]||!0===n||void 0===n&&!1!==r[s])&&(r[s||t]=me(e))}const s=(e,t)=>M.forEach(e,((e,n)=>o(e,n,t)));if(M.isPlainObject(e)||e instanceof this.constructor)s(e,t);else if(M.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))s((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach((function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&de[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(M.isHeaders(e))for(const[i,a]of e.entries())o(a,i,n);else null!=e&&o(t,e,n);return this}get(e,t){if(e=he(e)){const n=M.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(M.isFunction(t))return t.call(this,e,n);if(M.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=he(e)){const n=M.findKey(this,e);return!(!n||void 0===this[n]||t&&!ye(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=he(e)){const o=M.findKey(n,e);!o||t&&!ye(0,n[o],o,t)||(delete n[o],r=!0)}}return M.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!ye(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return M.forEach(this,((r,o)=>{const s=M.findKey(n,o);if(s)return t[s]=me(r),void delete t[o];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(o):String(o).trim();i!==o&&delete t[o],t[i]=me(r),n[i]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return M.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&M.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[pe]=this[pe]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=he(e);t[r]||(!function(e,t){const n=M.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})}))}(n,e),t[r]=!0)}return M.isArray(e)?e.forEach(r):r(e),this}};function ge(e,t){const n=this||fe,r=t||n,o=be.from(r.headers);let s=r.data;return M.forEach(e,(function(e){s=e.call(n,s,o.normalize(),t?t.status:void 0)})),o.normalize(),s}function we(e){return!(!e||!e.__CANCEL__)}function Ee(e,t,n){z.call(this,null==e?"canceled":e,z.ERR_CANCELED,t,n),this.name="CanceledError"}function Re(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new z("Request failed with status code "+n.status,[z.ERR_BAD_REQUEST,z.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}be.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),M.reduceDescriptors(be.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),M.freezeMethods(be),M.inherits(Ee,z,{__CANCEL__:!0});const Oe=(e,t,n=3)=>{let r=0;const o=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,s=0,i=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),l=r[i];o||(o=c),n[s]=a,r[s]=c;let u=i,f=0;for(;u!==s;)f+=n[u++],u%=e;if(s=(s+1)%e,s===i&&(i=(i+1)%e),c-o<t)return;const d=l&&c-l;return d?Math.round(1e3*f/d):void 0}}(50,250);return function(e,t){let n,r,o=0,s=1e3/t;const i=(t,s=Date.now())=>{o=s,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),a=t-o;a>=s?i(e,t):(n=e,r||(r=setTimeout((()=>{r=null,i(n)}),s-a)))},()=>n&&i(n)]}((n=>{const s=n.loaded,i=n.lengthComputable?n.total:void 0,a=s-r,c=o(a);r=s;e({loaded:s,total:i,progress:i?s/i:void 0,bytes:a,rate:c||void 0,estimated:c&&i&&s<=i?(i-s)/c:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})}),n)},Se=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Te=e=>(...t)=>M.asap((()=>e(...t))),Ae=le.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,le.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(le.origin),le.navigator&&/(msie|trident)/i.test(le.navigator.userAgent)):()=>!0,ve=le.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];M.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),M.isString(r)&&i.push("path="+r),M.isString(o)&&i.push("domain="+o),!0===s&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function xe(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Ce=e=>e instanceof be?{...e}:e;function je(e,t){t=t||{};const n={};function r(e,t,n,r){return M.isPlainObject(e)&&M.isPlainObject(t)?M.merge.call({caseless:r},e,t):M.isPlainObject(t)?M.merge({},t):M.isArray(t)?t.slice():t}function o(e,t,n,o){return M.isUndefined(t)?M.isUndefined(e)?void 0:r(void 0,e,0,o):r(e,t,0,o)}function s(e,t){if(!M.isUndefined(t))return r(void 0,t)}function i(e,t){return M.isUndefined(t)?M.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,o,s){return s in t?r(n,o):s in e?r(void 0,n):void 0}const c={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(e,t,n)=>o(Ce(e),Ce(t),0,!0)};return M.forEach(Object.keys(Object.assign({},e,t)),(function(r){const s=c[r]||o,i=s(e[r],t[r],r);M.isUndefined(i)&&s!==a||(n[r]=i)})),n}const Ne=e=>{const t=je({},e);let n,{data:r,withXSRFToken:o,xsrfHeaderName:s,xsrfCookieName:i,headers:a,auth:c}=t;if(t.headers=a=be.from(a),t.url=ee(xe(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),M.isFormData(r))if(le.hasStandardBrowserEnv||le.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(le.hasStandardBrowserEnv&&(o&&M.isFunction(o)&&(o=o(t)),o||!1!==o&&Ae(t.url))){const e=s&&i&&ve.read(i);e&&a.set(s,e)}return t},Pe="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=Ne(e);let o=r.data;const s=be.from(r.headers).normalize();let i,a,c,l,u,{responseType:f,onUploadProgress:d,onDownloadProgress:p}=r;function h(){l&&l(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let m=new XMLHttpRequest;function y(){if(!m)return;const r=be.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Re((function(e){t(e),h()}),(function(e){n(e),h()}),{data:f&&"text"!==f&&"json"!==f?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=y:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(y)},m.onabort=function(){m&&(n(new z("Request aborted",z.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new z("Network Error",z.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||ne;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new z(t,o.clarifyTimeoutError?z.ETIMEDOUT:z.ECONNABORTED,e,m)),m=null},void 0===o&&s.setContentType(null),"setRequestHeader"in m&&M.forEach(s.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),M.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),f&&"json"!==f&&(m.responseType=r.responseType),p&&([c,u]=Oe(p,!0),m.addEventListener("progress",c)),d&&m.upload&&([a,l]=Oe(d),m.upload.addEventListener("progress",a),m.upload.addEventListener("loadend",l)),(r.cancelToken||r.signal)&&(i=t=>{m&&(n(!t||t.type?new Ee(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const b=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);b&&-1===le.protocols.indexOf(b)?n(new z("Unsupported protocol "+b+":",z.ERR_BAD_REQUEST,e)):m.send(o||null)}))},Ue=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const o=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof z?t:new Ee(t instanceof Error?t.message:t))}};let s=t&&setTimeout((()=>{s=null,o(new z(`timeout ${t} of ms exceeded`,z.ETIMEDOUT))}),t);const i=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)})),e=null)};e.forEach((e=>e.addEventListener("abort",o)));const{signal:a}=r;return a.unsubscribe=()=>M.asap(i),a}},_e=function*(e,t){let n=e.byteLength;if(n<t)return void(yield e);let r,o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},Fe=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Le=(e,t,n,r)=>{const o=async function*(e,t){for await(const n of Fe(e))yield*_e(n,t)}(e,t);let s,i=0,a=e=>{s||(s=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await o.next();if(t)return a(),void e.close();let s=r.byteLength;if(n){let e=i+=s;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw a(t),t}},cancel:e=>(a(e),o.return())},{highWaterMark:2})},Be="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,ke=Be&&"function"==typeof ReadableStream,De=Be&&("function"==typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),qe=(e,...t)=>{try{return!!e(...t)}catch(n){return!1}},Ie=ke&&qe((()=>{let e=!1;const t=new Request(le.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),Me=ke&&qe((()=>M.isReadableStream(new Response("").body))),ze={stream:Me&&(e=>e.body)};var He;Be&&(He=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!ze[e]&&(ze[e]=M.isFunction(He[e])?t=>t[e]():(t,n)=>{throw new z(`Response type '${e}' is not supported`,z.ERR_NOT_SUPPORT,n)})})));const Je=async(e,t)=>{const n=M.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(M.isBlob(e))return e.size;if(M.isSpecCompliantForm(e)){const t=new Request(le.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return M.isArrayBufferView(e)||M.isArrayBuffer(e)?e.byteLength:(M.isURLSearchParams(e)&&(e+=""),M.isString(e)?(await De(e)).byteLength:void 0)})(t):n},We={http:null,xhr:Pe,fetch:Be&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:a,onUploadProgress:c,responseType:l,headers:u,withCredentials:f="same-origin",fetchOptions:d}=Ne(e);l=l?(l+"").toLowerCase():"text";let p,h=Ue([o,s&&s.toAbortSignal()],i);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let y;try{if(c&&Ie&&"get"!==n&&"head"!==n&&0!==(y=await Je(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(M.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=Se(y,Oe(Te(c)));r=Le(n.body,65536,e,t)}}M.isString(f)||(f=f?"include":"omit");const o="credentials"in Request.prototype;p=new Request(t,{...d,signal:h,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:o?f:void 0});let s=await fetch(p);const i=Me&&("stream"===l||"response"===l);if(Me&&(a||i&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=s[t]}));const t=M.toFiniteNumber(s.headers.get("content-length")),[n,r]=a&&Se(t,Oe(Te(a),!0))||[];s=new Response(Le(s.body,65536,n,(()=>{r&&r(),m&&m()})),e)}l=l||"text";let b=await ze[M.findKey(ze,l)||"text"](s,e);return!i&&m&&m(),await new Promise(((t,n)=>{Re(t,n,{data:b,headers:be.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:p})}))}catch(b){if(m&&m(),b&&"TypeError"===b.name&&/fetch/i.test(b.message))throw Object.assign(new z("Network Error",z.ERR_NETWORK,e,p),{cause:b.cause||b});throw z.from(b,b&&b.code,e,p)}})};M.forEach(We,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}}));const Ke=e=>`- ${e}`,Ve=e=>M.isFunction(e)||null===e||!1===e,$e=e=>{e=M.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){let t;if(n=e[s],r=n,!Ve(n)&&(r=We[(t=String(n)).toLowerCase()],void 0===r))throw new z(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+s]=r}if(!r){const e=Object.entries(o).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));throw new z("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(Ke).join("\n"):" "+Ke(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function Xe(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ee(null,e)}function Ge(e){Xe(e),e.headers=be.from(e.headers),e.data=ge.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return $e(e.adapter||fe.adapter)(e).then((function(t){return Xe(e),t.data=ge.call(e,e.transformResponse,t),t.headers=be.from(t.headers),t}),(function(t){return we(t)||(Xe(e),t&&t.response&&(t.response.data=ge.call(e,e.transformResponse,t.response),t.response.headers=be.from(t.response.headers))),Promise.reject(t)}))}const Qe="1.8.4",Ze={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Ze[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const Ye={};Ze.transitional=function(e,t,n){return(r,o,s)=>{if(!1===e)throw new z(function(e,t){return"[Axios v1.8.4] Transitional option '"+e+"'"+t+(n?". "+n:"")}(o," has been removed"+(t?" in "+t:"")),z.ERR_DEPRECATED);return t&&!Ye[o]&&(Ye[o]=!0),!e||e(r,o,s)}},Ze.spelling=function(e){return(e,t)=>!0};const et={assertOptions:function(e,t,n){if("object"!=typeof e)throw new z("options must be an object",z.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const t=e[s],n=void 0===t||i(t,s,e);if(!0!==n)throw new z("option "+s+" must be "+n,z.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new z("Unknown option "+s,z.ERR_BAD_OPTION)}},validators:Ze},tt=et.validators;let nt=class{constructor(e){this.defaults=e,this.interceptors={request:new te,response:new te}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(r){}}throw n}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=je(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&et.assertOptions(n,{silentJSONParsing:tt.transitional(tt.boolean),forcedJSONParsing:tt.transitional(tt.boolean),clarifyTimeoutError:tt.transitional(tt.boolean)},!1),null!=r&&(M.isFunction(r)?t.paramsSerializer={serialize:r}:et.assertOptions(r,{encode:tt.function,serialize:tt.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),et.assertOptions(t,{baseUrl:tt.spelling("baseURL"),withXsrfToken:tt.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=o&&M.merge(o.common,o[t.method]);o&&M.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete o[e]})),t.headers=be.concat(s,o);const i=[];let a=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,i.unshift(e.fulfilled,e.rejected))}));const c=[];let l;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let u,f=0;if(!a){const e=[Ge.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,c),u=e.length,l=Promise.resolve(t);f<u;)l=l.then(e[f++],e[f++]);return l}u=i.length;let d=t;for(f=0;f<u;){const e=i[f++],t=i[f++];try{d=e(d)}catch(p){t.call(this,p);break}}try{l=Ge.call(this,d)}catch(p){return Promise.reject(p)}for(f=0,u=c.length;f<u;)l=l.then(c[f++],c[f++]);return l}getUri(e){return ee(xe((e=je(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}};M.forEach(["delete","get","head","options"],(function(e){nt.prototype[e]=function(t,n){return this.request(je(n||{},{method:e,url:t,data:(n||{}).data}))}})),M.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,o){return this.request(je(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}nt.prototype[e]=t(),nt.prototype[e+"Form"]=t(!0)}));const rt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(rt).forEach((([e,t])=>{rt[t]=e}));const ot=function e(t){const r=new nt(t),o=n(nt.prototype.request,r);return M.extend(o,nt.prototype,r,{allOwnKeys:!0}),M.extend(o,r,null,{allOwnKeys:!0}),o.create=function(n){return e(je(t,n))},o}(fe);ot.Axios=nt,ot.CanceledError=Ee,ot.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,o){n.reason||(n.reason=new Ee(e,r,o),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;return{token:new e((function(e){t=e})),cancel:t}}},ot.isCancel=we,ot.VERSION=Qe,ot.toFormData=X,ot.AxiosError=z,ot.Cancel=ot.CanceledError,ot.all=function(e){return Promise.all(e)},ot.spread=function(e){return function(t){return e.apply(null,t)}},ot.isAxiosError=function(e){return M.isObject(e)&&!0===e.isAxiosError},ot.mergeConfig=je,ot.AxiosHeaders=be,ot.formToJSON=e=>ue(M.isHTMLForm(e)?new FormData(e):e),ot.getAdapter=$e,ot.HttpStatusCode=rt,ot.default=ot;const{Axios:st,AxiosError:it,CanceledError:at,isCancel:ct,CancelToken:lt,VERSION:ut,all:ft,Cancel:dt,isAxiosError:pt,spread:ht,toFormData:mt,AxiosHeaders:yt,HttpStatusCode:bt,formToJSON:gt,getAdapter:wt,mergeConfig:Et}=ot,Rt=ot.create({baseURL:"/api",timeout:3e4});Rt.interceptors.request.use((e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e}),(e=>Promise.reject(e))),Rt.interceptors.response.use((n=>{const{code:r,message:o,data:s}=n.data;return 200!==r?400===r?n.data:(e.error(o||"操作失败"),401===r&&(localStorage.removeItem("token"),t.push("/login")),Promise.reject(new Error(o||"操作失败"))):n.data}),(n=>{let r=n.message||"请求失败";return n.response&&(401===n.response.status?(localStorage.removeItem("token"),t.push("/login"),r="登录已过期，请重新登录"):404===n.response.status?r="请求的资源不存在":500===n.response.status&&(r="服务器内部错误")),e.error(r),Promise.reject(n)}));export{Rt as r};
