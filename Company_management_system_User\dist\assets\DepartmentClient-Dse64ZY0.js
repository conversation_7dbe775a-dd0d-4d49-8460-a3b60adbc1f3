import{_ as e,a,r as l,o as t,c as r,d as n,Z as o,f as i,e as u,i as s,j as c,$ as d,Y as p,z as v,E as g,m,g as h,a0 as f,F as w,A as b,n as y,a1 as _,t as x}from"./index-CGqeVPF3.js";import{c as z}from"./client-0FvGXzol.js";import{c as k}from"./department-DD9JCT-L.js";import"./request-Cm8Ap7dD.js";const V={class:"department-content-container"},C={class:"toolbar"},N={class:"filter-actions"},j={class:"pagination-container"},S={key:0,class:"empty-data"},A=e({__name:"DepartmentClient",setup(e){const A=a([]),I=a(!1),T=a(""),U=a(""),P=a(""),R=a(""),D=a(""),E=a(!1),K=a([]),$=a([]),q=a([]),F={checkStrictly:!0,emitPath:!1,expandTrigger:"hover",multiple:!0},L=a([]),Y=l({page:1,size:10,total:0}),Z=e=>{switch(e){case"海运":return"primary";case"空运":return"success";case"散货":return"warning";case"快递":return"info";default:return""}},B=e=>{switch(e){case"未审核":return"info";case"审核中":return"warning";case"审核通过":return"success";case"已拒绝":return"danger";default:return""}},G=e=>{switch(e){case"报价中":return"warning";case"已合作":return"success";default:return"info"}},H=async()=>{if(!q.value||0===q.value.length)return g.warning("请先选择要查看的部门"),A.value=[],void(Y.total=0);let e=!1;for(const l of q.value)if(!M(l)){e=!0;break}if(e)return g.warning("您只能查看自己负责的部门及其下级部门的客户数据"),A.value=[],void(Y.total=0);try{I.value=!0;let e={page:Y.page,size:Y.size,departmentIds:q.value};T.value&&""!==T.value.trim()&&(e.clientName=T.value.trim()),U.value&&""!==U.value.trim()&&(e.employeeName=U.value.trim()),P.value&&(e.category=P.value),R.value&&(e.status=R.value),D.value&&(e.clientStatus=D.value);const a=await z(e);200===a.code&&a.data?(Array.isArray(a.data.list)?A.value=a.data.list:Array.isArray(a.data.records)?A.value=a.data.records:Array.isArray(a.data)?A.value=a.data:A.value=[],J(a.data)):(g.error(a.message||"获取部门客户数据失败"),A.value=[],Y.total=0)}catch(a){g.error("获取部门客户列表失败，请稍后再试"),A.value=[],Y.total=0}finally{I.value=!1}},J=e=>{Y.total=e.total||0,void 0!==e.pageNum?Y.page=e.pageNum:void 0!==e.page&&(Y.page=e.page),void 0!==e.pageSize?Y.size=e.pageSize:void 0!==e.size&&(Y.size=e.size)},M=e=>{if(L.value.includes(e))return!0;for(const a of K.value)if(L.value.includes(a.value)){const l=(e,a)=>{if(!e||0===e.length)return!1;for(const t of e){if(t.value===a)return!0;if(t.children&&l(t.children,a))return!0}return!1};if(l(a.children,e))return!0}return!1},O=e=>{const a=[],l=(e,a)=>{if(!e||!e.length)return null;for(const t of e){if(t.value===a)return t;if(t.children&&t.children.length){const e=l(t.children,a);if(e)return e}}return null},t=e=>{if(e.children&&e.children.length)for(const l of e.children)a.push(l.value),t(l)},r=l(K.value,e);return r&&t(r),a},Q=e=>e&&e.length?e.map((e=>({value:e.departmentId,label:e.departmentName,children:Q(e.children||[])}))):[],W=e=>{if(!e||0===e.length)return q.value=[],$.value=[],Y.page=1,void H();const a=q.value||[],l=e.filter((e=>!a.includes(e))),t=a.filter((a=>!e.includes(a)));let r=[...e],n=[...e];for(const o of l){const e=O(o);e.length>0&&e.forEach((e=>{r.includes(e)||r.push(e),n.includes(e)||n.push(e)}))}for(const o of t){const e=O(o);e.length>0&&(r=r.filter((a=>!e.includes(a))),n=n.filter((a=>!e.includes(a))))}$.value=n,q.value=r,Y.page=1,H()},X=async()=>{Y.page=1,await H()},ee=async()=>{T.value="",U.value="",P.value="",R.value="",D.value="",Y.page=1,q.value&&q.value.length>0&&await H()},ae=e=>{Y.page=e,H()},le=e=>{Y.size=e,Y.page=1,H()},te=e=>{if(!e)return"-";const a=new Date(e);return isNaN(a.getTime())?e:a.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")},re=a([{value:"海运",label:"海运"},{value:"散货",label:"散货"},{value:"空运",label:"空运"},{value:"快递",label:"快递"}]),ne=a([{value:"未审核",label:"未审核"},{value:"审核中",label:"审核中"},{value:"审核通过",label:"审核通过"},{value:"已拒绝",label:"已拒绝"}]),oe=a([{value:"报价中",label:"报价中"},{value:"已合作",label:"已合作"}]);return t((async()=>{await(async()=>{try{E.value=!0;const e=await k();if(200===e.code)if(K.value=(e.data||[]).map((e=>({value:e.departmentId,label:e.departmentName,children:Q(e.children||[])}))),L.value=(e.data||[]).map((e=>e.departmentId)),L.value.length>0){const e=L.value[0],a=O(e);$.value=[e,...a],q.value=[e,...a],await H()}else g.warning("您没有任何可以查看客户的部门");else g.error(e.message||"获取部门信息失败")}catch(e){g.error("加载部门信息失败，请稍后再试")}finally{E.value=!1}})()})),(e,a)=>{const l=s("el-cascader"),t=s("el-button"),g=s("el-input"),z=s("el-option"),k=s("el-select"),q=s("el-icon"),L=s("el-table-column"),H=s("el-tag"),J=s("el-tooltip"),M=s("el-table"),O=s("el-pagination"),Q=s("el-empty"),ie=p("loading");return m(),r("div",V,[n("div",C,[n("div",N,[u(l,{modelValue:$.value,"onUpdate:modelValue":a[0]||(a[0]=e=>$.value=e),options:K.value,props:F,placeholder:"请选择您负责的部门",clearable:"",loading:E.value,onChange:W,style:{width:"280px","margin-right":"10px"},"collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":2},null,8,["modelValue","options","loading"]),u(g,{modelValue:T.value,"onUpdate:modelValue":a[1]||(a[1]=e=>T.value=e),placeholder:"客户名称",clearable:"",onKeyup:d(X,["enter"]),onClear:X,style:{width:"180px","margin-right":"10px"}},{append:c((()=>[u(t,{icon:h(f),onClick:X},null,8,["icon"])])),_:1},8,["modelValue"]),u(g,{modelValue:U.value,"onUpdate:modelValue":a[2]||(a[2]=e=>U.value=e),placeholder:"负责员工",clearable:"",onKeyup:d(X,["enter"]),onClear:X,style:{width:"180px","margin-right":"10px"}},{append:c((()=>[u(t,{icon:h(f),onClick:X},null,8,["icon"])])),_:1},8,["modelValue"]),u(k,{modelValue:P.value,"onUpdate:modelValue":a[3]||(a[3]=e=>P.value=e),placeholder:"客户分类",clearable:"",onChange:X,style:{width:"150px","margin-right":"10px"}},{default:c((()=>[(m(!0),r(w,null,b(re.value,(e=>(m(),v(z,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),u(k,{modelValue:R.value,"onUpdate:modelValue":a[4]||(a[4]=e=>R.value=e),placeholder:"审批状态",clearable:"",onChange:X,style:{width:"150px","margin-right":"10px"}},{default:c((()=>[(m(!0),r(w,null,b(ne.value,(e=>(m(),v(z,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),u(k,{modelValue:D.value,"onUpdate:modelValue":a[5]||(a[5]=e=>D.value=e),placeholder:"客户状态",clearable:"",onChange:X,style:{width:"150px","margin-right":"10px"}},{default:c((()=>[(m(!0),r(w,null,b(oe.value,(e=>(m(),v(z,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),u(t,{onClick:ee},{default:c((()=>[u(q,null,{default:c((()=>[u(h(_))])),_:1}),a[6]||(a[6]=y(" 重置 "))])),_:1})])]),o((m(),v(M,{data:A.value,border:"",stripe:"",style:{width:"100%"},"row-key":"clientId","max-height":"calc(100vh - 280px)",class:"custom-table"},{default:c((()=>[u(L,{label:"序号","min-width":"70",align:"center"},{default:c((e=>[y(x((Y.page-1)*Y.size+e.$index+1),1)])),_:1}),u(L,{label:"客户名称",prop:"name","min-width":"150",align:"center","show-overflow-tooltip":""}),u(L,{label:"联系人",prop:"contactPerson","min-width":"120",align:"center","show-overflow-tooltip":""},{default:c((({row:e})=>[y(x(e.contactPerson||"----"),1)])),_:1}),u(L,{label:"邮箱",prop:"email","min-width":"180",align:"center","show-overflow-tooltip":""}),u(L,{label:"电话",prop:"phone","min-width":"120",align:"center","show-overflow-tooltip":""}),u(L,{label:"国籍/地区",prop:"nationality","min-width":"100",align:"center","show-overflow-tooltip":""}),u(L,{label:"部门",prop:"departmentName","min-width":"120",align:"center","show-overflow-tooltip":""}),u(L,{label:"负责员工",prop:"employeeName","min-width":"100",align:"center","show-overflow-tooltip":""}),u(L,{label:"客户分类",prop:"category","min-width":"100",align:"center","show-overflow-tooltip":""},{default:c((({row:e})=>[u(H,{type:Z(e.category),effect:"plain"},{default:c((()=>[y(x(e.category||"待选择"),1)])),_:2},1032,["type"])])),_:1}),u(L,{label:"客户状态",prop:"clientStatus","min-width":"100",align:"center","show-overflow-tooltip":""},{default:c((({row:e})=>[u(H,{type:G(e.clientStatus),effect:"plain"},{default:c((()=>[y(x(e.clientStatus||"报价中"),1)])),_:2},1032,["type"])])),_:1}),u(L,{label:"审批状态",prop:"status","min-width":"100",align:"center","show-overflow-tooltip":""},{default:c((({row:e})=>[u(J,{class:"item",effect:"dark",content:e.rejectRemark,placement:"top",disabled:!("已拒绝"===e.status&&e.rejectRemark&&""!==e.rejectRemark.trim())},{default:c((()=>[u(H,{type:B(e.status),effect:"light"},{default:c((()=>{return[y(x((a=e.status,"已合作"===a?"审核通过":a||"----")),1)];var a})),_:2},1032,["type"])])),_:2},1032,["content","disabled"])])),_:1}),u(L,{label:"备注",prop:"remark","min-width":"150",align:"center","show-overflow-tooltip":""},{default:c((({row:e})=>[y(x(e.remark||"----"),1)])),_:1}),u(L,{label:"操单时间",prop:"operationTime","min-width":"160",align:"center","show-overflow-tooltip":""},{default:c((({row:e})=>[y(x(te(e.operationTime)),1)])),_:1}),u(L,{label:"创建时间",prop:"createTime","min-width":"160",align:"center","show-overflow-tooltip":""},{default:c((({row:e})=>[y(x(te(e.createTime)),1)])),_:1})])),_:1},8,["data"])),[[ie,I.value]]),n("div",j,[u(O,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":Y.page,"page-size":Y.size,total:Y.total,"page-sizes":[10,20,50,100],onSizeChange:le,onCurrentChange:ae},null,8,["current-page","page-size","total"])]),I.value||0!==A.value.length?i("",!0):(m(),r("div",S,[u(Q,{description:"暂无客户数据"})]))])}}},[["__scopeId","data-v-d6bcc1b3"]]);export{A as default};
