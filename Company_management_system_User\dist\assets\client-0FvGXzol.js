import{r as t}from"./request-Cm8Ap7dD.js";function e(e){return t({url:"/client/my/page",method:"get",params:{page:e.pageNum,limit:e.pageSize,name:e.name,category:e.category,status:e.status,clientStatus:e.clientStatus}})}function a(e){return t({url:"/client",method:"post",data:e})}function n(e){return t({url:"/client",method:"put",data:e})}function s(e,a){return t({url:"/client/update-status",method:"post",data:{clientId:e,status:a}})}function r(e){return t({url:`/client/${e}`,method:"get"})}function u(e){return t({url:"/client/department-clients",method:"post",data:{page:e.page,size:e.size,departmentIds:e.departmentIds,clientName:e.clientName,employeeName:e.employeeName,category:e.category,status:e.status}})}export{a,s as b,u as c,e as f,r as g,n as u};
