import request from './request'

/**
 * {{CHENGQI: 销售日报API接口}}
 * {{CHENGQI: 任务ID: P3-LD-008}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 销售日报前端API调用封装}}
 */

// 提交或更新销售日报
export function submitSalesReport(data) {
    return request({
        url: '/sales-report',
        method: 'post',
        data
    })
}

// 获取指定日期的个人日报
export function getSalesReportByDate(date) {
    return request({
        url: `/sales-report/${date}`,
        method: 'get'
    })
}

// 获取日报详情
export function getSalesReportById(id) {
    return request({
        url: `/sales-report/detail/${id}`,
        method: 'get'
    })
}

// 删除销售日报
export function deleteSalesReport(id) {
    return request({
        url: `/sales-report/${id}`,
        method: 'delete'
    })
}

// 分页查询我的销售日报列表（只返回当前用户的日报）
export function getMySalesReportPage(params) {
    return request({
        url: '/sales-report/my-reports',
        method: 'get',
        params
    })
}

// 分页查询销售日报列表（部门和管理员使用）
export function getSalesReportPage(params) {
    return request({
        url: '/sales-report/page',
        method: 'get',
        params
    })
}

// 获取员工负责的客户列表（支持搜索）
export function getMyClients(params = {}) {
    return request({
        url: '/sales-report/my-clients',
        method: 'get',
        params
    })
}

// 获取员工客户统计信息（年度新客户、当月新客户、距离上次新客户天数）
export function getMyClientStatistics() {
    return request({
        url: '/sales-report/my-client-statistics',
        method: 'get'
    })
}

// 获取最近的日报
export function getRecentReports(limit = 10) {
    return request({
        url: '/sales-report/recent',
        method: 'get',
        params: { limit }
    })
}

// 获取统计数据
export function getSalesReportStatistics(params) {
    return request({
        url: '/sales-report/statistics',
        method: 'get',
        params
    })
}

// 获取责任心评级分布
export function getResponsibilityDistribution(params) {
    return request({
        url: '/sales-report/responsibility-distribution',
        method: 'get',
        params
    })
}

// 获取检查清单完成度统计
export function getChecklistStats(params) {
    return request({
        url: '/sales-report/checklist-stats',
        method: 'get',
        params
    })
}

// 获取部门销售日报分页列表（部门主管权限）
// 使用通用的分页接口，但会根据当前用户的部门权限进行过滤
export function getDepartmentSalesReportPage(params) {
    return request({
        url: '/sales-report/page',
        method: 'get',
        params
    })
}

// 部门负责人编辑员工日报
export function editDepartmentReport(data) {
    return request({
        url: '/sales-report/department/edit',
        method: 'put',
        data
    })
}

// 更新日报评价
export function updateReportEvaluation(reportId, managerEvaluation) {
    return request({
        url: '/sales-report/evaluation',
        method: 'put',
        params: {
            reportId,
            managerEvaluation
        }
    })
}

// 获取特定员工的客户列表（部门负责人权限）
export function getDepartmentEmployeeClients(employeeId, params = {}) {
    return request({
        url: `/sales-report/department/employee-clients/${employeeId}`,
        method: 'get',
        params
    })
}

// 获取特定日报的详细信息（部门负责人权限）
export function getDepartmentReportDetailById(reportId) {
    return request({
        url: `/sales-report/department/report-detail/${reportId}`,
        method: 'get'
    })
}

// {{CHENGQI: API接口封装完成}}
