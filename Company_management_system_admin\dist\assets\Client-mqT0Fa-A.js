import{a0 as e,_ as a,a as l,r as t,o,c as n,e as r,R as i,f as u,k as d,S as c,j as s,T as p,K as m,E as v,s as g,m as y,h,U as f,I as w,J as b,n as _,V as k,W as V,g as x,a1 as T,t as I,Y as S,Z as C,a2 as U,O as j}from"./index-BDR0Pmj6.js";import{g as N,b as P}from"./employee-DbFXp2d5.js";import{b as z}from"./department-jpUZgat6.js";function B(a){return e({url:"/client/update-status",method:"post",data:a})}function q(a){return e({url:"/client/batch-update-status",method:"post",data:a})}const R={class:"client-container"},$={class:"toolbar"},A={class:"search-box"},Y={class:"action-box"},D={class:"operation-buttons"},K={class:"pagination-container"},E={class:"empty-text"},H={class:"dialog-footer"},L={class:"action-toolbar"},M={class:"approval-action-buttons"},O=a({__name:"Client",setup(a){const O=l([]),W=l(!0),Z=l(""),F=l(""),J=l(""),G=l([]);l(!1);const Q=l(""),X=l(""),ee=l(""),ae=l(!1),le=l(!1),te=l([]),oe=l([]),ne=l(0),re=l([]),ie=l(!1),ue=l(""),de=l(""),ce=l([]),se=l(!1),pe=t({currentPage:1,pageSize:10,total:0}),me=l(!1),ve=l("add"),ge=l(null),ye=l(!1),he=t({client_id:null,name:"",contact_person:"",email:"",phone:"",remark:"",reject_remark:"",nationality:"",employee_id:null,category:"海运",status:"未审核",clientStatus:"报价中",operationTime:null}),fe={name:[{required:!0,message:"请输入客户名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],employee_id:[{required:!1,message:"请选择负责员工",trigger:"change"}],category:[{required:!0,message:"请选择客户分类",trigger:"change"}],status:[{required:!0,message:"请选择审批状态",trigger:"change"}],clientStatus:[{required:!0,message:"请选择客户状态",trigger:"change"}]},we=async(e,a=!1)=>{a?(de.value=e,he.employee_id=null,ce.value=[]):(ue.value=e,J.value="",G.value=[])},be=async e=>{if(de.value)if(e){se.value=!0;try{const a=await N({pageNum:1,pageSize:10,name:e,departmentId:de.value});200===a.code?a.data&&a.data.list?ce.value=a.data.list:Array.isArray(a.data)?ce.value=a.data:ce.value=[]:v.error(a.msg||"搜索员工失败")}catch(a){v.error("搜索员工失败: "+(a.message||"未知错误"))}finally{se.value=!1}}else ce.value=[];else v.warning("请先选择部门")},_e=async()=>{W.value=!0,O.value=[];try{const l=await(a={pageNum:pe.currentPage,pageSize:pe.pageSize,name:Z.value||void 0,departmentId:ue.value||void 0,employeeName:F.value||void 0,category:Q.value||void 0,status:X.value||void 0,clientStatus:ee.value||void 0},e({url:"/client/page",method:"get",params:{page:a.pageNum,limit:a.pageSize,name:a.name,departmentId:a.departmentId,employeeName:a.employeeName,category:a.category,status:a.status,clientStatus:a.clientStatus}}));200===l.code?(l.data&&Array.isArray(l.data.list)?O.value=l.data.list:l.data&&Array.isArray(l.data)?O.value=l.data:O.value=[],l.data&&(pe.total=l.data.total||0,pe.currentPage=l.data.pageNum||l.data.page||pe.currentPage,pe.pageSize=l.data.pageSize||l.data.limit||pe.pageSize)):v.error(l.msg||"获取客户数据失败")}catch(l){v.error("加载客户数据失败: "+(l.message||"未知错误"))}finally{W.value=!1}var a},ke=()=>{ge.value&&ge.value.resetFields(),he.client_id=null,he.name="",he.contact_person="",he.email="",he.phone="",he.remark="",he.reject_remark="",he.nationality="",he.employee_id=null,he.category="海运",he.status="未审核",he.clientStatus="报价中",he.operationTime=null,de.value="",ce.value=[]},Ve=()=>{me.value=!1,ke()},xe=()=>{ve.value="add",ke(),me.value=!0},Te=async e=>{if(ve.value="edit",ke(),he.client_id=e.clientId,he.name=e.name,he.contact_person=e.contactPerson||"",he.email=e.email,he.phone=e.phone,he.remark=e.remark||"",he.reject_remark=e.rejectRemark||"",he.nationality=e.nationality||"",he.employee_id=e.employeeId,he.category=e.category||"海运",he.status=e.status||"未审核",he.clientStatus=e.clientStatus||"报价中",e.operationTime){const l=new Date(e.operationTime);if(isNaN(l.getTime()))he.operationTime=null;else try{let e=l.toLocaleString("sv-SE",{timeZone:"Asia/Shanghai",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1});e=e.replace("T"," "),he.operationTime=e}catch(a){he.operationTime=null}}else he.operationTime=null;if(e.employeeId){const a=await(async e=>{se.value=!0;try{const a=await P(e);if(200===a.code&&a.data)return ce.value=[a.data],a.data.departmentId}catch(a){}finally{se.value=!1}return null})(e.employeeId);a&&(de.value=a)}me.value=!0},Ie=a=>{j.confirm(`确定要删除客户 "${a.name}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{W.value=!0;try{const t=await(l=a.clientId,e({url:`/client/${l}`,method:"delete"}));200===t.code?(v({type:"success",message:"删除客户成功",duration:2e3}),_e()):(v({type:"error",message:t.msg||"删除失败",duration:3e3}),W.value=!1)}catch(t){v({type:"error",message:"删除失败: "+(t.message||"未知错误"),duration:3e3}),W.value=!1}var l})).catch((()=>{}))},Se=async a=>{a&&await a.validate((async(a,l)=>{if(!a){const e=[];for(const a in l)if(l.hasOwnProperty(a)){const t=l[a];t&&t.length>0&&e.push(t[0].message)}return v({type:"warning",message:"请完善表单信息："+e.join("；"),duration:5e3}),!1}ye.value=!0;try{const a={clientId:he.client_id,name:he.name,contactPerson:he.contact_person,email:he.email,phone:he.phone,remark:he.remark,rejectRemark:he.reject_remark,nationality:he.nationality,employeeId:he.employee_id||null,category:he.category,status:he.status,clientStatus:he.clientStatus,operationTime:""===he.operationTime?null:he.operationTime};let l;l="add"===ve.value?await(t=a,e({url:"/client",method:"post",data:t})):await function(a){return e({url:"/client",method:"put",data:a})}(a),200===l.code?(v({type:"success",message:"add"===ve.value?"添加客户成功":"更新客户成功",duration:2e3}),me.value=!1,ke(),_e()):v({type:"error",message:l.msg||("add"===ve.value?"添加失败":"更新失败"),duration:3e3})}catch(o){v({type:"error",message:"提交失败: "+(o.message||"未知错误"),duration:3e3})}finally{ye.value=!1}var t}))},Ce=()=>{pe.currentPage=1,_e()},Ue=()=>{Z.value="",F.value="",J.value="",ue.value="",Q.value="",X.value="",ee.value="",G.value=[],pe.currentPage=1,_e()},je=e=>{pe.currentPage=e,_e()},Ne=e=>{pe.pageSize=e,pe.currentPage=1,_e()},Pe=e=>{switch(e){case"海运":return"primary";case"空运":return"success";case"散货":return"warning";case"快递":return"info";default:return""}},ze=e=>{switch(e){case"未审核":return"info";case"审核中":return"warning";case"审核通过":return"success";case"已拒绝":return"danger";default:return""}},Be=e=>{switch(e){case"报价中":return"warning";case"已合作":return"success";default:return"info"}},qe=e=>"已合作"===e?"审核通过":e||"----",Re=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).replace(/\//g,"-")},$e=async()=>{try{const a=await e({url:"/client/pending-count",method:"get"});200===a.code&&(ne.value=a.data||0,ne.value>0&&g({title:"客户审批提醒",message:`您有 ${ne.value} 个客户待审批`,type:"info",duration:5e3,position:"top-right"}))}catch(a){}},Ae=async()=>{ae.value=!0,le.value=!0,oe.value=[];try{const l=await e({url:"/client/pending",method:"get",params:a});200===l.code?te.value=l.data||[]:(v.error(l.msg||"获取待审批客户列表失败"),te.value=[])}catch(l){v.error("获取待审批客户列表失败: "+(l.message||"未知错误")),te.value=[]}finally{le.value=!1}var a},Ye=()=>{$e()},De=e=>{oe.value=e},Ke=()=>{0!==oe.value.length?j.confirm(`确定要通过选中的 ${oe.value.length} 个客户申请吗？`,"批量审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"success"}).then((async()=>{le.value=!0;try{const e=oe.value.map((e=>e.clientId)),a=await q({clientIds:e,status:"审核通过"});200===a.code?(v.success(`已通过 ${e.length} 个客户申请`),te.value=te.value.filter((a=>!e.includes(a.clientId))),oe.value=[],_e()):v.error(a.msg||"批量审批操作失败")}catch(e){v.error("批量审批操作失败: "+(e.message||"未知错误"))}finally{le.value=!1}})).catch((()=>{})):v.warning("请先选择要审批的客户")},Ee=()=>{0!==oe.value.length?j.confirm(`确定要拒绝选中的 ${oe.value.length} 个客户申请吗？`,"批量审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{j.prompt("请输入批量拒绝理由（将应用于所有选定客户）：","批量拒绝客户",{confirmButtonText:"提交拒绝",cancelButtonText:"取消输入",inputPlaceholder:"批量拒绝理由（可选）",inputType:"textarea"}).then((async({value:e})=>{le.value=!0;try{const a=oe.value.map((e=>e.clientId)),l=await q({clientIds:a,status:"已拒绝",rejectRemark:e||""});200===l.code?(v.success(`已拒绝 ${a.length} 个客户申请`),te.value=te.value.filter((e=>!a.includes(e.clientId))),oe.value=[],_e(),$e()):v.error(l.msg||"批量审批操作失败")}catch(a){v.error("批量审批操作失败: "+(a.message||"未知错误"))}finally{le.value=!1}})).catch((()=>{v.info("已取消输入批量拒绝理由")}))})).catch((()=>{v.info("已取消批量拒绝操作")})):v.warning("请先选择要审批的客户")},He=l([{value:"国内-同行",label:"国内-同行"},{value:"国外-同行",label:"国外-同行"},{value:"国内-直客",label:"国内-直客"},{value:"国外-直客",label:"国外-直客"}]);return o((()=>{(async()=>{ie.value=!0;try{const e=await z();200===e.code?re.value=e.data||[]:v.error(e.msg||"获取部门列表失败")}catch(e){v.error("加载部门列表失败: "+(e.message||"未知错误"))}finally{ie.value=!1}})(),_e(),$e()})),(e,a)=>{const l=s("el-icon"),t=s("el-input"),o=s("el-option"),g=s("el-select"),N=s("el-button"),P=s("el-badge"),z=s("el-table-column"),q=s("el-tag"),J=s("el-tooltip"),G=s("el-table"),ke=s("el-pagination"),Le=s("el-form-item"),Me=s("el-date-picker"),Oe=s("el-form"),We=s("el-dialog"),Ze=p("loading");return y(),n("div",R,[r("div",$,[r("div",A,[u(t,{modelValue:Z.value,"onUpdate:modelValue":a[0]||(a[0]=e=>Z.value=e),placeholder:"搜索客户名称",clearable:"",onKeyup:c(Ce,["enter"]),style:{width:"200px"}},{prefix:d((()=>[u(l,null,{default:d((()=>[u(h(f))])),_:1})])),_:1},8,["modelValue"]),u(g,{modelValue:ue.value,"onUpdate:modelValue":a[1]||(a[1]=e=>ue.value=e),placeholder:"选择部门",clearable:"",filterable:"",style:{width:"180px"},onChange:a[2]||(a[2]=e=>we(e,!1)),loading:ie.value},{default:d((()=>[(y(!0),n(w,null,b(re.value,(e=>(y(),m(o,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"]),u(t,{modelValue:F.value,"onUpdate:modelValue":a[3]||(a[3]=e=>F.value=e),placeholder:"输入负责员工名称搜索",clearable:"",onKeyup:c(Ce,["enter"]),style:{width:"180px"}},null,8,["modelValue"]),u(g,{modelValue:Q.value,"onUpdate:modelValue":a[4]||(a[4]=e=>Q.value=e),placeholder:"选择客户分类",clearable:"",style:{width:"140px"}},{default:d((()=>[(y(),n(w,null,b(["海运","空运","散货","快递"],(e=>u(o,{key:e,label:e,value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),u(g,{modelValue:ee.value,"onUpdate:modelValue":a[5]||(a[5]=e=>ee.value=e),placeholder:"选择客户状态",clearable:"",style:{width:"140px"}},{default:d((()=>[(y(),n(w,null,b(["报价中","已合作"],(e=>u(o,{key:e,label:e,value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),u(g,{modelValue:X.value,"onUpdate:modelValue":a[6]||(a[6]=e=>X.value=e),placeholder:"选择审批状态",clearable:"",style:{width:"140px"}},{default:d((()=>[(y(),n(w,null,b(["未审核","审核中","审核通过","已拒绝"],(e=>u(o,{key:e,label:e,value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),u(N,{type:"primary",onClick:Ce},{default:d((()=>a[24]||(a[24]=[_("搜索")]))),_:1}),u(N,{onClick:Ue},{default:d((()=>[u(l,null,{default:d((()=>[u(h(k))])),_:1}),a[25]||(a[25]=_("重置 "))])),_:1})]),r("div",Y,[u(N,{type:"primary",onClick:xe,class:"add-btn"},{default:d((()=>[u(l,null,{default:d((()=>[u(h(V))])),_:1}),a[26]||(a[26]=_("添加客户 "))])),_:1}),u(N,{type:"success",onClick:Ae,class:"approval-btn"},{default:d((()=>[u(l,null,{default:d((()=>[u(h(T))])),_:1}),a[27]||(a[27]=_("客户审批 ")),ne.value>0?(y(),m(P,{key:0,value:ne.value,class:"approval-badge"},null,8,["value"])):x("",!0)])),_:1})])]),i((y(),m(G,{data:O.value,border:"","row-key":"clientId","max-height":"calc(100vh - 220px)",class:"custom-table"},{default:d((()=>[u(z,{type:"index",width:"60",align:"center",label:"序号",fixed:"","class-name":"index-column"}),u(z,{prop:"name",label:"客户名称","min-width":"120","show-overflow-tooltip":""}),u(z,{prop:"contactPerson",label:"联系人","min-width":"100","show-overflow-tooltip":""},{default:d((e=>[_(I(e.row.contactPerson||"----"),1)])),_:1}),u(z,{prop:"email",label:"邮箱","min-width":"150","show-overflow-tooltip":""}),u(z,{prop:"phone",label:"电话","min-width":"130","show-overflow-tooltip":""}),u(z,{prop:"nationality",label:"国籍/地区","min-width":"100","show-overflow-tooltip":""},{default:d((e=>[_(I(e.row.nationality||"----"),1)])),_:1}),u(z,{prop:"departmentName",label:"部门","min-width":"120","show-overflow-tooltip":""},{default:d((({row:e})=>[_(I(e.departmentName||"----"),1)])),_:1}),u(z,{prop:"employeeName",label:"负责员工","min-width":"100","show-overflow-tooltip":""},{default:d((({row:e})=>[_(I(e.employeeName||"----"),1)])),_:1}),u(z,{prop:"category",label:"客户分类","min-width":"100","show-overflow-tooltip":""},{default:d((({row:e})=>[u(q,{type:Pe(e.category),effect:"plain"},{default:d((()=>[_(I(e.category||"海运"),1)])),_:2},1032,["type"])])),_:1}),u(z,{prop:"clientStatus",label:"客户状态","min-width":"100","show-overflow-tooltip":""},{default:d((({row:e})=>[u(q,{type:Be(e.clientStatus),effect:"plain"},{default:d((()=>[_(I(e.clientStatus||"报价中"),1)])),_:2},1032,["type"])])),_:1}),u(z,{prop:"status",label:"审批状态","min-width":"100","show-overflow-tooltip":""},{default:d((({row:e})=>[u(J,{class:"item",effect:"dark",content:e.rejectRemark,placement:"top",disabled:!("已拒绝"===e.status&&e.rejectRemark&&""!==e.rejectRemark.trim())},{default:d((()=>[u(q,{type:ze(e.status),effect:"light"},{default:d((()=>[_(I(qe(e.status)),1)])),_:2},1032,["type"])])),_:2},1032,["content","disabled"])])),_:1}),u(z,{prop:"operationTime",label:"操单时间","min-width":"180","show-overflow-tooltip":""},{default:d((e=>[_(I(Re(e.row.operationTime)||"----"),1)])),_:1}),u(z,{prop:"remark",label:"备注","min-width":"150","show-overflow-tooltip":""},{default:d((e=>[_(I(e.row.remark||"----"),1)])),_:1}),u(z,{prop:"createTime",label:"创建时间","min-width":"180","show-overflow-tooltip":""},{default:d((e=>[_(I(Re(e.row.createTime)),1)])),_:1}),u(z,{label:"操作",width:"120",fixed:"right",align:"center"},{default:d((({row:e})=>[r("div",D,[u(N,{class:"edit-btn",onClick:a=>Te(e),title:"编辑"},{default:d((()=>[u(l,null,{default:d((()=>[u(h(S))])),_:1})])),_:2},1032,["onClick"]),u(N,{class:"delete-btn",onClick:a=>Ie(e),title:"删除"},{default:d((()=>[u(l,null,{default:d((()=>[u(h(C))])),_:1})])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[Ze,W.value]]),r("div",K,[u(ke,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":pe.currentPage,"page-size":pe.pageSize,total:pe.total,"page-sizes":[10,20,50,100],onSizeChange:Ne,onCurrentChange:je},null,8,["current-page","page-size","total"])]),u(We,{modelValue:me.value,"onUpdate:modelValue":a[22]||(a[22]=e=>me.value=e),title:"add"===ve.value?"添加客户":"编辑客户",width:"500px","destroy-on-close":"",class:"custom-dialog"},{default:d((()=>[u(Oe,{ref_key:"formRef",ref:ge,model:he,rules:fe,"label-position":"left","label-width":"100px",class:"dialog-form"},{default:d((()=>[u(Le,{label:"客户名称",prop:"name",required:""},{default:d((()=>[u(t,{modelValue:he.name,"onUpdate:modelValue":a[7]||(a[7]=e=>he.name=e),placeholder:"请输入客户名称"},null,8,["modelValue"])])),_:1}),u(Le,{label:"联系人",prop:"contact_person"},{default:d((()=>[u(t,{modelValue:he.contact_person,"onUpdate:modelValue":a[8]||(a[8]=e=>he.contact_person=e),placeholder:"请输入联系人姓名"},null,8,["modelValue"])])),_:1}),u(Le,{label:"邮箱",prop:"email",required:""},{default:d((()=>[u(t,{modelValue:he.email,"onUpdate:modelValue":a[9]||(a[9]=e=>he.email=e),placeholder:"请输入邮箱地址"},null,8,["modelValue"])])),_:1}),u(Le,{label:"电话",prop:"phone"},{default:d((()=>[u(t,{modelValue:he.phone,"onUpdate:modelValue":a[10]||(a[10]=e=>he.phone=e),placeholder:"请输入电话号码"},null,8,["modelValue"])])),_:1}),u(Le,{label:"国籍/地区",prop:"nationality"},{default:d((()=>[u(g,{modelValue:he.nationality,"onUpdate:modelValue":a[11]||(a[11]=e=>he.nationality=e),placeholder:"请选择国籍/地区",style:{width:"100%"},clearable:""},{default:d((()=>[(y(!0),n(w,null,b(He.value,(e=>(y(),m(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(Le,{label:"所属部门",required:""},{default:d((()=>[u(g,{modelValue:de.value,"onUpdate:modelValue":a[12]||(a[12]=e=>de.value=e),placeholder:"请选择部门",clearable:"",style:{width:"100%"},onChange:a[13]||(a[13]=e=>we(e,!0)),loading:ie.value},{default:d((()=>[(y(!0),n(w,null,b(re.value,(e=>(y(),m(o,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),u(Le,{label:"负责员工",prop:"employee_id",required:""},{default:d((()=>[u(g,{modelValue:he.employee_id,"onUpdate:modelValue":a[14]||(a[14]=e=>he.employee_id=e),clearable:"",filterable:"",remote:"",style:{width:"100%"},disabled:!de.value,placeholder:"输入员工名称进行搜索","remote-method":be,loading:se.value},{empty:d((()=>[r("p",E,I(de.value?"请输入员工名称搜索":"请先选择部门"),1)])),default:d((()=>[(y(!0),n(w,null,b(ce.value,(e=>(y(),m(o,{key:e.employeeId,label:e.name+(e.positionName?"（"+e.positionName+"）":""),value:e.employeeId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled","loading"])])),_:1}),u(Le,{label:"客户分类",prop:"category"},{default:d((()=>[u(g,{modelValue:he.category,"onUpdate:modelValue":a[15]||(a[15]=e=>he.category=e),placeholder:"请选择客户分类",clearable:"",style:{width:"100%"}},{default:d((()=>[(y(),n(w,null,b(["海运","空运","散货","快递"],(e=>u(o,{key:e,label:e,value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),u(Le,{label:"客户状态",prop:"clientStatus"},{default:d((()=>[u(g,{modelValue:he.clientStatus,"onUpdate:modelValue":a[16]||(a[16]=e=>he.clientStatus=e),placeholder:"请选择客户状态",clearable:"",style:{width:"100%"}},{default:d((()=>[(y(),n(w,null,b(["报价中","已合作"],(e=>u(o,{key:e,label:e,value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),u(Le,{label:"审批状态",prop:"status"},{default:d((()=>[u(g,{modelValue:he.status,"onUpdate:modelValue":a[17]||(a[17]=e=>he.status=e),placeholder:"请选择审批状态",clearable:"",style:{width:"100%"}},{default:d((()=>[(y(),n(w,null,b(["未审核","审核中","审核通过","已拒绝"],(e=>u(o,{key:e,label:e,value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),u(Le,{label:"操单时间",prop:"operationTime"},{default:d((()=>[u(Me,{modelValue:he.operationTime,"onUpdate:modelValue":a[18]||(a[18]=e=>he.operationTime=e),type:"datetime",placeholder:"选择操单时间",style:{width:"100%"},clearable:"","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])])),_:1}),u(Le,{label:"备注",prop:"remark"},{default:d((()=>[u(t,{modelValue:he.remark,"onUpdate:modelValue":a[19]||(a[19]=e=>he.remark=e),type:"textarea",placeholder:"请输入备注信息",rows:3},null,8,["modelValue"])])),_:1}),"已拒绝"===he.status?(y(),m(Le,{key:0,label:"拒绝备注",prop:"reject_remark"},{default:d((()=>[u(t,{modelValue:he.reject_remark,"onUpdate:modelValue":a[20]||(a[20]=e=>he.reject_remark=e),type:"textarea",placeholder:"请输入拒绝备注",rows:3},null,8,["modelValue"])])),_:1})):x("",!0)])),_:1},8,["model"]),r("div",H,[u(N,{onClick:Ve},{default:d((()=>a[28]||(a[28]=[_("取消")]))),_:1}),u(N,{type:"primary",onClick:a[21]||(a[21]=e=>Se(ge.value)),loading:ye.value},{default:d((()=>a[29]||(a[29]=[_("确定")]))),_:1},8,["loading"])])])),_:1},8,["modelValue","title"]),u(We,{modelValue:ae.value,"onUpdate:modelValue":a[23]||(a[23]=e=>ae.value=e),title:"客户审批",width:"90%",style:{maxWidth:"1200px"},"close-on-click-modal":!1,"append-to-body":"",onClosed:Ye},{default:d((()=>[r("div",L,[u(N,{type:"success",icon:h(T),onClick:Ke,disabled:0===oe.value.length},{default:d((()=>a[30]||(a[30]=[_(" 批量通过 ")]))),_:1},8,["icon","disabled"]),u(N,{type:"danger",icon:h(U),onClick:Ee,disabled:0===oe.value.length},{default:d((()=>a[31]||(a[31]=[_(" 批量拒绝 ")]))),_:1},8,["icon","disabled"])]),i((y(),m(G,{data:te.value,border:"",class:"custom-table approval-table","max-height":"calc(70vh - 180px)",style:{width:"100%","margin-bottom":"0"},onSelectionChange:De},{default:d((()=>[u(z,{type:"selection",width:"50",align:"center"}),u(z,{prop:"name",label:"客户名称","min-width":"110","show-overflow-tooltip":"",align:"center"}),u(z,{prop:"contactPerson",label:"联系人","min-width":"90","show-overflow-tooltip":"",align:"center"},{default:d((e=>[_(I(e.row.contactPerson||"----"),1)])),_:1}),u(z,{prop:"phone",label:"联系电话","min-width":"110","show-overflow-tooltip":"",align:"center"}),u(z,{prop:"email",label:"邮箱","min-width":"130","show-overflow-tooltip":"",align:"center"}),u(z,{prop:"nationality",label:"国籍/地区","min-width":"100","show-overflow-tooltip":"",align:"center"},{default:d((e=>[_(I(e.row.nationality||"----"),1)])),_:1}),u(z,{prop:"departmentName",label:"部门","min-width":"110","show-overflow-tooltip":"",align:"center"}),u(z,{prop:"employeeName",label:"负责员工","min-width":"90","show-overflow-tooltip":"",align:"center"}),u(z,{prop:"category",label:"客户类型","min-width":"90",align:"center"},{default:d((e=>[u(q,{type:Pe(e.row.category)},{default:d((()=>[_(I(e.row.category),1)])),_:2},1032,["type"])])),_:1}),u(z,{prop:"status",label:"状态","min-width":"90",align:"center"},{default:d((e=>[u(q,{type:ze(e.row.status)},{default:d((()=>[_(I(qe(e.row.status)),1)])),_:2},1032,["type"])])),_:1}),u(z,{prop:"remark",label:"备注","min-width":"130","show-overflow-tooltip":"",align:"center"},{default:d((e=>[_(I(e.row.remark||"----"),1)])),_:1}),u(z,{label:"操作",width:"120",fixed:"right",align:"center"},{default:d((e=>[r("div",M,[u(N,{type:"success",link:"",icon:h(T),onClick:a=>{return l=e.row,void j.confirm(`确定要通过客户 "${l.name}" 的申请吗？`,"审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"success"}).then((async()=>{le.value=!0;try{const e=await B({clientId:l.clientId,status:"审核通过"});200===e.code?(v.success("已通过客户申请"),te.value=te.value.filter((e=>e.clientId!==l.clientId)),_e()):v.error(e.msg||"审批操作失败")}catch(e){v.error("审批操作失败: "+(e.message||"未知错误"))}finally{le.value=!1}})).catch((()=>{}));var l}},{default:d((()=>a[32]||(a[32]=[_(" 通过 ")]))),_:2},1032,["icon","onClick"]),u(N,{type:"danger",link:"",icon:h(U),onClick:a=>{return l=e.row,void j.confirm(`确定要拒绝客户 "${l.name}" 的申请吗？`,"审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{j.prompt("请输入拒绝理由：","拒绝客户: "+l.name,{confirmButtonText:"提交拒绝",cancelButtonText:"取消输入",inputPlaceholder:"拒绝理由（可选）",inputType:"textarea"}).then((async({value:e})=>{le.value=!0;try{const a=await B({clientId:l.clientId,status:"已拒绝",rejectRemark:e||""});200===a.code?(v.success("已拒绝客户申请"),te.value=te.value.filter((e=>e.clientId!==l.clientId)),_e(),$e()):v.error(a.msg||"审批操作失败")}catch(a){v.error("审批操作失败: "+(a.message||"未知错误"))}finally{le.value=!1}})).catch((()=>{v.info("已取消输入拒绝理由")}))})).catch((()=>{v.info("已取消拒绝操作")}));var l}},{default:d((()=>a[33]||(a[33]=[_(" 拒绝 ")]))),_:2},1032,["icon","onClick"])])])),_:1})])),_:1},8,["data"])),[[Ze,le.value]])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-264ca71c"]]);export{O as default};
