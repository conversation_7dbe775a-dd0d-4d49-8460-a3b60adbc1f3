import{_ as e,a,r as t,o as l,c as d,e as r,R as n,f as i,k as o,S as s,j as u,T as p,K as m,E as c,m as v,h as g,U as _,t as f,n as h,V as y,W as w,X as b,Y as V,Z as x,$ as I,g as z,O as C}from"./index-BDR0Pmj6.js";import{g as S,a as k,b as N,d as P,c as D,u as U,e as L}from"./department-jpUZgat6.js";import{s as A}from"./employee-DbFXp2d5.js";const T={class:"department-container"},j={class:"toolbar"},q={class:"search-box"},E={class:"employee-suggestion-item"},$={class:"employee-name"},B={class:"employee-email"},K={class:"action-box"},R={class:"operation-buttons"},F={class:"pagination-container"},M={class:"employee-suggestion-item"},O={class:"employee-name"},W={class:"employee-email"},X={class:"dialog-footer"},Y={class:"department-detail-container"},Z={class:"card-header"},G={class:"department-info"},H={class:"info-item"},J={class:"info-value"},Q={class:"info-item"},ee={class:"info-value"},ae={class:"info-item"},te={class:"info-value"},le={class:"info-item"},de={class:"info-value"},re={class:"info-item"},ne={class:"info-value"},ie={class:"info-item"},oe={class:"info-value"},se={class:"card-header"},ue={class:"employee-search-box"},pe={class:"employee-list"},me={class:"employee-pagination-container"},ce={key:0,class:"no-data"},ve=e({__name:"Department",setup(e){const ve=a([]),ge=a([]),_e=a(!0),fe=a(""),he=a(""),ye=a(null),we=a(""),be=a(null),Ve=t({currentPage:1,pageSize:10,total:0}),xe=a(!1),Ie=a("add"),ze=a(null),Ce=a(!1),Se=t({department_id:null,department_name:"",department_leader:"",department_description:"",status:"Active",leader_id:null,parent_department_id:null,parent_department_name:""}),ke=(e,a,t)=>{const l=async e=>{try{const t=await S(e);if(200===t.code&&t.data)for(const e of t.data){if(e.departmentId===a)return!0;if(await l(e.departmentId))return!0}return!1}catch(t){return!1}};l(e).then((e=>{e?t(new Error("不能选择子部门作为上级部门")):t()}))},Ne={department_name:[{required:!0,message:"请输入部门名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],department_leader:[{max:50,message:"长度不能超过 50 个字符",trigger:"blur"}],department_description:[{max:255,message:"长度不能超过 255 个字符",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],parent_department_name:[{validator:(e,a,t)=>{a&&Se.parent_department_id?Se.department_id&&Se.parent_department_id===Se.department_id?t(new Error("不能选择自己作为上级部门")):Se.department_id?ke(Se.department_id,Se.parent_department_id,t):t():t()},trigger:"change"}]},Pe=a(!1),De=a(null),Ue=a([]),Le=a(!1),Ae=a(""),Te=t({currentPage:1,pageSize:10,total:0}),je=a(!1);a([]);const qe=async()=>{_e.value=!0,ve.value=[];try{if(be.value){const e=await S(be.value);if(200===e.code){const a=e.data||[];Ve.total=a.length;const t=(Ve.currentPage-1)*Ve.pageSize,l=t+Ve.pageSize;ve.value=a.slice(t,l),Ve.currentPage>Math.ceil(Ve.total/Ve.pageSize)&&(Ve.currentPage=1)}else c.error(e.msg||"获取子部门数据失败")}else{const e=await k({pageNum:Ve.currentPage,pageSize:Ve.pageSize,departmentName:fe.value||void 0,leaderId:ye.value||void 0,leaderName:he.value||void 0});200===e.code?(ve.value=e.data.list||[],Ve.total=e.data.total||0):c.error(e.msg||"获取部门数据失败")}}catch(e){c.error("加载部门数据失败: "+(e.message||"未知错误"))}finally{_e.value=!1}},Ee=async()=>{try{const e=await N();200===e.code&&(ge.value=e.data||[])}catch(e){}},$e=()=>{ze.value&&ze.value.resetFields(),Se.department_id=null,Se.department_name="",Se.department_leader="",Se.department_description="",Se.status="Active",Se.leader_id=null,Se.parent_department_id=null,Se.parent_department_name=""},Be=()=>{xe.value=!1,$e()},Ke=()=>{Ie.value="add",$e(),xe.value=!0},Re=()=>{Ve.currentPage=1,qe()},Fe=()=>{he.value="",ye.value=null,Re()},Me=()=>{fe.value="",he.value="",ye.value=null,we.value="",be.value=null,Ve.currentPage=1,Ve.pageSize=10,qe(),Ee()},Oe=e=>{Ve.currentPage=e,qe()},We=e=>{Ve.pageSize=e,Ve.currentPage=1,qe()},Xe=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}).replace(/\//g,"-")},Ye=async()=>{if(De.value){Le.value=!0;try{const e={pageNum:Te.currentPage,pageSize:Te.pageSize,name:Ae.value||void 0},a=await L(De.value.departmentId,e);200===a.code?(Ue.value=a.data.list||[],Te.total=a.data.total||0):(c.error(a.msg||"获取部门员工数据失败"),Ue.value=[],Te.total=0)}catch(e){c.error("获取部门员工数据失败: "+(e.message||"未知错误")),Ue.value=[],Te.total=0}finally{Le.value=!1}}},Ze=()=>{Te.currentPage=1,Ye()},Ge=()=>{Ae.value="",Te.currentPage=1,Ye()},He=e=>{Te.currentPage=e,Ye()},Je=e=>{Te.pageSize=e,Te.currentPage=1,Ye()},Qe=async(e,a)=>{if(""!==e.trim()){je.value=!0;try{const t=await A(e);if(200===t.code){const e=t.data.list||[];a(e.map((e=>({value:e.employeeId,label:e.name,email:e.email}))))}else a([])}catch(t){a([])}finally{je.value=!1}}else a([])},ea=e=>{e&&e.value&&(Se.leader_id=e.value,Se.department_leader=e.label)},aa=(e,a)=>{a(ge.value.filter((a=>a.departmentName.toLowerCase().includes(e.toLowerCase()))).map((e=>({value:e.departmentName,departmentId:e.departmentId,label:e.departmentName}))))},ta=e=>{e&&(be.value=e.departmentId,we.value=e.label,Ve.currentPage=1,da())},la=()=>{be.value=null,we.value="",Ve.currentPage=1,Ve.pageSize=10,qe()},da=()=>{be.value?qe():c.warning("请先选择要查询子部门的部门")},ra=e=>{if(!e)return"无";const a=ge.value.find((a=>a.departmentId===e));return a?a.departmentName:"未知部门"},na=(e,a)=>{a(ge.value.filter((a=>a.departmentName.toLowerCase().includes(e.toLowerCase())&&(!Se.department_id||a.departmentId!==Se.department_id))).map((e=>({value:e.departmentName,departmentId:e.departmentId,label:e.departmentName}))))},ia=e=>{Se.parent_department_name=e.value,Se.parent_department_id=e.departmentId};return l((()=>{qe(),Ee()})),(e,a)=>{var t;const l=u("el-icon"),S=u("el-input"),k=u("el-autocomplete"),N=u("el-button"),L=u("el-table-column"),A=u("el-tag"),ge=u("el-table"),be=u("el-pagination"),ke=u("el-form-item"),da=u("el-option"),oa=u("el-select"),sa=u("el-form"),ua=u("el-dialog"),pa=u("el-card"),ma=u("el-drawer"),ca=p("loading");return v(),d("div",T,[r("div",j,[r("div",q,[i(S,{modelValue:fe.value,"onUpdate:modelValue":a[0]||(a[0]=e=>fe.value=e),placeholder:"搜索部门名称",clearable:"",onKeyup:s(Re,["enter"])},{prefix:o((()=>[i(l,null,{default:o((()=>[i(g(_))])),_:1})])),_:1},8,["modelValue"]),i(k,{modelValue:he.value,"onUpdate:modelValue":a[1]||(a[1]=e=>he.value=e),placeholder:"部门负责人",style:{width:"220px"},"fetch-suggestions":Qe,onSelect:a[2]||(a[2]=e=>{e&&(ye.value=e.value,he.value=e.label),Re()}),"trigger-on-focus":!1,debounce:300,clearable:"",onClear:Fe},{prefix:o((()=>[i(l,{class:"el-input__icon"},{default:o((()=>[i(g(_))])),_:1})])),default:o((({item:e})=>[r("div",E,[r("div",$,f(e.label),1),r("div",B,f(e.email),1)])])),_:1},8,["modelValue"]),i(k,{modelValue:we.value,"onUpdate:modelValue":a[3]||(a[3]=e=>we.value=e),"fetch-suggestions":aa,placeholder:"搜索部门的子部门",style:{width:"220px"},clearable:"",onSelect:ta,onClear:la},{prefix:o((()=>[i(l,null,{default:o((()=>[i(g(_))])),_:1})])),default:o((({item:e})=>[r("div",null,f(e.label),1)])),_:1},8,["modelValue"]),i(N,{type:"primary",onClick:Re},{default:o((()=>a[14]||(a[14]=[h("搜索")]))),_:1}),i(N,{onClick:Me},{default:o((()=>[i(l,null,{default:o((()=>[i(g(y))])),_:1}),a[15]||(a[15]=h("重置 "))])),_:1})]),r("div",K,[i(N,{type:"primary",onClick:Ke,class:"add-btn"},{default:o((()=>[i(l,null,{default:o((()=>[i(g(w))])),_:1}),a[16]||(a[16]=h("添加部门 "))])),_:1})])]),n((v(),m(ge,{data:ve.value,border:"","row-key":"departmentId","max-height":"calc(100vh - 220px)",class:"custom-table"},{default:o((()=>[i(L,{type:"index",width:"70",align:"center",label:"序号",fixed:"","class-name":"index-column"}),i(L,{prop:"departmentName",label:"部门名称","min-width":"150","show-overflow-tooltip":""}),i(L,{label:"部门负责人","min-width":"150","show-overflow-tooltip":""},{default:o((({row:e})=>[h(f(e.departmentLeader||"无"),1)])),_:1}),i(L,{prop:"departmentDescription",label:"部门描述","min-width":"200","show-overflow-tooltip":""}),i(L,{label:"上级部门","min-width":"150","show-overflow-tooltip":""},{default:o((({row:e})=>[h(f(ra(e.parentDepartmentId)||"无"),1)])),_:1}),i(L,{prop:"status",label:"状态",width:"100",align:"center","show-overflow-tooltip":""},{default:o((({row:e})=>[i(A,{type:"Active"===e.status?"success":"danger",class:"status-tag"},{default:o((()=>[h(f("Active"===e.status?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),i(L,{label:"创建时间","min-width":"160","show-overflow-tooltip":""},{default:o((({row:e})=>[h(f(Xe(e.createTime)),1)])),_:1}),i(L,{label:"更新时间","min-width":"160","show-overflow-tooltip":""},{default:o((({row:e})=>[h(f(Xe(e.updateTime)),1)])),_:1}),i(L,{label:"操作",width:"180",align:"center",fixed:"right","class-name":"operation-column"},{default:o((({row:e})=>[r("div",R,[i(N,{class:"view-btn",onClick:a=>(async e=>{De.value=e,Pe.value=!0,await Ye()})(e),title:"查看详情"},{default:o((()=>[i(l,null,{default:o((()=>[i(g(b))])),_:1})])),_:2},1032,["onClick"]),i(N,{class:"edit-btn",onClick:a=>(e=>{Ie.value="edit",$e(),Se.department_id=e.departmentId,Se.department_name=e.departmentName,Se.leader_id=e.leaderId,!e.leaderId||e.departmentLeader&&""!==e.departmentLeader.trim()?Se.department_leader=e.departmentLeader||"":Se.department_leader="",Se.department_description=e.departmentDescription||"",Se.status=e.status,Se.parent_department_id=e.parentDepartmentId,e.parentDepartmentId?Se.parent_department_name=ra(e.parentDepartmentId):Se.parent_department_name="",xe.value=!0})(e),title:"编辑"},{default:o((()=>[i(l,null,{default:o((()=>[i(g(V))])),_:1})])),_:2},1032,["onClick"]),i(N,{class:"delete-btn",onClick:a=>(e=>{C.confirm(`确定要删除部门 "${e.departmentName}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{_e.value=!0;try{const a=await P(e.departmentId);200===a.code?(c({type:"success",message:"删除部门成功",duration:2e3}),qe(),Ee()):(c({type:"error",message:a.msg||"删除失败",duration:3e3}),_e.value=!1)}catch(a){c({type:"error",message:"删除失败: "+(a.message||"未知错误"),duration:3e3}),_e.value=!1}})).catch((()=>{}))})(e),title:"删除"},{default:o((()=>[i(l,null,{default:o((()=>[i(g(x))])),_:1})])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[ca,_e.value]]),r("div",F,[i(be,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":Ve.currentPage,"page-size":Ve.pageSize,total:Ve.total,"page-sizes":[10,20,50,100],onSizeChange:We,onCurrentChange:Oe},null,8,["current-page","page-size","total"])]),i(ua,{modelValue:xe.value,"onUpdate:modelValue":a[11]||(a[11]=e=>xe.value=e),title:"add"===Ie.value?"添加部门":"编辑部门",width:"500px","destroy-on-close":"",class:"custom-dialog"},{default:o((()=>[i(sa,{ref_key:"formRef",ref:ze,model:Se,rules:Ne,"label-position":"left","label-width":"100px",class:"dialog-form"},{default:o((()=>[i(ke,{label:"部门名称",prop:"department_name",required:""},{default:o((()=>[i(S,{modelValue:Se.department_name,"onUpdate:modelValue":a[4]||(a[4]=e=>Se.department_name=e),placeholder:"请输入部门名称"},null,8,["modelValue"])])),_:1}),i(ke,{label:"部门负责人",prop:"department_leader"},{default:o((()=>[i(k,{modelValue:Se.department_leader,"onUpdate:modelValue":a[5]||(a[5]=e=>Se.department_leader=e),placeholder:"请输入部门负责人姓名",style:{width:"100%"},"fetch-suggestions":Qe,onSelect:ea,"trigger-on-focus":!1,debounce:300,loading:je.value,"popper-class":"leader-suggestions",clearable:"",onClear:a[6]||(a[6]=()=>Se.leader_id=null)},{default:o((({item:e})=>[r("div",M,[r("div",O,f(e.label),1),r("div",W,f(e.email),1)])])),suffix:o((()=>[je.value?(v(),m(l,{key:0,class:"loading-icon"},{default:o((()=>[i(g(I))])),_:1})):(v(),m(l,{key:1},{default:o((()=>[i(g(_))])),_:1}))])),_:1},8,["modelValue","loading"])])),_:1}),i(ke,{label:"上级部门",prop:"parent_department_name"},{default:o((()=>[i(k,{modelValue:Se.parent_department_name,"onUpdate:modelValue":a[7]||(a[7]=e=>Se.parent_department_name=e),placeholder:"请选择上级部门",style:{width:"100%"},"fetch-suggestions":na,onSelect:ia,"trigger-on-focus":!1,debounce:300,clearable:""},{default:o((({item:e})=>[r("div",null,f(e.label),1)])),suffix:o((()=>[i(l,null,{default:o((()=>[i(g(_))])),_:1})])),_:1},8,["modelValue"])])),_:1}),i(ke,{label:"部门描述",prop:"department_description"},{default:o((()=>[i(S,{modelValue:Se.department_description,"onUpdate:modelValue":a[8]||(a[8]=e=>Se.department_description=e),type:"textarea",rows:3,placeholder:"请输入部门描述"},null,8,["modelValue"])])),_:1}),i(ke,{label:"状态",prop:"status",required:""},{default:o((()=>[i(oa,{modelValue:Se.status,"onUpdate:modelValue":a[9]||(a[9]=e=>Se.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:o((()=>[i(da,{label:"启用",value:"Active"}),i(da,{label:"禁用",value:"Inactive"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"]),r("div",X,[i(N,{onClick:Be},{default:o((()=>a[17]||(a[17]=[h("取消")]))),_:1}),i(N,{type:"primary",loading:Ce.value,onClick:a[10]||(a[10]=e=>(async e=>{e&&await e.validate((async e=>{if(!e)return c({type:"warning",message:"请完善表单信息",duration:2e3}),!1;Ce.value=!0;try{const e={departmentId:Se.department_id,departmentName:Se.department_name,leaderId:Se.leader_id,departmentDescription:Se.department_description,status:Se.status,parentDepartmentId:Se.parent_department_id};let a;a="add"===Ie.value?await D(e):await U(e),200===a.code?(c({type:"success",message:"add"===Ie.value?"添加部门成功":"更新部门成功",duration:2e3}),xe.value=!1,$e(),qe(),Ee()):c({type:"error",message:a.msg||("add"===Ie.value?"添加失败":"更新失败"),duration:3e3})}catch(a){c({type:"error",message:"提交失败: "+(a.message||"未知错误"),duration:3e3})}finally{Ce.value=!1}}))})(ze.value))},{default:o((()=>a[18]||(a[18]=[h("确定")]))),_:1},8,["loading"])])])),_:1},8,["modelValue","title"]),i(ma,{modelValue:Pe.value,"onUpdate:modelValue":a[13]||(a[13]=e=>Pe.value=e),title:`${(null==(t=De.value)?void 0:t.departmentName)||""} - 部门详情`,direction:"rtl",size:"80%","destroy-on-close":"","modal-class":"department-detail-drawer"},{default:o((()=>[r("div",Y,[i(pa,{class:"detail-card"},{header:o((()=>{var e;return[r("div",Z,[a[19]||(a[19]=r("h3",null,"部门基本信息",-1)),i(A,{type:"Active"===(null==(e=De.value)?void 0:e.status)?"success":"danger",class:"status-tag"},{default:o((()=>{var e;return[h(f("Active"===(null==(e=De.value)?void 0:e.status)?"启用":"禁用"),1)]})),_:1},8,["type"])])]})),default:o((()=>{var e,t,l,d,n,i;return[r("div",G,[r("div",H,[a[20]||(a[20]=r("div",{class:"info-label"},"部门名称：",-1)),r("div",J,f(null==(e=De.value)?void 0:e.departmentName),1)]),r("div",Q,[a[21]||(a[21]=r("div",{class:"info-label"},"部门负责人：",-1)),r("div",ee,f((null==(t=De.value)?void 0:t.departmentLeader)||"暂无"),1)]),r("div",ae,[a[22]||(a[22]=r("div",{class:"info-label"},"部门描述：",-1)),r("div",te,f((null==(l=De.value)?void 0:l.departmentDescription)||"暂无描述"),1)]),r("div",le,[a[23]||(a[23]=r("div",{class:"info-label"},"上级部门：",-1)),r("div",de,f(ra(null==(d=De.value)?void 0:d.parentDepartmentId)),1)]),r("div",re,[a[24]||(a[24]=r("div",{class:"info-label"},"创建时间：",-1)),r("div",ne,f(Xe(null==(n=De.value)?void 0:n.createTime)),1)]),r("div",ie,[a[25]||(a[25]=r("div",{class:"info-label"},"更新时间：",-1)),r("div",oe,f(Xe(null==(i=De.value)?void 0:i.updateTime)),1)])])]})),_:1}),i(pa,{class:"employee-card"},{header:o((()=>[r("div",se,[a[28]||(a[28]=r("h3",null,"部门员工列表",-1)),r("div",ue,[i(S,{modelValue:Ae.value,"onUpdate:modelValue":a[12]||(a[12]=e=>Ae.value=e),placeholder:"搜索员工姓名",clearable:"",onInput:Ze},{prefix:o((()=>[i(l,null,{default:o((()=>[i(g(_))])),_:1})])),_:1},8,["modelValue"]),i(N,{type:"primary",onClick:Ze},{default:o((()=>a[26]||(a[26]=[h("搜索")]))),_:1}),i(N,{onClick:Ge},{default:o((()=>[i(l,null,{default:o((()=>[i(g(y))])),_:1}),a[27]||(a[27]=h("重置 "))])),_:1})])])])),default:o((()=>[n((v(),d("div",pe,[i(ge,{data:Ue.value,stripe:"",border:"",style:{width:"100%"},"max-height":"calc(100vh - 500px)"},{default:o((()=>[i(L,{type:"index",label:"序号",width:"60",align:"center"}),i(L,{prop:"name",label:"姓名","min-width":"100"}),i(L,{prop:"email",label:"邮箱","min-width":"150"}),i(L,{prop:"positionName",label:"职位","min-width":"120"}),i(L,{prop:"entryDate",label:"入职日期","min-width":"120"},{default:o((({row:e})=>[h(f(Xe(e.entryDate)),1)])),_:1}),i(L,{prop:"status",label:"状态",width:"80",align:"center"},{default:o((({row:e})=>[i(A,{type:"Active"===e.status?"success":"danger"},{default:o((()=>[h(f("Active"===e.status?"在职":"离职"),1)])),_:2},1032,["type"])])),_:1})])),_:1},8,["data"]),r("div",me,[i(be,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":Te.currentPage,"page-size":Te.pageSize,total:Te.total,"page-sizes":[10,20,50],onSizeChange:Je,onCurrentChange:He},null,8,["current-page","page-size","total"])]),Le.value||0!==Ue.value.length?z("",!0):(v(),d("div",ce," 暂无员工数据 "))])),[[ca,Le.value]])])),_:1})])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-d1eadd33"]]);export{ve as default};
