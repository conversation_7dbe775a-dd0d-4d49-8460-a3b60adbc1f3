import{_ as s,u as e,r as a,a as o,o as r,b as t,c as d,d as l,e as i,w as n,f as c,g as p,h as u,i as v,j as g,t as m,l as h,k as w,m as f,n as b,E as y,p as V,q as x,s as H}from"./index-BDR0Pmj6.js";const _={class:"login-container"},k={class:"login-box"},z={class:"input-group"},E={key:0,class:"error-message"},L={class:"input-group"},B={key:0,class:"error-message"},C=s({__name:"Login",setup(s){const C=t(),I=e(),M=a({phone:"",password:""}),U=o(!1),j=o(!1),q=a({phone:!1,password:!1,form:!1}),A=s=>{const e=/[A-Z]/.test(s),a=/[a-z]/.test(s),o=/\d/.test(s);return s.length>=8&&e&&a&&o},T=()=>(q.phone=!0,M.phone?"":"请输入手机号"),Z=()=>(q.password=!0,M.password?A(M.password)?"":"密码必须至少8位，包含大小写字母和数字":"请输入密码"),S=()=>q.phone?T():"",$=()=>q.password?Z():"",D=async()=>{var s;if((()=>{q.form=!0,q.phone=!0,q.password=!0,M.phone;const s=A(M.password);return j.value=s,j.value})()){U.value=!0,I.setLoading(!0),I.setError(null);try{const a=await V(M);if(200===a.code){const o=a.data;I.setToken(o);try{const e=await x();200===e.code?(I.setUser(e.data),y({message:"登录成功，欢迎回来！",type:"success",duration:3e3,showClose:!0}),H({title:"登录成功",message:`欢迎回到公司管理系统，${(null==(s=e.data)?void 0:s.name)||"管理员"}！`,type:"success",duration:4e3,position:"top-right"}),setTimeout((()=>{C.push({path:"/",query:{fromLogin:"true"}})}),1e3)):y.error(e.message||"获取用户信息失败")}catch(e){y.error("获取用户信息失败，请刷新页面重试")}}else I.setError(a.message),y.error(a.message||"登录失败")}catch(a){I.setError(a.message),y.error(a.message||"登录失败，请检查网络连接或账号密码")}finally{U.value=!1,I.setLoading(!1)}}else y.error("请正确填写所有必填项")};return r((()=>{I.isAuthenticated&&C.push("/")})),(s,e)=>{const a=g("el-input"),o=g("el-button");return f(),d("div",_,[e[9]||(e[9]=l('<div class="bg-animation" data-v-c1d01d87><div class="cube" data-v-c1d01d87></div><div class="cube" data-v-c1d01d87></div><div class="cube" data-v-c1d01d87></div><div class="cube" data-v-c1d01d87></div><div class="cube" data-v-c1d01d87></div></div>',1)),i("div",k,[e[6]||(e[6]=i("div",{class:"logo"},[i("svg",{viewBox:"0 0 24 24",width:"48",height:"48",fill:"currentColor"},[i("path",{d:"M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z"})])],-1)),e[7]||(e[7]=i("h1",{class:"title"},"中航物流管理后台",-1)),i("form",{onSubmit:n(D,["prevent"]),class:"login-form"},[i("div",z,[c(a,{modelValue:M.phone,"onUpdate:modelValue":e[0]||(e[0]=s=>M.phone=s),placeholder:"手机号","prefix-icon":u(v),size:"large",onInput:T,onBlur:e[1]||(e[1]=s=>q.phone=!0),clearable:""},null,8,["modelValue","prefix-icon"]),S()?(f(),d("div",E,m(S()),1)):p("",!0)]),i("div",L,[c(a,{modelValue:M.password,"onUpdate:modelValue":e[2]||(e[2]=s=>M.password=s),type:"password",placeholder:"密码","prefix-icon":u(h),"show-password":"",size:"large",onInput:Z,onBlur:e[3]||(e[3]=s=>q.password=!0),clearable:""},null,8,["modelValue","prefix-icon"]),$()?(f(),d("div",B,m($()),1)):p("",!0)]),c(o,{type:"primary","native-type":"submit",class:"login-button",size:"large",loading:U.value},{default:w((()=>e[4]||(e[4]=[b("登录")]))),_:1},8,["loading"]),e[5]||(e[5]=i("div",{class:"forgot-password"},[i("a",null,"忘记密码?")],-1))],32),e[8]||(e[8]=i("div",{class:"footer-text"}," Copyright © 2025 中航物流管理系统 ",-1))])])}}},[["__scopeId","data-v-c1d01d87"]]);export{C as default};
