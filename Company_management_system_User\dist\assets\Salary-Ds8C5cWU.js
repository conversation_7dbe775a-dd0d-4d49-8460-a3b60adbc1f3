import{g as a,a as l}from"./salary-VxckMG12.js";import{_ as e,a as t,r as s,o as i,c as d,d as n,Z as r,f as o,e as u,g as c,a7 as p,i as v,j as m,Y as g,z as b,E as f,m as y,n as h,a1 as w,t as _}from"./index-CGqeVPF3.js";import"./request-Cm8Ap7dD.js";const S={class:"salary-container"},k={class:"toolbar"},x={class:"filter-actions"},z={class:"total-salary"},B={key:0,class:"empty-block"},Y={class:"pagination-container"},A={key:0,class:"detail-container"},C={class:"detail-header"},D={class:"date-label"},F={class:"total-amount"},j={class:"detail-content"},V={class:"detail-item"},E={class:"detail-value"},M={class:"detail-item"},O={class:"detail-value bonus"},P={class:"detail-item"},$={class:"detail-value full-attendance-bonus"},U={class:"detail-item"},q={class:"detail-value business-operation-bonus"},I={class:"detail-item"},Z={class:"detail-value sum-salary"},G={class:"detail-item"},H={class:"detail-value leave-deduction"},J={class:"detail-item"},K={class:"detail-value deduction"},L={class:"detail-item"},N={class:"detail-value late-deduction"},Q={class:"detail-item"},R={class:"detail-value social-security-personal"},T={class:"detail-item"},W={class:"detail-value provident-fund"},X={class:"detail-item"},aa={class:"detail-value tax"},la={class:"detail-item"},ea={class:"detail-value water-electricity-fee"},ta={class:"detail-item"},sa={class:"detail-value actual-salary"},ia={class:"detail-item"},da={class:"detail-value reimbursement"},na={class:"detail-item"},ra={class:"detail-value private-account"},oa={class:"detail-item"},ua={class:"detail-value total-salary"},ca={class:"detail-item remark-item"},pa={class:"detail-value remark-value"},va={class:"summary-section"},ma={class:"summary-item"},ga={class:"summary-value"},ba={class:"dialog-footer"},fa=e({__name:"Salary",setup(e){const fa=t([]),ya=t(!1),ha=t(""),wa=t(null),_a=t(!1),Sa=s({page:1,size:10,total:0}),ka=a=>{if(!a)return"";if(/^\d{4}-\d{2}$/.test(a)){const[l,e]=a.split("-");return`${l}年${e}月`}return a},xa=async()=>{try{ya.value=!0;let l={page:Sa.page,size:Sa.size};ha.value&&(l.date=ha.value.substring(0,7));const e=await a(l);200===e.code?(fa.value=e.data||[],e.data&&"object"==typeof e.data&&(Array.isArray(e.data.records)?(fa.value=e.data.records,Sa.total=e.data.total||0):(fa.value=e.data,Sa.total=e.data.length||0))):f.error(e.message||"获取工资数据失败")}catch(l){f.error("获取工资列表失败，请稍后再试")}finally{ya.value=!1}},za=async()=>{Sa.page=1,await xa()},Ba=async()=>{ha.value="",Sa.page=1,await xa()},Ya=a=>{Sa.page=a,xa()},Aa=a=>{Sa.size=a,Sa.page=1,xa()},Ca=a=>null==a?"¥0.00":"¥"+parseFloat(a).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,",");return i((()=>{xa()})),(a,e)=>{const t=v("el-date-picker"),s=v("el-icon"),i=v("el-button"),xa=v("el-tag"),Da=v("el-table-column"),Fa=v("el-table"),ja=v("el-empty"),Va=v("el-pagination"),Ea=v("el-divider"),Ma=v("el-dialog"),Oa=g("loading");return y(),d("div",S,[n("div",k,[n("div",x,[u(t,{modelValue:ha.value,"onUpdate:modelValue":e[0]||(e[0]=a=>ha.value=a),type:"month",placeholder:"选择月份",format:"YYYY-MM","value-format":"YYYY-MM-DD","prefix-icon":c(p),onChange:za,clearable:""},null,8,["modelValue","prefix-icon"]),u(i,{onClick:Ba,disabled:!ha.value},{default:m((()=>[u(s,null,{default:m((()=>[u(c(w))])),_:1}),e[3]||(e[3]=h(" 重置 "))])),_:1},8,["disabled"])])]),r((y(),b(Fa,{data:fa.value,border:"",stripe:"",style:{width:"100%"},"row-key":"id","max-height":"calc(100vh - 280px)",class:"custom-table"},{default:m((()=>[u(Da,{label:"年月",prop:"date",width:"120",align:"center"},{default:m((({row:a})=>[u(xa,{type:"info"},{default:m((()=>[h(_(ka(a.date)),1)])),_:2},1024)])),_:1}),u(Da,{label:"基本工资",prop:"basicSalary","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.basicSalary)),1)])),_:1}),u(Da,{label:"奖金",prop:"performanceBonus","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.performanceBonus)),1)])),_:1}),u(Da,{label:"全勤奖",prop:"fullAttendanceBonus","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.fullAttendanceBonus)),1)])),_:1}),u(Da,{label:"业务与操作奖金",prop:"businessOperationBonus","min-width":"130",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.businessOperationBonus)),1)])),_:1}),u(Da,{label:"实得金额",prop:"sumSalary","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.sumSalary)),1)])),_:1}),u(Da,{label:"请假",prop:"leaveDeduction","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.leaveDeduction)),1)])),_:1}),u(Da,{label:"扣款",prop:"deduction","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.deduction)),1)])),_:1}),u(Da,{label:"迟到与缺卡",prop:"lateDeduction","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.lateDeduction)),1)])),_:1}),u(Da,{label:"社会保险费个人部分",prop:"socialSecurityPersonal","min-width":"150",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.socialSecurityPersonal)),1)])),_:1}),u(Da,{label:"公积金",prop:"providentFund","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.providentFund)),1)])),_:1}),u(Da,{label:"代扣代缴个税",prop:"tax","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.tax)),1)])),_:1}),u(Da,{label:"水电费",prop:"waterElectricityFee","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.waterElectricityFee)),1)])),_:1}),u(Da,{label:"实发工资",prop:"actualSalary","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.actualSalary)),1)])),_:1}),u(Da,{label:"报销",prop:"reimbursement","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.reimbursement)),1)])),_:1}),u(Da,{label:"私帐",prop:"privateAccount","min-width":"120",align:"right"},{default:m((({row:a})=>[h(_(Ca(a.privateAccount)),1)])),_:1}),u(Da,{label:"合计",prop:"totalSalary","min-width":"150",align:"right"},{default:m((({row:a})=>[n("span",z,_(Ca(a.totalSalary)),1)])),_:1}),u(Da,{label:"备注",prop:"remark","min-width":"150",align:"left","show-overflow-tooltip":""},{default:m((({row:a})=>[h(_(a.remark||"-"),1)])),_:1}),u(Da,{label:"操作",width:"120",align:"center",fixed:"right","class-name":"operation-column"},{default:m((({row:a})=>[u(i,{type:"primary",size:"small",onClick:e=>(async a=>{try{ya.value=!0;const e=await l(a);200===e.code?(wa.value=e.data,_a.value=!0):f.error(e.message||"获取工资详情失败")}catch(e){f.error("获取工资详情失败，请稍后再试")}finally{ya.value=!1}})(a.date)},{default:m((()=>e[4]||(e[4]=[h(" 详情 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Oa,ya.value]]),0!==fa.value.length||ya.value?o("",!0):(y(),d("div",B,[u(ja,{description:"暂无工资数据"})])),n("div",Y,[u(Va,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":Sa.page,"page-size":Sa.size,total:Sa.total,"page-sizes":[10,20,50,100],onSizeChange:Aa,onCurrentChange:Ya},null,8,["current-page","page-size","total"])]),u(Ma,{modelValue:_a.value,"onUpdate:modelValue":e[2]||(e[2]=a=>_a.value=a),title:"工资详情",width:"520px","destroy-on-close":"",class:"custom-dialog","close-on-click-modal":!1},{footer:m((()=>[n("span",ba,[u(i,{onClick:e[1]||(e[1]=a=>_a.value=!1)},{default:m((()=>e[23]||(e[23]=[h("关闭")]))),_:1})])])),default:m((()=>[wa.value?(y(),d("div",A,[n("div",C,[n("span",D,_(ka(wa.value.date)),1),n("span",F,_(Ca(wa.value.totalSalary)),1)]),n("div",j,[n("div",V,[e[5]||(e[5]=n("span",{class:"detail-label"},"基本工资",-1)),n("span",E,_(Ca(wa.value.basicSalary)),1)]),n("div",M,[e[6]||(e[6]=n("span",{class:"detail-label"},"奖金",-1)),n("span",O,_(Ca(wa.value.performanceBonus)),1)]),n("div",P,[e[7]||(e[7]=n("span",{class:"detail-label"},"全勤奖",-1)),n("span",$,_(Ca(wa.value.fullAttendanceBonus)),1)]),n("div",U,[e[8]||(e[8]=n("span",{class:"detail-label"},"业务与操作奖金",-1)),n("span",q,_(Ca(wa.value.businessOperationBonus)),1)]),n("div",I,[e[9]||(e[9]=n("span",{class:"detail-label"},"实得金额",-1)),n("span",Z,_(Ca(wa.value.sumSalary)),1)]),n("div",G,[e[10]||(e[10]=n("span",{class:"detail-label"},"请假",-1)),n("span",H,_(Ca(wa.value.leaveDeduction)),1)]),n("div",J,[e[11]||(e[11]=n("span",{class:"detail-label"},"扣款",-1)),n("span",K,_(Ca(wa.value.deduction)),1)]),n("div",L,[e[12]||(e[12]=n("span",{class:"detail-label"},"迟到与缺卡",-1)),n("span",N,_(Ca(wa.value.lateDeduction)),1)]),n("div",Q,[e[13]||(e[13]=n("span",{class:"detail-label"},"社会保险费个人部分",-1)),n("span",R,_(Ca(wa.value.socialSecurityPersonal)),1)]),n("div",T,[e[14]||(e[14]=n("span",{class:"detail-label"},"公积金",-1)),n("span",W,_(Ca(wa.value.providentFund)),1)]),n("div",X,[e[15]||(e[15]=n("span",{class:"detail-label"},"代扣代缴个税",-1)),n("span",aa,_(Ca(wa.value.tax)),1)]),n("div",la,[e[16]||(e[16]=n("span",{class:"detail-label"},"水电费",-1)),n("span",ea,_(Ca(wa.value.waterElectricityFee)),1)]),n("div",ta,[e[17]||(e[17]=n("span",{class:"detail-label"},"实发工资",-1)),n("span",sa,_(Ca(wa.value.actualSalary)),1)]),n("div",ia,[e[18]||(e[18]=n("span",{class:"detail-label"},"报销",-1)),n("span",da,_(Ca(wa.value.reimbursement)),1)]),n("div",na,[e[19]||(e[19]=n("span",{class:"detail-label"},"私帐",-1)),n("span",ra,_(Ca(wa.value.privateAccount)),1)]),u(Ea),n("div",oa,[e[20]||(e[20]=n("span",{class:"detail-label"},"合计",-1)),n("span",ua,_(Ca(wa.value.totalSalary)),1)]),n("div",ca,[e[21]||(e[21]=n("span",{class:"detail-label"},"备注",-1)),n("span",pa,_(wa.value.remark||"-"),1)])]),n("div",va,[n("div",ma,[e[22]||(e[22]=n("span",null,"工资合计",-1)),n("span",ga,_(Ca(wa.value.totalSalary)),1)])])])):o("",!0)])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-c3122368"]]);export{fa as default};
