import{_ as e,a as l,F as t,D as a,K as i,k as o,c as d,e as n,f as s,t as r,j as u,I as p,J as m,n as c,m as y,E as g}from"./index-BDR0Pmj6.js";const v={key:0,class:"extend-dialog-content"},x={class:"dialog-tip",style:{"margin-bottom":"15px"}},b={key:0},f={key:1},h={key:2},V={class:"dialog-tip",style:{"margin-top":"0px","font-size":"12px",color:"#909399"}},_={key:1,class:"empty-extend-dialog"},w={class:"dialog-footer",style:{"margin-top":"10px","padding-bottom":"15px"}},F=e({__name:"BatchExtendDialog",props:{isVisible:{type:Boolean,required:!0},itemsToExtend:{type:Array,required:!0,default:()=>[]},monthOptions:{type:Array,required:!0,default:()=>[]},entityName:{type:String,default:"项目"},itemKeyField:{type:String,required:!0},itemDisplayFields:{type:Array,required:!0,default:()=>[]}},emits:["update:isVisible","submit-extend"],setup(e,{expose:F,emit:E}){const S=e,k=E,N=l([]),q=l([]),K=l(!1),U=()=>{N.value=JSON.parse(JSON.stringify(S.itemsToExtend)).map((e=>({...e,isSelectedForExtend:!0,_internalKey:e[S.itemKeyField]||Symbol()})))};t((()=>S.isVisible),(e=>{e&&(U(),q.value=[])})),t((()=>S.itemsToExtend),(()=>{S.isVisible&&U()}),{deep:!0});const B=a({get:()=>N.value.length>0&&N.value.every((e=>e.isSelectedForExtend)),set:e=>{N.value.forEach((l=>{l.isSelectedForExtend=e}))}}),C=a((()=>{const e=N.value.filter((e=>e.isSelectedForExtend)).length;return e>0&&e<N.value.length})),D=()=>{k("update:isVisible",!1)},O=()=>{const e=N.value.filter((e=>e.isSelectedForExtend));if(0===e.length)return void g.warning(`请至少勾选一个${S.entityName}进行延用`);if(0===q.value.length)return void g.warning("请至少选择一个目标月份");K.value=!0;const l=e.map((e=>{const{isSelectedForExtend:l,_internalKey:t,...a}=e;return a}));k("submit-extend",{selectedItems:l,targetMonths:q.value})};return F({resetLoadingState:()=>{K.value=!1}}),(l,t)=>{const a=u("el-checkbox"),g=u("el-table-column"),F=u("el-table"),E=u("el-option"),S=u("el-select"),U=u("el-form-item"),z=u("el-form"),A=u("el-empty"),I=u("el-button"),J=u("el-dialog");return y(),i(J,{"model-value":e.isVisible,title:`批量延用${e.entityName}`,width:"750px","destroy-on-close":"",class:"custom-dialog batch-extend-dialog-reusable","close-on-click-modal":!1,"onUpdate:modelValue":t[2]||(t[2]=e=>k("update:isVisible",e)),onClose:D},{default:o((()=>[N.value.length>0?(y(),d("div",v,[n("p",x," 勾选需要延用的"+r(e.entityName)+"，并选择目标月份： ",1),s(F,{data:N.value,border:"",size:"small","max-height":"280px",class:"extend-items-table","row-key":"_internalKey"},{default:o((()=>[s(g,{width:"55",align:"center"},{header:o((()=>[s(a,{modelValue:B.value,"onUpdate:modelValue":t[0]||(t[0]=e=>B.value=e),indeterminate:C.value,title:"全选/取消全选"},null,8,["modelValue","indeterminate"])])),default:o((({row:e})=>[s(a,{modelValue:e.isSelectedForExtend,"onUpdate:modelValue":l=>e.isSelectedForExtend=l},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(y(!0),d(p,null,m(e.itemDisplayFields,(e=>(y(),i(g,{key:e.prop,prop:e.prop,label:e.label,width:e.width,"min-width":e.minWidth,align:e.align||"left","show-overflow-tooltip":""},{default:o((({row:l})=>{return[e.isCurrency?(y(),d("span",b,r((t=l[e.prop],null==t?"¥0.00":"¥"+parseFloat(t).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","))),1)):e.formatter?(y(),d("span",f,r(e.formatter(l[e.prop],l)),1)):(y(),d("span",h,r(null===l[e.prop]||void 0===l[e.prop]||""===l[e.prop]?"-":l[e.prop]),1))];var t})),_:2},1032,["prop","label","width","min-width","align"])))),128))])),_:1},8,["data"]),s(z,{"label-width":"160px",style:{"margin-top":"20px","margin-bottom":"0px"}},{default:o((()=>[s(U,{label:"选择延用目标月份",required:""},{default:o((()=>[s(S,{modelValue:q.value,"onUpdate:modelValue":t[1]||(t[1]=e=>q.value=e),multiple:"",filterable:"",placeholder:"请选择一个或多个目标月份",style:{width:"300px"},clearable:"",size:"default"},{default:o((()=>[(y(!0),d(p,null,m(e.monthOptions,(e=>(y(),i(E,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),n("p",V," 提示：系统将为每个勾选的"+r(e.entityName)+"和每个目标月份创建新记录。已存在对应数据不会覆盖，请手动编辑 ",1)])):(y(),d("div",_,[s(A,{description:`没有有效的${e.entityName}可供延用（可能因重复或未选择被过滤）`},null,8,["description"])])),n("div",w,[s(I,{onClick:D},{default:o((()=>t[3]||(t[3]=[c("取消")]))),_:1}),s(I,{type:"primary",loading:K.value,onClick:O,disabled:0===N.value.filter((e=>e.isSelectedForExtend)).length||0===q.value.length},{default:o((()=>t[4]||(t[4]=[c("确定延用")]))),_:1},8,["loading","disabled"])])])),_:1},8,["model-value","title"])}}},[["__scopeId","data-v-c697c350"]]);export{F as B};
