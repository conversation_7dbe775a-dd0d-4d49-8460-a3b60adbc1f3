import request from '@/utils/request'

/**
 * {{CHENGQI: 销售日报管理端API接口}}
 * {{CHENGQI: 任务ID: P3-LD-010}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 销售日报管理端API调用封装}}
 */

// 分页查询销售日报列表（管理端）
export function getSalesReportPage(params) {
    return request({
        url: '/sales-report/page',
        method: 'get',
        params
    })
}

// 获取日报详情
export function getSalesReportById(id) {
    return request({
        url: `/sales-report/detail/${id}`,
        method: 'get'
    })
}

// 删除销售日报（管理员权限）
export function deleteSalesReport(id) {
    return request({
        url: `/sales-report/${id}`,
        method: 'delete'
    })
}

// 注：已删除未实现的统计相关API：
// - getSalesReportStatistics (后端未实现)
// - getResponsibilityDistribution (后端未实现)
// - getChecklistStats (后端未实现)

// 批量删除日报（管理员权限）
export function batchDeleteSalesReports(ids) {
    return request({
        url: '/sales-report/batch-delete',
        method: 'delete',
        data: { ids }
    })
}

// 注：已删除 exportSalesReports (后端未实现导出功能)

// 编辑销售日报（管理员权限）
export function editSalesReport(data) {
    return request({
        url: '/sales-report/department/edit',
        method: 'put',
        data
    })
}

// 获取客户列表（使用现有端点）
export function getAllClients(params = {}) {
    return request({
        url: '/sales-report/my-clients',
        method: 'get',
        params
    })
}

// 获取特定员工的客户列表（管理员权限）
export function getEmployeeClients(employeeId, params = {}) {
    return request({
        url: `/sales-report/admin/employee-clients/${employeeId}`,
        method: 'get',
        params
    })
}

// 获取特定日报的详细信息（管理员权限）
export function getReportDetailById(reportId) {
    return request({
        url: `/sales-report/admin/report-detail/${reportId}`,
        method: 'get'
    })
}

// 为了兼容组件中的导入名称，添加别名导出
export { batchDeleteSalesReports as batchDeleteSalesReport }
export { getSalesReportPage as getAllSalesReportPage }
export { editSalesReport as editDepartmentReport }
export { getAllClients as getMyClients }

// {{CHENGQI: 管理端API接口封装完成}}
