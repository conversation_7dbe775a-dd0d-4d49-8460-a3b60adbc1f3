import{a0 as e,_ as a,a as l,r as t,o as r,c as o,e as n,R as d,f as s,K as i,g as u,k as m,S as p,j as c,T as f,a3 as v,E as g,m as y,h,U as w,I as b,J as _,n as I,V as P,Z as k,W as V,a4 as x,t as N,Y as C,O as E}from"./index-BDR0Pmj6.js";import{b as M}from"./department-jpUZgat6.js";import{g as z}from"./employee-DbFXp2d5.js";import{I as Y}from"./ImportErrorsDialog-DbpGOqA7.js";const $={class:"performance-container"},U={class:"toolbar"},S={class:"search-box"},q={class:"action-box"},j={key:0},L={key:0},F={key:0},T={key:0},B={key:0},D={key:0},O={class:"operation-buttons"},R={class:"pagination-container"},A={class:"empty-text"},K={key:0,class:"el-form-item__help"},J={class:"dialog-footer"},W=a({__name:"PerformanceAnalysis",setup(a){const W=l([]),Z=l(!0),G=l(""),H=l(""),Q=l(""),X=l([]),ee=l([]),ae=l(!1),le=l(!1),te=l(""),re=l([]),oe=t({page:1,size:10,total:0}),ne=l(!1),de=l("add"),se=l(null),ie=l(!1),ue=t({id:null,employeeId:null,employeeName:"",departmentId:null,department:"",position:"",date:"",estimatedPerformance:0,actualPerformance:0}),me={departmentId:[{required:!0,message:"请选择部门",trigger:"change"}],employeeId:[{required:!0,message:"请选择员工",trigger:"change"}],date:[{required:!0,message:"请选择年月",trigger:"change"}],estimatedPerformance:[{required:!0,message:"请输入预估业绩",trigger:"blur"},{type:"number",message:"预估业绩必须为数字",trigger:"blur"}],actualPerformance:[{required:!0,message:"请输入实际业绩",trigger:"blur"},{type:"number",message:"实际业绩必须为数字",trigger:"blur"}]},pe=async()=>{ae.value=!0;try{const e=await M();200===e.code?X.value=e.data:g.error(e.message||"获取部门列表失败")}catch(e){g.error("加载部门列表失败: "+(e.message||"未知错误"))}finally{ae.value=!1}},ce=e=>{ue.employeeId="",ue.position="",ue.departmentId=e},fe=async e=>{if(te.value)if(e){0===X.value.length&&await pe(),le.value=!0;try{const a=await z({pageNum:1,pageSize:10,name:e,departmentId:te.value});if(200===a.code){let e=[];a.data&&a.data.list?e=a.data.list:Array.isArray(a.data)&&(e=a.data),e.forEach((e=>{if(e.departmentId){const a=X.value.find((a=>a.departmentId===e.departmentId||a.departmentId===parseInt(e.departmentId,10)));e.departmentName=a?a.departmentName:"未知部门"}else e.departmentName="未知部门"})),ee.value=e}else g.error(a.message||"搜索员工失败")}catch(a){g.error("搜索员工失败: "+(a.message||"未知错误"))}finally{le.value=!1}}else ee.value=[];else g.warning("请先选择部门")},ve=e=>{if(!e)return void(ue.position="");const a=ee.value.find((a=>a.employeeId===e));a&&(ue.position=a.positionName||"")},ge=async()=>{Z.value=!0,W.value=[];try{const a={page:oe.page,size:oe.size,employeeName:G.value||void 0,departmentId:H.value||void 0,yearMonth:Q.value||void 0},l=await function(a){return e({url:"/performance/page",method:"get",params:a})}(a);200===l.code?(W.value=l.data.records||[],oe.total=l.data.total||0):g.error(l.message||"获取业绩数据失败")}catch(a){g.error("加载业绩数据失败: "+(a.message||"未知错误"))}finally{Z.value=!1}},ye=()=>{se.value&&se.value.resetFields(),ue.id=null,ue.employeeId=null,ue.employeeName="",ue.departmentId=null,ue.department="",ue.position="",ue.date="",ue.estimatedPerformance=0,ue.actualPerformance=0,te.value=""},he=()=>{ne.value=!1,ye()},we=()=>{de.value="add",ye(),ne.value=!0},be=async a=>{de.value="edit",ye();try{0===X.value.length&&await pe();const t=await(l=a.id,e({url:`/performance/${l}`,method:"get"}));if(200===t.code){const e=t.data;ue.id=e.id,ue.employeeId=e.employeeId,ue.employeeName=e.employeeName,ue.department=e.department,ue.position=e.position,ue.date=e.date,ue.estimatedPerformance=e.estimatedPerformance,ue.actualPerformance=e.actualPerformance;let a=null;if(e.department){const l=X.value.find((a=>a.departmentName===e.department));l&&(a=l.departmentId)}if(e.departmentId&&(a="string"==typeof e.departmentId?parseInt(e.departmentId,10):e.departmentId),ue.departmentId=a,te.value=a,!a&&e.department){await pe();const l=X.value.find((a=>a.departmentName===e.department));l&&(a=l.departmentId,ue.departmentId=a,te.value=a)}e.employeeId&&(ee.value=[{employeeId:e.employeeId,name:e.employeeName,positionName:e.position,departmentId:a}]),ne.value=!0}else g.error(t.message||"获取业绩详情失败")}catch(t){g.error("获取业绩详情失败: "+(t.message||"未知错误"))}var l},_e=a=>{E.confirm(`确定要删除员工 "${a.employeeName}" 在 "${a.date}" 的业绩记录吗？此操作不可撤销！`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{Z.value=!0;try{const t=await(l=a.id,e({url:`/performance/${l}`,method:"delete"}));200===t.code?(g({type:"success",message:"删除成功",duration:2e3}),ge()):(g.error(t.message||"删除失败"),Z.value=!1)}catch(t){g.error("删除失败: "+(t.message||"未知错误")),Z.value=!1}var l})).catch((()=>{}))},Ie=()=>{if(0===re.value.length)return void g.warning("请选择要删除的记录");const a=re.value.map((e=>e.id)),l=re.value.map((e=>`${e.employeeName}(${e.date})`)).join("、");E.confirm(`确定要删除以下员工的业绩记录吗？\n${l}\n此操作不可撤销！`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{Z.value=!0;try{const l=await function(a){return e({url:"/performance/batch",method:"delete",data:a})}(a);200===l.code?(g({type:"success",message:"批量删除成功",duration:2e3}),re.value=[],ge()):(g.error(l.message||"批量删除失败"),Z.value=!1)}catch(l){g.error("批量删除失败: "+(l.message||"未知错误")),Z.value=!1}})).catch((()=>{}))},Pe=async a=>{a&&await a.validate((async a=>{if(!a)return g.warning("请完善表单信息"),!1;ie.value=!0;try{const a={id:ue.id,employeeId:ue.employeeId,date:ue.date,estimatedPerformance:ue.estimatedPerformance,actualPerformance:ue.actualPerformance};let t;t="add"===de.value?await(l=a,e({url:"/performance",method:"post",data:l})):await function(a){return e({url:"/performance",method:"put",data:a})}(a),200===t.code?(g({type:"success",message:"add"===de.value?"添加成功":"更新成功",duration:2e3}),ne.value=!1,ye(),ge()):g.error(t.message||("add"===de.value?"添加失败":"更新失败"))}catch(t){g.error("提交失败: "+(t.message||"未知错误"))}finally{ie.value=!1}var l}))},ke=e=>{re.value=e},Ve=()=>{oe.page=1,ge()},xe=()=>{G.value="",H.value="",Q.value="",oe.page=1,ge()},Ne=e=>{oe.page=e,ge()},Ce=e=>{oe.size=e,oe.page=1,ge()},Ee=e=>null==e?"¥0.00":"¥"+parseFloat(e).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),Me=l(!1),ze=l(!1),Ye=l(null),$e=()=>{Me.value=!0},Ue=(e,a)=>{if("performance"===a){Ye.value=e,Z.value=!0;try{e&&(e.failureCount>0||e.generalErrors&&e.generalErrors.length>0)?ze.value=!0:Me.value=!1,ge()}catch(l){g.error("处理导入结果时发生错误: "+(l.message||"未知错误")),Ye.value=null}finally{Z.value=!1}}},Se=e=>`业绩数据全部导入成功！共处理 ${e.processedRows||0} 行，成功导入 ${e.successCount||0} 条记录。`,qe=e=>{let a=`业绩数据导入处理完成。共处理 ${e.processedRows||0} 行，成功 ${e.successCount||0} 行，失败/跳过 ${e.failureCount||0} 行。`;return e.generalErrors&&e.generalErrors.length>0&&(a+=` 通用错误: ${e.generalErrors.join("; ")}`),a};return r((()=>{pe(),ge()})),(e,a)=>{const l=c("el-icon"),t=c("el-input"),r=c("el-option"),g=c("el-select"),E=c("el-date-picker"),M=c("el-button"),z=c("el-table-column"),pe=c("el-tag"),ge=c("el-table"),ye=c("el-pagination"),je=c("el-form-item"),Le=c("el-input-number"),Fe=c("el-form"),Te=c("el-dialog"),Be=f("loading");return y(),o("div",$,[n("div",U,[n("div",S,[s(t,{modelValue:G.value,"onUpdate:modelValue":a[0]||(a[0]=e=>G.value=e),placeholder:"搜索员工姓名",clearable:"",onKeyup:p(Ve,["enter"])},{prefix:m((()=>[s(l,null,{default:m((()=>[s(h(w))])),_:1})])),_:1},8,["modelValue"]),s(g,{modelValue:H.value,"onUpdate:modelValue":a[1]||(a[1]=e=>H.value=e),placeholder:"选择部门",clearable:"",loading:ae.value},{default:m((()=>[(y(!0),o(b,null,_(X.value,(e=>(y(),i(r,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"]),s(E,{modelValue:Q.value,"onUpdate:modelValue":a[2]||(a[2]=e=>Q.value=e),type:"month",placeholder:"选择年月",format:"YYYY-MM","value-format":"YYYY-MM"},null,8,["modelValue"]),s(M,{type:"primary",onClick:Ve},{default:m((()=>[s(l,null,{default:m((()=>[s(h(w))])),_:1}),a[12]||(a[12]=I("搜索 "))])),_:1}),s(M,{onClick:xe},{default:m((()=>[s(l,null,{default:m((()=>[s(h(P))])),_:1}),a[13]||(a[13]=I("重置 "))])),_:1})]),n("div",q,[s(M,{type:"danger",disabled:0===re.value.length,onClick:Ie},{default:m((()=>[s(l,null,{default:m((()=>[s(h(k))])),_:1}),a[14]||(a[14]=I("批量删除 "))])),_:1},8,["disabled"]),s(M,{type:"primary",class:"add-btn",onClick:we},{default:m((()=>[s(l,null,{default:m((()=>[s(h(V))])),_:1}),a[15]||(a[15]=I("添加业绩 "))])),_:1}),s(M,{type:"success",onClick:$e},{default:m((()=>[s(l,null,{default:m((()=>[s(h(x))])),_:1}),a[16]||(a[16]=I("导入业绩 "))])),_:1})])]),d((y(),i(ge,{data:W.value,border:"","row-key":"id",onSelectionChange:ke,"max-height":"calc(100vh - 220px)",class:"custom-table"},{default:m((()=>[s(z,{type:"selection",width:"55",align:"center"}),s(z,{label:"员工姓名",prop:"employeeName","min-width":"100","show-overflow-tooltip":""}),s(z,{label:"部门",prop:"department","min-width":"120","show-overflow-tooltip":""}),s(z,{label:"职位",prop:"position","min-width":"120","show-overflow-tooltip":""}),s(z,{label:"年月",prop:"date",width:"100",align:"center"},{default:m((({row:e})=>[s(pe,{type:"info"},{default:m((()=>[I(N(e.date),1)])),_:2},1024)])),_:1}),s(z,{label:"预估业绩",prop:"estimatedPerformance","min-width":"150",align:"right"},{default:m((({row:e})=>[I(N(Ee(e.estimatedPerformance)),1)])),_:1}),s(z,{label:"实际业绩",prop:"actualPerformance","min-width":"150",align:"right"},{default:m((({row:e})=>[I(N(Ee(e.actualPerformance)),1)])),_:1}),s(z,{label:"本月备用金",prop:"totalPettyCash","min-width":"150",align:"right"},{default:m((({row:e})=>[null!==e.totalPettyCash&&void 0!==e.totalPettyCash?(y(),o("span",j,N(Ee(e.totalPettyCash)),1)):(y(),i(pe,{key:1,type:"info",size:"small"},{default:m((()=>a[17]||(a[17]=[I("暂无数据")]))),_:1}))])),_:1}),s(z,{label:"本月发布工资",prop:"totalSalary","min-width":"150",align:"right"},{default:m((({row:e})=>[null!==e.totalSalary&&void 0!==e.totalSalary?(y(),o("span",L,N(Ee(e.totalSalary)),1)):(y(),i(pe,{key:1,type:"info",size:"small"},{default:m((()=>a[18]||(a[18]=[I("暂无数据")]))),_:1}))])),_:1}),s(z,{label:"本月平均部门开销",prop:"averageDepartmentExpense","min-width":"170",align:"right"},{default:m((({row:e})=>[null!==e.averageDepartmentExpense&&void 0!==e.averageDepartmentExpense?(y(),o("span",F,N(Ee(e.averageDepartmentExpense)),1)):(y(),i(pe,{key:1,type:"info",size:"small"},{default:m((()=>a[19]||(a[19]=[I("暂无数据")]))),_:1}))])),_:1}),s(z,{label:"本月员工费用",prop:"totalEmployeeOtherExpenses","min-width":"160",align:"right"},{default:m((({row:e})=>[null!==e.totalEmployeeOtherExpenses&&void 0!==e.totalEmployeeOtherExpenses?(y(),o("span",T,N(Ee(e.totalEmployeeOtherExpenses)),1)):(y(),i(pe,{key:1,type:"info",size:"small"},{default:m((()=>a[20]||(a[20]=[I("暂无数据")]))),_:1}))])),_:1}),s(z,{label:"本月预计盈亏",prop:"estimatedMonthlyProfitLoss","min-width":"160",align:"right"},{default:m((({row:e})=>[null!==e.estimatedMonthlyProfitLoss&&void 0!==e.estimatedMonthlyProfitLoss?(y(),o("span",B,N(Ee(e.estimatedMonthlyProfitLoss)),1)):(y(),i(pe,{key:1,type:"info",size:"small"},{default:m((()=>a[21]||(a[21]=[I("暂无数据")]))),_:1}))])),_:1}),s(z,{label:"本月实际盈亏",prop:"actualMonthlyProfitLoss","min-width":"160",align:"right"},{default:m((({row:e})=>[null!==e.actualMonthlyProfitLoss&&void 0!==e.actualMonthlyProfitLoss?(y(),o("span",D,N(Ee(e.actualMonthlyProfitLoss)),1)):(y(),i(pe,{key:1,type:"info",size:"small"},{default:m((()=>a[22]||(a[22]=[I("暂无数据")]))),_:1}))])),_:1}),s(z,{label:"操作",width:"150",align:"center",fixed:"right","class-name":"operation-column"},{default:m((({row:e})=>[n("div",O,[s(M,{class:"edit-btn",onClick:a=>be(e),title:"编辑"},{default:m((()=>[s(l,null,{default:m((()=>[s(h(C))])),_:1})])),_:2},1032,["onClick"]),s(M,{class:"delete-btn",onClick:a=>_e(e),title:"删除"},{default:m((()=>[s(l,null,{default:m((()=>[s(h(k))])),_:1})])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[Be,Z.value]]),n("div",R,[s(ye,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":oe.page,"page-size":oe.size,total:oe.total,"page-sizes":[10,20,50,100],onSizeChange:Ce,onCurrentChange:Ne},null,8,["current-page","page-size","total"])]),s(Te,{modelValue:ne.value,"onUpdate:modelValue":a[9]||(a[9]=e=>ne.value=e),title:"add"===de.value?"添加业绩记录":"编辑业绩记录",width:"500px","destroy-on-close":"",class:"custom-dialog"},{default:m((()=>[s(Fe,{ref_key:"formRef",ref:se,model:ue,rules:me,"label-width":"100px",class:"dialog-form"},{default:m((()=>[s(je,{label:"部门",prop:"departmentId",required:""},{default:m((()=>[s(g,{modelValue:te.value,"onUpdate:modelValue":a[3]||(a[3]=e=>te.value=e),placeholder:"选择部门",loading:ae.value,onChange:ce},{default:m((()=>[(y(!0),o(b,null,_(X.value,(e=>(y(),i(r,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),s(je,{label:"员工",prop:"employeeId",required:""},{default:m((()=>[s(g,{modelValue:ue.employeeId,"onUpdate:modelValue":a[4]||(a[4]=e=>ue.employeeId=e),placeholder:"请先选择部门再输入员工名称搜索",disabled:!te.value,onChange:ve,remote:"",filterable:"","remote-method":fe,loading:le.value,style:{width:"100%"}},{empty:m((()=>[n("p",A,N(te.value?"请输入员工名称搜索":"请先选择部门"),1)])),default:m((()=>[(y(!0),o(b,null,_(ee.value,(e=>(y(),i(r,{key:e.employeeId,label:e.name+(e.departmentName?` (${e.departmentName}${e.positionName?"-"+e.positionName:""})`:e.positionName?` (${e.positionName})`:""),value:e.employeeId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled","loading"]),te.value||"add"!==de.value?u("",!0):(y(),o("div",K))])),_:1}),s(je,{label:"年月",prop:"date",required:""},{default:m((()=>[s(E,{modelValue:ue.date,"onUpdate:modelValue":a[5]||(a[5]=e=>ue.date=e),type:"month",placeholder:"选择年月",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),s(je,{label:"预估业绩",prop:"estimatedPerformance",required:""},{default:m((()=>[s(Le,{modelValue:ue.estimatedPerformance,"onUpdate:modelValue":a[6]||(a[6]=e=>ue.estimatedPerformance=e),precision:2,step:1e3,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),s(je,{label:"实际业绩",prop:"actualPerformance",required:""},{default:m((()=>[s(Le,{modelValue:ue.actualPerformance,"onUpdate:modelValue":a[7]||(a[7]=e=>ue.actualPerformance=e),precision:2,step:1e3,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),n("div",J,[s(M,{onClick:he},{default:m((()=>a[23]||(a[23]=[I("取消")]))),_:1}),s(M,{type:"primary",loading:ie.value,onClick:a[8]||(a[8]=e=>Pe(se.value))},{default:m((()=>a[24]||(a[24]=[I("确定")]))),_:1},8,["loading"])])])),_:1},8,["modelValue","title"]),Me.value?(y(),i(v,{key:0,"model-value":Me.value,"onUpdate:modelValue":a[10]||(a[10]=e=>Me.value=e),importType:"performance",dialogTitle:"导入业绩数据",templateFileName:"业绩导入模板.xlsx",uploadUrl:"/api/performance/import",maxFileSizeMB:200,onImportSuccess:Ue,successMessageFormatter:Se,partialSuccessMessageFormatter:qe},null,8,["model-value"])):u("",!0),s(Y,{modelValue:ze.value,"onUpdate:modelValue":a[11]||(a[11]=e=>ze.value=e),"import-result":Ye.value,title:"业绩导入结果"},null,8,["modelValue","import-result"])])}}},[["__scopeId","data-v-a1c16e0f"]]);export{W as default};
