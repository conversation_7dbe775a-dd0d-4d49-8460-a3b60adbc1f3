import{a0 as e,_ as a,u as l,D as t,a as o,r as n,o as d,c as r,e as u,R as i,f as s,k as p,S as m,j as c,T as v,K as g,E as h,s as f,m as y,h as w,U as b,I as _,J as I,n as V,V as x,Z as k,W as C,g as N,a1 as $,t as Y,Y as T,a2 as U,O as B}from"./index-BDR0Pmj6.js";import{b as z}from"./department-jpUZgat6.js";import{g as q}from"./employee-DbFXp2d5.js";function M(a,l){return e({url:`/petty-cash/approve/${a}`,method:"put",data:{status:l}})}const j={class:"petty-cash-container"},S={class:"toolbar"},E={class:"search-box"},F={class:"action-box"},P={class:"operation-buttons"},A={class:"pagination-container"},D={class:"empty-text"},K={key:0,class:"el-form-item__help"},R={class:"dialog-footer"},W={class:"dialog-footer"},J={class:"action-toolbar"},L={class:"approval-action-buttons"},O=a({__name:"PettyCash",setup(a){const O=l(),Z=t((()=>O.user)),G=o([]),H=o(!0),Q=o(""),X=o(""),ee=o(""),ae=o(""),le=o([]),te=o([]),oe=o(!1),ne=o(!1),de=o(""),re=o([]),ue=o(!1),ie=o(!1),se=o([]),pe=o([]),me=o(0),ce=n({page:1,size:10,total:0}),ve=o(!1),ge=o("add"),he=o(null),fe=o(!1),ye=o(!1),we=o(null),be=o(""),_e=[{label:"审核中",value:"审核中"},{label:"已审核",value:"已审核"},{label:"已拒绝",value:"已拒绝"}],Ie=n({id:null,employeeId:null,employeeName:"",departmentId:null,date:"",purpose:"",amount:0,status:"审核中"}),Ve={departmentId:[{required:!0,message:"请选择部门",trigger:"change"}],employeeId:[{required:!0,message:"请选择员工",trigger:"change"}],purpose:[{required:!0,message:"请输入用途",trigger:"blur"}],amount:[{required:!0,message:"请输入金额",trigger:"blur"},{validator:(e,a,l)=>{null==a?l(new Error("请输入金额")):"number"==typeof a&&a>0?l():l(new Error("金额必须大于0"))},trigger:"blur"}],date:[{required:!0,message:"请选择年月",trigger:"change"}]},xe=async()=>{if(Z.value&&Z.value.departmentId)try{H.value=!0;const a={page:ce.page,size:ce.size,departmentId:X.value||null,employeeName:Q.value,status:ee.value,date:ae.value},l=await function(a){return e({url:"/petty-cash/page",method:"get",params:a})}(a);200===l.code?(G.value=l.data.list||[],ce.total=l.data.total||0):h.error(l.message||"获取备用金数据失败")}catch(a){h.error("加载备用金数据失败: "+(a.message||"未知错误"))}finally{H.value=!1}else h.error("无法获取部门信息，请重新登录")},ke=async()=>{oe.value=!0;try{const e=await z();200===e.code?le.value=e.data:h.error(e.message||"获取部门列表失败")}catch(e){h.error("加载部门列表失败: "+(e.message||"未知错误"))}finally{oe.value=!1}},Ce=async()=>{try{const a=await e({url:"/petty-cash/pending-count",method:"get"});200===a.code&&(me.value=a.data||0,me.value>0&&f({title:"待审批提醒",message:`您有 ${me.value} 条备用金申请待审批`,type:"info",duration:5e3,position:"top-right"}))}catch(a){}},Ne=async()=>{ue.value=!0,ie.value=!0,pe.value=[];try{const a=await e({url:"/petty-cash/pending",method:"get"});200===a.code?se.value=a.data||[]:(h.error(a.message||"获取待审批备用金列表失败"),se.value=[])}catch(a){h.error("获取待审批备用金列表失败: "+(a.message||"未知错误")),se.value=[]}finally{ie.value=!1}},$e=()=>{Ce()},Ye=e=>{pe.value=e},Te=()=>{0!==pe.value.length?B.confirm(`确定要通过选中的 ${pe.value.length} 个备用金申请吗？`,"批量审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"success"}).then((async()=>{ie.value=!0;try{const e=pe.value.map((e=>M(e.id,"已审核"))),a=await Promise.all(e),l=a.filter((e=>200===e.code)).length;if(l>0){h.success(`已通过 ${l} 个备用金申请`);const e=pe.value.map(((e,l)=>200===a[l].code?e.id:null)).filter((e=>null!==e));se.value=se.value.filter((a=>!e.includes(a.id))),pe.value=[],xe()}l<pe.value.length&&h.warning(pe.value.length-l+" 个备用金申请审批失败，请重试")}catch(e){h.error("批量审批操作失败: "+(e.message||"未知错误"))}finally{ie.value=!1}})).catch((()=>{})):h.warning("请先选择要审批的备用金申请")},Ue=()=>{0!==pe.value.length?B.confirm(`确定要拒绝选中的 ${pe.value.length} 个备用金申请吗？`,"批量审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{ie.value=!0;try{const e=pe.value.map((e=>M(e.id,"已拒绝"))),a=await Promise.all(e),l=a.filter((e=>200===e.code)).length;if(l>0){h.success(`已拒绝 ${l} 个备用金申请`);const e=pe.value.map(((e,l)=>200===a[l].code?e.id:null)).filter((e=>null!==e));se.value=se.value.filter((a=>!e.includes(a.id))),pe.value=[],xe()}l<pe.value.length&&h.warning(pe.value.length-l+" 个备用金申请拒绝失败，请重试")}catch(e){h.error("批量拒绝操作失败: "+(e.message||"未知错误"))}finally{ie.value=!1}})).catch((()=>{})):h.warning("请先选择要审批的备用金申请")},Be=()=>{ce.page=1,xe()},ze=()=>{Q.value="",X.value="",ee.value="",ae.value="",ce.page=1,xe()},qe=e=>{re.value=e},Me=e=>{ce.page=e,xe()},je=e=>{ce.size=e,ce.page=1,xe()};d((()=>{ke(),xe(),Ce()}));const Se=e=>null==e?"¥0.00":"¥"+parseFloat(e).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),Ee=e=>{const a=le.value.find((a=>a.departmentId===e));return a?a.departmentName:""},Fe=e=>{switch(e){case"已审核":return"success";case"已拒绝":return"danger";default:return"warning"}},Pe=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},Ae=e=>{e<=0&&(h.warning("金额必须大于0"),Ie.amount=.01)},De=async e=>{if(de.value)if(e){0===le.value.length&&await ke(),ne.value=!0;try{const a=await q({pageNum:1,pageSize:10,name:e,departmentId:de.value});if(200===a.code){let e=[];a.data&&a.data.list?e=a.data.list:Array.isArray(a.data)&&(e=a.data),e.forEach((e=>{if(e.departmentId){const a=le.value.find((a=>a.departmentId===e.departmentId||a.departmentId===parseInt(e.departmentId,10)));e.departmentName=a?a.departmentName:"未知部门"}else e.departmentName="未知部门"})),te.value=e}else h.error(a.message||"搜索员工失败")}catch(a){h.error("搜索员工失败: "+(a.message||"未知错误"))}finally{ne.value=!1}}else te.value=[];else h.warning("请先选择部门")},Ke=e=>{Ie.employeeId=null,Ie.employeeName="",Ie.departmentId=e},Re=e=>{const a=te.value.find((a=>a.employeeId===e));a&&(Ie.employeeName=a.name)},We=()=>{ge.value="add",Je(),ve.value=!0},Je=()=>{he.value&&he.value.resetFields(),Ie.id=null,Ie.employeeId=null,Ie.employeeName="",Ie.departmentId=null,Ie.date="",Ie.purpose="",Ie.amount=0,Ie.status="审核中",de.value=""},Le=()=>{ve.value=!1,Je()},Oe=()=>{if(0===re.value.length)return void h.warning("请选择要删除的记录");const a=re.value.map((e=>e.id)),l=re.value.map((e=>`${e.employeeName}(${e.purpose})`)).join("、");B.confirm(`确定要删除以下员工的备用金申请吗？\n${l}\n此操作不可撤销！`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{H.value=!0;try{const l=await function(a){return e({url:"/petty-cash/batch",method:"delete",data:{ids:a}})}(a);200===l.code?(h({type:"success",message:"批量删除成功",duration:2e3}),re.value=[],xe()):(h.error(l.message||"批量删除失败"),H.value=!1)}catch(l){h.error("批量删除失败: "+(l.message||"未知错误")),H.value=!1}})).catch((()=>{}))},Ze=async a=>{a&&await a.validate((async a=>{if(!a)return h.warning("请完善表单信息"),!1;fe.value=!0;try{const a={id:Ie.id,employeeId:Ie.employeeId,date:Ie.date,purpose:Ie.purpose,amount:Ie.amount,status:Ie.status};let t;t="add"===ge.value?await(l=a,e({url:"/petty-cash",method:"post",data:l})):await function(a){return e({url:`/petty-cash/${a.id}`,method:"put",data:a})}(a),200===t.code?(h({type:"success",message:"add"===ge.value?"添加成功":"更新成功",duration:2e3}),ve.value=!1,Je(),xe()):h.error(t.message||("add"===ge.value?"添加失败":"更新失败"))}catch(t){h.error("提交失败: "+(t.message||"未知错误"))}finally{fe.value=!1}var l}))},Ge=async a=>{ge.value="edit",Je();try{0===le.value.length&&await ke();const t=await(l=a.id,e({url:`/petty-cash/${l}`,method:"get"}));if(200===t.code){const e=t.data;if(Ie.id=e.id,Ie.employeeId=e.employeeId,Ie.employeeName=e.employeeName,Ie.date=e.date,Ie.purpose=e.purpose,Ie.amount=e.amount,Ie.status=e.status,e.departmentId)Ie.departmentId=e.departmentId,de.value=e.departmentId;else if(e.department){const a=le.value.find((a=>a.departmentName===e.department));a&&(Ie.departmentId=a.departmentId,de.value=a.departmentId)}e.employeeId&&(te.value=[{employeeId:e.employeeId,name:e.employeeName,positionName:e.position||"",departmentId:Ie.departmentId,departmentName:e.department||Ee(Ie.departmentId)}]),ve.value=!0}else h.error(t.message||"获取备用金详情失败")}catch(t){h.error("获取备用金详情失败: "+(t.message||"未知错误"))}var l},He=a=>{B.confirm(`确定要删除员工 "${a.employeeName}" 的备用金申请(用途: ${a.purpose})吗？此操作不可撤销！`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{H.value=!0;try{const t=await(l=a.id,e({url:`/petty-cash/${l}`,method:"delete"}));200===t.code?(h({type:"success",message:"删除成功",duration:2e3}),xe()):(h.error(t.message||"删除失败"),H.value=!1)}catch(t){h.error("删除失败: "+(t.message||"未知错误")),H.value=!1}var l})).catch((()=>{}))},Qe=async()=>{if(be.value)try{const e=await M(we.value,be.value);200===e.code?(h.success("审核成功"),ye.value=!1,xe()):h.error(e.message||"审核失败")}catch(e){h.error("审核失败: "+(e.message||"未知错误"))}else h.warning("请选择审核状态")};return(e,a)=>{const l=c("el-icon"),t=c("el-input"),o=c("el-option"),n=c("el-select"),d=c("el-date-picker"),f=c("el-form-item"),z=c("el-button"),q=c("el-badge"),O=c("el-table-column"),Z=c("el-tag"),we=c("el-table"),ke=c("el-pagination"),Ce=c("el-input-number"),Ee=c("el-form"),Je=c("el-dialog"),Xe=c("el-radio"),ea=c("el-radio-group"),aa=v("loading");return y(),r("div",j,[u("div",S,[u("div",E,[s(t,{modelValue:Q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.value=e),placeholder:"搜索员工姓名",clearable:"",onKeyup:m(Be,["enter"]),style:{width:"150px"}},{prefix:p((()=>[s(l,null,{default:p((()=>[s(w(b))])),_:1})])),_:1},8,["modelValue"]),s(n,{modelValue:X.value,"onUpdate:modelValue":a[1]||(a[1]=e=>X.value=e),placeholder:"选择部门",clearable:"",loading:oe.value,style:{width:"150px"}},{default:p((()=>[(y(!0),r(_,null,I(le.value,(e=>(y(),g(o,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"]),s(n,{modelValue:ee.value,"onUpdate:modelValue":a[2]||(a[2]=e=>ee.value=e),placeholder:"选择状态",clearable:"",onClear:xe,style:{width:"150px"}},{default:p((()=>[s(o,{label:"待审批",value:"0"}),s(o,{label:"已批准",value:"1"}),s(o,{label:"已驳回",value:"2"})])),_:1},8,["modelValue"]),s(f,{label:""},{default:p((()=>[s(d,{modelValue:ae.value,"onUpdate:modelValue":a[3]||(a[3]=e=>ae.value=e),type:"month",placeholder:"选择年月",format:"YYYY-MM","value-format":"YYYY-MM",clearable:"",onClear:xe,style:{width:"150px"}},null,8,["modelValue"])])),_:1}),s(z,{type:"primary",onClick:Be},{default:p((()=>[s(l,null,{default:p((()=>[s(w(b))])),_:1}),a[16]||(a[16]=V("搜索 "))])),_:1}),s(z,{onClick:ze},{default:p((()=>[s(l,null,{default:p((()=>[s(w(x))])),_:1}),a[17]||(a[17]=V("重置 "))])),_:1})]),u("div",F,[s(z,{type:"danger",disabled:0===re.value.length,onClick:Oe},{default:p((()=>[s(l,null,{default:p((()=>[s(w(k))])),_:1}),a[18]||(a[18]=V("批量删除 "))])),_:1},8,["disabled"]),s(z,{type:"primary",class:"add-btn",onClick:We},{default:p((()=>[s(l,null,{default:p((()=>[s(w(C))])),_:1}),a[19]||(a[19]=V("添加备用金 "))])),_:1}),s(z,{type:"success",class:"approve-list-btn",onClick:Ne},{default:p((()=>[s(l,null,{default:p((()=>[s(w($))])),_:1}),a[20]||(a[20]=V("审批备用金 ")),me.value>0?(y(),g(q,{key:0,value:me.value,class:"approval-badge"},null,8,["value"])):N("",!0)])),_:1})])]),i((y(),g(we,{data:G.value,border:"","row-key":"id",onSelectionChange:qe,"max-height":"calc(100vh - 220px)",class:"custom-table",fit:""},{default:p((()=>[s(O,{type:"selection",width:"55",align:"center"}),s(O,{label:"员工姓名",prop:"employeeName","min-width":"100","show-overflow-tooltip":"",align:"center"}),s(O,{label:"部门",prop:"department","min-width":"120","show-overflow-tooltip":"",align:"center"}),s(O,{label:"职位",prop:"position","min-width":"120","show-overflow-tooltip":"",align:"center"}),s(O,{label:"年月",prop:"date","min-width":"100","show-overflow-tooltip":"",align:"center"}),s(O,{label:"用途",prop:"purpose","min-width":"150","show-overflow-tooltip":"",align:"center"}),s(O,{label:"金额",prop:"amount","min-width":"120",align:"right"},{default:p((({row:e})=>[V(Y(Se(e.amount)),1)])),_:1}),s(O,{label:"状态",prop:"status",width:"100",align:"center"},{default:p((({row:e})=>[s(Z,{type:Fe(e.status)},{default:p((()=>[V(Y(e.status),1)])),_:2},1032,["type"])])),_:1}),s(O,{label:"创建时间",prop:"createTime","min-width":"160","show-overflow-tooltip":"",align:"center"},{default:p((({row:e})=>[V(Y(Pe(e.createTime)),1)])),_:1}),s(O,{label:"操作",width:"180",align:"center",fixed:"right","class-name":"operation-column"},{default:p((({row:e})=>[u("div",P,[s(z,{class:"edit-btn",onClick:a=>Ge(e),title:"编辑"},{default:p((()=>[s(l,null,{default:p((()=>[s(w(T))])),_:1})])),_:2},1032,["onClick"]),s(z,{class:"delete-btn",onClick:a=>He(e),title:"删除"},{default:p((()=>[s(l,null,{default:p((()=>[s(w(k))])),_:1})])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[aa,H.value]]),u("div",A,[s(ke,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":ce.page,"page-size":ce.size,total:ce.total,"page-sizes":[10,20,50,100],onSizeChange:je,onCurrentChange:Me},null,8,["current-page","page-size","total"])]),s(Je,{modelValue:ve.value,"onUpdate:modelValue":a[11]||(a[11]=e=>ve.value=e),title:"add"===ge.value?"添加备用金申请":"编辑备用金申请",width:"500px","destroy-on-close":"",class:"custom-dialog"},{default:p((()=>[s(Ee,{ref_key:"formRef",ref:he,model:Ie,rules:Ve,"label-width":"100px",class:"dialog-form"},{default:p((()=>[s(f,{label:"部门",prop:"departmentId",required:""},{default:p((()=>[s(n,{modelValue:de.value,"onUpdate:modelValue":a[4]||(a[4]=e=>de.value=e),placeholder:"请选择部门",style:{width:"100%"},onChange:Ke,loading:oe.value},{default:p((()=>[(y(!0),r(_,null,I(le.value,(e=>(y(),g(o,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),s(f,{label:"员工",prop:"employeeId",required:""},{default:p((()=>[s(n,{modelValue:Ie.employeeId,"onUpdate:modelValue":a[5]||(a[5]=e=>Ie.employeeId=e),placeholder:"请先选择部门再输入员工名称搜索",disabled:!de.value,onChange:Re,remote:"",filterable:"","remote-method":De,loading:ne.value,style:{width:"100%"}},{empty:p((()=>[u("p",D,Y(de.value?"请输入员工名称搜索":"请先选择部门"),1)])),default:p((()=>[(y(!0),r(_,null,I(te.value,(e=>(y(),g(o,{key:e.employeeId,label:e.name+(e.departmentName?` (${e.departmentName}${e.positionName?"-"+e.positionName:""})`:e.positionName?` (${e.positionName})`:""),value:e.employeeId},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled","loading"]),de.value||"add"!==ge.value?N("",!0):(y(),r("div",K))])),_:1}),s(f,{label:"年月",prop:"date",required:""},{default:p((()=>[s(d,{modelValue:Ie.date,"onUpdate:modelValue":a[6]||(a[6]=e=>Ie.date=e),type:"month",placeholder:"请选择年月",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),s(f,{label:"用途",prop:"purpose",required:""},{default:p((()=>[s(t,{modelValue:Ie.purpose,"onUpdate:modelValue":a[7]||(a[7]=e=>Ie.purpose=e),type:"textarea",rows:3,maxlength:200,"show-word-limit":"",placeholder:"请输入备用金用途",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),s(f,{label:"金额",prop:"amount",required:""},{default:p((()=>[s(Ce,{modelValue:Ie.amount,"onUpdate:modelValue":a[8]||(a[8]=e=>Ie.amount=e),precision:2,step:100,controls:!0,onChange:Ae,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),"edit"===ge.value?(y(),g(f,{key:0,label:"状态",prop:"status"},{default:p((()=>[s(n,{modelValue:Ie.status,"onUpdate:modelValue":a[9]||(a[9]=e=>Ie.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:p((()=>[(y(),r(_,null,I(_e,(e=>s(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1})):N("",!0)])),_:1},8,["model"]),u("div",R,[s(z,{onClick:Le},{default:p((()=>a[21]||(a[21]=[V("取消")]))),_:1}),s(z,{type:"primary",loading:fe.value,onClick:a[10]||(a[10]=e=>Ze(he.value))},{default:p((()=>a[22]||(a[22]=[V("确定")]))),_:1},8,["loading"])])])),_:1},8,["modelValue","title"]),s(Je,{modelValue:ye.value,"onUpdate:modelValue":a[14]||(a[14]=e=>ye.value=e),title:"审核备用金申请",width:"400px",class:"custom-dialog"},{default:p((()=>[s(Ee,{"label-width":"80px"},{default:p((()=>[s(f,{label:"审核结果"},{default:p((()=>[s(ea,{modelValue:be.value,"onUpdate:modelValue":a[12]||(a[12]=e=>be.value=e)},{default:p((()=>[s(Xe,{label:"已审核"},{default:p((()=>a[23]||(a[23]=[V("审核通过")]))),_:1}),s(Xe,{label:"已拒绝"},{default:p((()=>a[24]||(a[24]=[V("拒绝")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1}),u("div",W,[s(z,{onClick:a[13]||(a[13]=e=>ye.value=!1)},{default:p((()=>a[25]||(a[25]=[V("取消")]))),_:1}),s(z,{type:"primary",disabled:!be.value,onClick:Qe},{default:p((()=>a[26]||(a[26]=[V("确认")]))),_:1},8,["disabled"])])])),_:1},8,["modelValue"]),s(Je,{modelValue:ue.value,"onUpdate:modelValue":a[15]||(a[15]=e=>ue.value=e),title:"备用金审批",width:"90%",style:{maxWidth:"1200px"},"close-on-click-modal":!1,"append-to-body":"",onClosed:$e},{default:p((()=>[u("div",J,[s(z,{type:"success",icon:w($),onClick:Te,disabled:0===pe.value.length},{default:p((()=>a[27]||(a[27]=[V(" 批量通过 ")]))),_:1},8,["icon","disabled"]),s(z,{type:"danger",icon:w(U),onClick:Ue,disabled:0===pe.value.length},{default:p((()=>a[28]||(a[28]=[V(" 批量拒绝 ")]))),_:1},8,["icon","disabled"])]),i((y(),g(we,{data:se.value,border:"",class:"custom-table approval-table","max-height":"calc(70vh - 180px)",style:{width:"100%","margin-bottom":"0"},onSelectionChange:Ye,fit:""},{default:p((()=>[s(O,{type:"selection",width:"50",align:"center"}),s(O,{prop:"employeeName",label:"员工姓名","min-width":"100","show-overflow-tooltip":"",align:"center"}),s(O,{prop:"date",label:"年月","min-width":"100","show-overflow-tooltip":"",align:"center"}),s(O,{prop:"department",label:"部门","min-width":"120","show-overflow-tooltip":"",align:"center"}),s(O,{prop:"position",label:"职位","min-width":"120","show-overflow-tooltip":"",align:"center"}),s(O,{prop:"purpose",label:"用途","min-width":"150","show-overflow-tooltip":"",align:"center"}),s(O,{label:"金额","min-width":"120",align:"right"},{default:p((({row:e})=>[V(Y(Se(e.amount)),1)])),_:1}),s(O,{label:"创建时间","min-width":"160","show-overflow-tooltip":"",align:"center"},{default:p((({row:e})=>[V(Y(Pe(e.createTime)),1)])),_:1}),s(O,{label:"操作",width:"120",fixed:"right",align:"center"},{default:p((e=>[u("div",L,[s(z,{type:"success",link:"",icon:w($),onClick:a=>{return l=e.row,void B.confirm(`确定要通过员工 "${l.employeeName}" 的备用金申请(用途: ${l.purpose})吗？`,"审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"success"}).then((async()=>{ie.value=!0;try{const e=await M(l.id,"已审核");200===e.code?(h.success("已通过备用金申请"),se.value=se.value.filter((e=>e.id!==l.id)),xe()):h.error(e.message||"审批操作失败")}catch(e){h.error("审批操作失败: "+(e.message||"未知错误"))}finally{ie.value=!1}})).catch((()=>{}));var l}},{default:p((()=>a[29]||(a[29]=[V(" 通过 ")]))),_:2},1032,["icon","onClick"]),s(z,{type:"danger",link:"",icon:w(U),onClick:a=>{return l=e.row,void B.confirm(`确定要拒绝员工 "${l.employeeName}" 的备用金申请(用途: ${l.purpose})吗？`,"审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{ie.value=!0;try{const e=await M(l.id,"已拒绝");200===e.code?(h.success("已拒绝备用金申请"),se.value=se.value.filter((e=>e.id!==l.id)),xe()):h.error(e.message||"审批操作失败")}catch(e){h.error("审批操作失败: "+(e.message||"未知错误"))}finally{ie.value=!1}})).catch((()=>{}));var l}},{default:p((()=>a[30]||(a[30]=[V(" 拒绝 ")]))),_:2},1032,["icon","onClick"])])])),_:1})])),_:1},8,["data"])),[[aa,ie.value]])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-3ead1a24"]]);export{O as default};
