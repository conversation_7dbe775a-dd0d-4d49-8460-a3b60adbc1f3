import{q as e,E as t,_ as n,s as a,a as o,v as i,x as r,i as l,c as s,m as u,d,e as c,j as m,g as f,y as p,z as h,F as g,A as y,t as v,B as b,f as w,n as T,C as N,r as _,D as M,G as S,H as E,I as A,J as D,K as C,L as k,M as P,N as x,O as I,P as R,Q as O,R as L,S as U,T as W,o as z,u as F,k as H,U as B,V,W as j,X as Y}from"./index-CGqeVPF3.js";import{u as X}from"./token-Ced5ba2J.js";import{d as G,m as q,a as $,g as J,b as Z,c as K,l as Q,u as ee}from"./notification-DJy6sAcV.js";import"./request-Cm8Ap7dD.js";const te=e("notification",{state:()=>({popupNotifications:[],bellNotifications:[],hasUnreadBellMessage:!1,isLoadingPopup:!1,isLoadingBell:!1,useMockData:!1,shouldOpenCenterOnLoginIfUnread:!1}),actions:{async fetchPopupNotifications(){if(!this.isLoadingPopup){this.isLoadingPopup=!0;try{const e=await Z();e.data&&e.data.code}catch(e){}finally{this.isLoadingPopup=!1}}},async fetchBellNotifications(e=!1){if(!this.isLoadingBell){this.isLoadingBell=!0,this.shouldOpenCenterOnLoginIfUnread=!1;try{const n=await J();200===n.code?(this.bellNotifications=n.data||[],this.hasUnreadBellMessage=this.bellNotifications.some((e=>!e.isReadInBell&&"DISMISSED"!==e.status)),e&&this.hasUnreadBellMessage&&(this.shouldOpenCenterOnLoginIfUnread=!0)):(this.bellNotifications=[],this.hasUnreadBellMessage=!1,t.error(n.message||"获取铃铛通知失败[API]"))}catch(n){this.bellNotifications=[],this.hasUnreadBellMessage=!1,t.error("获取铃铛通知时发生网络错误[API]")}finally{this.isLoadingBell=!1}}},async handleDismissNotification(e){try{const n=await $(e);n&&200===n.code?(t.success(n.data||n.message||"通知已处理"),this.fetchBellNotifications()):t.error((null==n?void 0:n.message)||"处理通知失败[API]")}catch(n){t.error("处理通知时发生网络错误[API]")}},async handleMarkAsReadInBell(e){try{const n=await q(e);if(n&&200===n.code){const t=this.bellNotifications.findIndex((t=>t.notificationId===e));-1!==t&&(this.bellNotifications[t].isReadInBell=!0),this.hasUnreadBellMessage=this.bellNotifications.some((e=>!e.isReadInBell&&"DISMISSED"!==e.status))}else t.error((null==n?void 0:n.message)||"标记已读失败[API]")}catch(n){t.error("标记已读时发生网络错误[API]")}},clearNotifications(){this.popupNotifications=[],this.bellNotifications=[],this.hasUnreadBellMessage=!1},setOpenedCenterOnLogin(){this.shouldOpenCenterOnLoginIfUnread=!1},async handleDismissMultipleNotifications(e){try{const n=await G(e);n&&200===n.code?(t.success(n.data||n.message||"批量通知已处理"),this.fetchBellNotifications()):t.error((null==n?void 0:n.message)||"批量处理通知失败[API]")}catch(n){t.error("批量处理通知时发生网络错误[API]")}}},getters:{unreadBellNotificationCount:e=>e.bellNotifications.filter((e=>!e.isReadInBell&&"DISMISSED"!==e.status)).length}}),{entries:ne,setPrototypeOf:ae,isFrozen:oe,getPrototypeOf:ie,getOwnPropertyDescriptor:re}=Object;let{freeze:le,seal:se,create:ue}=Object,{apply:de,construct:ce}="undefined"!=typeof Reflect&&Reflect;le||(le=function(e){return e}),se||(se=function(e){return e}),de||(de=function(e,t,n){return e.apply(t,n)}),ce||(ce=function(e,t){return new e(...t)});const me=Ae(Array.prototype.forEach),fe=Ae(Array.prototype.lastIndexOf),pe=Ae(Array.prototype.pop),he=Ae(Array.prototype.push),ge=Ae(Array.prototype.splice),ye=Ae(String.prototype.toLowerCase),ve=Ae(String.prototype.toString),be=Ae(String.prototype.match),we=Ae(String.prototype.replace),Te=Ae(String.prototype.indexOf),Ne=Ae(String.prototype.trim),_e=Ae(Object.prototype.hasOwnProperty),Me=Ae(RegExp.prototype.test),Se=(Ee=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return ce(Ee,t)});var Ee;function Ae(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];return de(e,t,a)}}function De(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ye;ae&&ae(e,null);let a=t.length;for(;a--;){let o=t[a];if("string"==typeof o){const e=n(o);e!==o&&(oe(t)||(t[a]=e),o=e)}e[o]=!0}return e}function Ce(e){for(let t=0;t<e.length;t++){_e(e,t)||(e[t]=null)}return e}function ke(e){const t=ue(null);for(const[n,a]of ne(e)){_e(e,n)&&(Array.isArray(a)?t[n]=Ce(a):a&&"object"==typeof a&&a.constructor===Object?t[n]=ke(a):t[n]=a)}return t}function Pe(e,t){for(;null!==e;){const n=re(e,t);if(n){if(n.get)return Ae(n.get);if("function"==typeof n.value)return Ae(n.value)}e=ie(e)}return function(){return null}}const xe=le(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ie=le(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Re=le(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Oe=le(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Le=le(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Ue=le(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),We=le(["#text"]),ze=le(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Fe=le(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),He=le(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Be=le(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Ve=se(/\{\{[\w\W]*|[\w\W]*\}\}/gm),je=se(/<%[\w\W]*|[\w\W]*%>/gm),Ye=se(/\$\{[\w\W]*/gm),Xe=se(/^data-[\-\w.\u00B7-\uFFFF]+$/),Ge=se(/^aria-[\-\w]+$/),qe=se(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),$e=se(/^(?:\w+script|data):/i),Je=se(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Ze=se(/^html$/i),Ke=se(/^[a-z][.\w]*(-[.\w]+)+$/i);var Qe=Object.freeze({__proto__:null,ARIA_ATTR:Ge,ATTR_WHITESPACE:Je,CUSTOM_ELEMENT:Ke,DATA_ATTR:Xe,DOCTYPE_NAME:Ze,ERB_EXPR:je,IS_ALLOWED_URI:qe,IS_SCRIPT_OR_DATA:$e,MUSTACHE_EXPR:Ve,TMPLIT_EXPR:Ye});const et=1,tt=3,nt=7,at=8,ot=9;var it=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"undefined"==typeof window?null:window;const n=t=>e(t);if(n.version="3.2.6",n.removed=[],!t||!t.document||t.document.nodeType!==ot||!t.Element)return n.isSupported=!1,n;let{document:a}=t;const o=a,i=o.currentScript,{DocumentFragment:r,HTMLTemplateElement:l,Node:s,Element:u,NodeFilter:d,NamedNodeMap:c=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:f,trustedTypes:p}=t,h=u.prototype,g=Pe(h,"cloneNode"),y=Pe(h,"remove"),v=Pe(h,"nextSibling"),b=Pe(h,"childNodes"),w=Pe(h,"parentNode");if("function"==typeof l){const e=a.createElement("template");e.content&&e.content.ownerDocument&&(a=e.content.ownerDocument)}let T,N="";const{implementation:_,createNodeIterator:M,createDocumentFragment:S,getElementsByTagName:E}=a,{importNode:A}=o;let D={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof ne&&"function"==typeof w&&_&&void 0!==_.createHTMLDocument;const{MUSTACHE_EXPR:C,ERB_EXPR:k,TMPLIT_EXPR:P,DATA_ATTR:x,ARIA_ATTR:I,IS_SCRIPT_OR_DATA:R,ATTR_WHITESPACE:O,CUSTOM_ELEMENT:L}=Qe;let{IS_ALLOWED_URI:U}=Qe,W=null;const z=De({},[...xe,...Ie,...Re,...Le,...We]);let F=null;const H=De({},[...ze,...Fe,...He,...Be]);let B=Object.seal(ue(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),V=null,j=null,Y=!0,X=!0,G=!1,q=!0,$=!1,J=!0,Z=!1,K=!1,Q=!1,ee=!1,te=!1,ae=!1,oe=!0,ie=!1,re=!0,se=!1,de={},ce=null;const Ee=De({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ae=null;const Ce=De({},["audio","video","img","source","image","track"]);let Ve=null;const je=De({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ye="http://www.w3.org/1998/Math/MathML",Xe="http://www.w3.org/2000/svg",Ge="http://www.w3.org/1999/xhtml";let $e=Ge,Je=!1,Ke=null;const it=De({},[Ye,Xe,Ge],ve);let rt=De({},["mi","mo","mn","ms","mtext"]),lt=De({},["annotation-xml"]);const st=De({},["title","style","font","a","script"]);let ut=null;const dt=["application/xhtml+xml","text/html"];let ct=null,mt=null;const ft=a.createElement("form"),pt=function(e){return e instanceof RegExp||e instanceof Function},ht=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!mt||mt!==e){if(e&&"object"==typeof e||(e={}),e=ke(e),ut=-1===dt.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,ct="application/xhtml+xml"===ut?ve:ye,W=_e(e,"ALLOWED_TAGS")?De({},e.ALLOWED_TAGS,ct):z,F=_e(e,"ALLOWED_ATTR")?De({},e.ALLOWED_ATTR,ct):H,Ke=_e(e,"ALLOWED_NAMESPACES")?De({},e.ALLOWED_NAMESPACES,ve):it,Ve=_e(e,"ADD_URI_SAFE_ATTR")?De(ke(je),e.ADD_URI_SAFE_ATTR,ct):je,Ae=_e(e,"ADD_DATA_URI_TAGS")?De(ke(Ce),e.ADD_DATA_URI_TAGS,ct):Ce,ce=_e(e,"FORBID_CONTENTS")?De({},e.FORBID_CONTENTS,ct):Ee,V=_e(e,"FORBID_TAGS")?De({},e.FORBID_TAGS,ct):ke({}),j=_e(e,"FORBID_ATTR")?De({},e.FORBID_ATTR,ct):ke({}),de=!!_e(e,"USE_PROFILES")&&e.USE_PROFILES,Y=!1!==e.ALLOW_ARIA_ATTR,X=!1!==e.ALLOW_DATA_ATTR,G=e.ALLOW_UNKNOWN_PROTOCOLS||!1,q=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,$=e.SAFE_FOR_TEMPLATES||!1,J=!1!==e.SAFE_FOR_XML,Z=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,ae=e.RETURN_TRUSTED_TYPE||!1,Q=e.FORCE_BODY||!1,oe=!1!==e.SANITIZE_DOM,ie=e.SANITIZE_NAMED_PROPS||!1,re=!1!==e.KEEP_CONTENT,se=e.IN_PLACE||!1,U=e.ALLOWED_URI_REGEXP||qe,$e=e.NAMESPACE||Ge,rt=e.MATHML_TEXT_INTEGRATION_POINTS||rt,lt=e.HTML_INTEGRATION_POINTS||lt,B=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&pt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(B.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&pt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(B.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(B.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),$&&(X=!1),te&&(ee=!0),de&&(W=De({},We),F=[],!0===de.html&&(De(W,xe),De(F,ze)),!0===de.svg&&(De(W,Ie),De(F,Fe),De(F,Be)),!0===de.svgFilters&&(De(W,Re),De(F,Fe),De(F,Be)),!0===de.mathMl&&(De(W,Le),De(F,He),De(F,Be))),e.ADD_TAGS&&(W===z&&(W=ke(W)),De(W,e.ADD_TAGS,ct)),e.ADD_ATTR&&(F===H&&(F=ke(F)),De(F,e.ADD_ATTR,ct)),e.ADD_URI_SAFE_ATTR&&De(Ve,e.ADD_URI_SAFE_ATTR,ct),e.FORBID_CONTENTS&&(ce===Ee&&(ce=ke(ce)),De(ce,e.FORBID_CONTENTS,ct)),re&&(W["#text"]=!0),Z&&De(W,["html","head","body"]),W.table&&(De(W,["tbody"]),delete V.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw Se('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw Se('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');T=e.TRUSTED_TYPES_POLICY,N=T.createHTML("")}else void 0===T&&(T=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const a="data-tt-policy-suffix";t&&t.hasAttribute(a)&&(n=t.getAttribute(a));const o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML:e=>e,createScriptURL:e=>e})}catch(i){return null}}(p,i)),null!==T&&"string"==typeof N&&(N=T.createHTML(""));le&&le(e),mt=e}},gt=De({},[...Ie,...Re,...Oe]),yt=De({},[...Le,...Ue]),vt=function(e){he(n.removed,{element:e});try{w(e).removeChild(e)}catch(t){y(e)}},bt=function(e,t){try{he(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(a){he(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(ee||te)try{vt(t)}catch(a){}else try{t.setAttribute(e,"")}catch(a){}},wt=function(e){let t=null,n=null;if(Q)e="<remove></remove>"+e;else{const t=be(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===ut&&$e===Ge&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=T?T.createHTML(e):e;if($e===Ge)try{t=(new f).parseFromString(o,ut)}catch(r){}if(!t||!t.documentElement){t=_.createDocument($e,"template",null);try{t.documentElement.innerHTML=Je?N:o}catch(r){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(a.createTextNode(n),i.childNodes[0]||null),$e===Ge?E.call(t,Z?"html":"body")[0]:Z?t.documentElement:i},Tt=function(e){return M.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT|d.SHOW_PROCESSING_INSTRUCTION|d.SHOW_CDATA_SECTION,null)},Nt=function(e){return e instanceof m&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof c)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},_t=function(e){return"function"==typeof s&&e instanceof s};function Mt(e,t,a){me(e,(e=>{e.call(n,t,a,mt)}))}const St=function(e){let t=null;if(Mt(D.beforeSanitizeElements,e,null),Nt(e))return vt(e),!0;const a=ct(e.nodeName);if(Mt(D.uponSanitizeElement,e,{tagName:a,allowedTags:W}),J&&e.hasChildNodes()&&!_t(e.firstElementChild)&&Me(/<[/\w!]/g,e.innerHTML)&&Me(/<[/\w!]/g,e.textContent))return vt(e),!0;if(e.nodeType===nt)return vt(e),!0;if(J&&e.nodeType===at&&Me(/<[/\w]/g,e.data))return vt(e),!0;if(!W[a]||V[a]){if(!V[a]&&At(a)){if(B.tagNameCheck instanceof RegExp&&Me(B.tagNameCheck,a))return!1;if(B.tagNameCheck instanceof Function&&B.tagNameCheck(a))return!1}if(re&&!ce[a]){const t=w(e)||e.parentNode,n=b(e)||e.childNodes;if(n&&t){for(let a=n.length-1;a>=0;--a){const o=g(n[a],!0);o.__removalCount=(e.__removalCount||0)+1,t.insertBefore(o,v(e))}}}return vt(e),!0}return e instanceof u&&!function(e){let t=w(e);t&&t.tagName||(t={namespaceURI:$e,tagName:"template"});const n=ye(e.tagName),a=ye(t.tagName);return!!Ke[e.namespaceURI]&&(e.namespaceURI===Xe?t.namespaceURI===Ge?"svg"===n:t.namespaceURI===Ye?"svg"===n&&("annotation-xml"===a||rt[a]):Boolean(gt[n]):e.namespaceURI===Ye?t.namespaceURI===Ge?"math"===n:t.namespaceURI===Xe?"math"===n&&lt[a]:Boolean(yt[n]):e.namespaceURI===Ge?!(t.namespaceURI===Xe&&!lt[a])&&!(t.namespaceURI===Ye&&!rt[a])&&!yt[n]&&(st[n]||!gt[n]):!("application/xhtml+xml"!==ut||!Ke[e.namespaceURI]))}(e)?(vt(e),!0):"noscript"!==a&&"noembed"!==a&&"noframes"!==a||!Me(/<\/no(script|embed|frames)/i,e.innerHTML)?($&&e.nodeType===tt&&(t=e.textContent,me([C,k,P],(e=>{t=we(t,e," ")})),e.textContent!==t&&(he(n.removed,{element:e.cloneNode()}),e.textContent=t)),Mt(D.afterSanitizeElements,e,null),!1):(vt(e),!0)},Et=function(e,t,n){if(oe&&("id"===t||"name"===t)&&(n in a||n in ft))return!1;if(X&&!j[t]&&Me(x,t));else if(Y&&Me(I,t));else if(!F[t]||j[t]){if(!(At(e)&&(B.tagNameCheck instanceof RegExp&&Me(B.tagNameCheck,e)||B.tagNameCheck instanceof Function&&B.tagNameCheck(e))&&(B.attributeNameCheck instanceof RegExp&&Me(B.attributeNameCheck,t)||B.attributeNameCheck instanceof Function&&B.attributeNameCheck(t))||"is"===t&&B.allowCustomizedBuiltInElements&&(B.tagNameCheck instanceof RegExp&&Me(B.tagNameCheck,n)||B.tagNameCheck instanceof Function&&B.tagNameCheck(n))))return!1}else if(Ve[t]);else if(Me(U,we(n,O,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==Te(n,"data:")||!Ae[e]){if(G&&!Me(R,we(n,O,"")));else if(n)return!1}else;return!0},At=function(e){return"annotation-xml"!==e&&be(e,L)},Dt=function(e){Mt(D.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Nt(e))return;const a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:F,forceKeepAttr:void 0};let o=t.length;for(;o--;){const r=t[o],{name:l,namespaceURI:s,value:u}=r,d=ct(l),c=u;let m="value"===l?c:Ne(c);if(a.attrName=d,a.attrValue=m,a.keepAttr=!0,a.forceKeepAttr=void 0,Mt(D.uponSanitizeAttribute,e,a),m=a.attrValue,!ie||"id"!==d&&"name"!==d||(bt(l,e),m="user-content-"+m),J&&Me(/((--!?|])>)|<\/(style|title)/i,m)){bt(l,e);continue}if(a.forceKeepAttr)continue;if(!a.keepAttr){bt(l,e);continue}if(!q&&Me(/\/>/i,m)){bt(l,e);continue}$&&me([C,k,P],(e=>{m=we(m,e," ")}));const f=ct(e.nodeName);if(Et(f,d,m)){if(T&&"object"==typeof p&&"function"==typeof p.getAttributeType)if(s);else switch(p.getAttributeType(f,d)){case"TrustedHTML":m=T.createHTML(m);break;case"TrustedScriptURL":m=T.createScriptURL(m)}if(m!==c)try{s?e.setAttributeNS(s,l,m):e.setAttribute(l,m),Nt(e)?vt(e):pe(n.removed)}catch(i){bt(l,e)}}else bt(l,e)}Mt(D.afterSanitizeAttributes,e,null)},Ct=function e(t){let n=null;const a=Tt(t);for(Mt(D.beforeSanitizeShadowDOM,t,null);n=a.nextNode();)Mt(D.uponSanitizeShadowNode,n,null),St(n),Dt(n),n.content instanceof r&&e(n.content);Mt(D.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=null,i=null,l=null,u=null;if(Je=!e,Je&&(e="\x3c!--\x3e"),"string"!=typeof e&&!_t(e)){if("function"!=typeof e.toString)throw Se("toString is not a function");if("string"!=typeof(e=e.toString()))throw Se("dirty is not a string, aborting")}if(!n.isSupported)return e;if(K||ht(t),n.removed=[],"string"==typeof e&&(se=!1),se){if(e.nodeName){const t=ct(e.nodeName);if(!W[t]||V[t])throw Se("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof s)a=wt("\x3c!----\x3e"),i=a.ownerDocument.importNode(e,!0),i.nodeType===et&&"BODY"===i.nodeName||"HTML"===i.nodeName?a=i:a.appendChild(i);else{if(!ee&&!$&&!Z&&-1===e.indexOf("<"))return T&&ae?T.createHTML(e):e;if(a=wt(e),!a)return ee?null:ae?N:""}a&&Q&&vt(a.firstChild);const d=Tt(se?e:a);for(;l=d.nextNode();)St(l),Dt(l),l.content instanceof r&&Ct(l.content);if(se)return e;if(ee){if(te)for(u=S.call(a.ownerDocument);a.firstChild;)u.appendChild(a.firstChild);else u=a;return(F.shadowroot||F.shadowrootmode)&&(u=A.call(o,u,!0)),u}let c=Z?a.outerHTML:a.innerHTML;return Z&&W["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&Me(Ze,a.ownerDocument.doctype.name)&&(c="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+c),$&&me([C,k,P],(e=>{c=we(c,e," ")})),T&&ae?T.createHTML(c):c},n.setConfig=function(){ht(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),K=!0},n.clearConfig=function(){mt=null,K=!1},n.isValidAttribute=function(e,t,n){mt||ht({});const a=ct(e),o=ct(t);return Et(a,o,n)},n.addHook=function(e,t){"function"==typeof t&&he(D[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=fe(D[e],t);return-1===n?void 0:ge(D[e],n,1)[0]}return pe(D[e])},n.removeHooks=function(e){D[e]=[]},n.removeAllHooks=function(){D={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}();const rt=6e4,lt=36e5,st=43200,ut=Symbol.for("constructDateFrom");function dt(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&ut in e?e[ut](t):e instanceof Date?new e.constructor(t):new Date(t)}function ct(e,t){return dt(t||e,e)}let mt={};function ft(){return mt}function pt(e,t){var n,a,o,i;const r=ft(),l=(null==t?void 0:t.weekStartsOn)??(null==(a=null==(n=null==t?void 0:t.locale)?void 0:n.options)?void 0:a.weekStartsOn)??r.weekStartsOn??(null==(i=null==(o=r.locale)?void 0:o.options)?void 0:i.weekStartsOn)??0,s=ct(e,null==t?void 0:t.in),u=s.getDay(),d=(u<l?7:0)+u-l;return s.setDate(s.getDate()-d),s.setHours(0,0,0,0),s}function ht(e){const t=ct(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function gt(e,...t){const n=dt.bind(null,e||t.find((e=>"object"==typeof e)));return t.map(n)}function yt(e,t){const n=+ct(e)-+ct(t);return n<0?-1:n>0?1:n}function vt(e,t){const n=ct(e,null==t?void 0:t.in);return+function(e,t){const n=ct(e,null==t?void 0:t.in);return n.setHours(23,59,59,999),n}(n,t)==+function(e,t){const n=ct(e,null==t?void 0:t.in),a=n.getMonth();return n.setFullYear(n.getFullYear(),a+1,0),n.setHours(23,59,59,999),n}(n,t)}function bt(e,t,n){const[a,o,i]=gt(null==n?void 0:n.in,e,e,t),r=yt(o,i),l=Math.abs(function(e,t,n){const[a,o]=gt(null==n?void 0:n.in,e,t);return 12*(a.getFullYear()-o.getFullYear())+(a.getMonth()-o.getMonth())}(o,i));if(l<1)return 0;1===o.getMonth()&&o.getDate()>27&&o.setDate(30),o.setMonth(o.getMonth()-r*l);let s=yt(o,i)===-r;vt(a)&&1===l&&1===yt(a,i)&&(s=!1);const u=r*(l-+s);return 0===u?0:u}function wt(e,t,n){const a=function(e,t){return+ct(e)-+ct(t)}(e,t)/1e3;return(o=null==n?void 0:n.roundingMethod,e=>{const t=(o?Math[o]:Math.trunc)(e);return 0===t?0:t})(a);var o}const Tt={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function Nt(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const _t={date:Nt({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:Nt({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:Nt({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Mt={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function St(e){return(t,n)=>{let a;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&e.formattingValues){const t=e.defaultFormattingWidth||e.defaultWidth,o=(null==n?void 0:n.width)?String(n.width):t;a=e.formattingValues[o]||e.formattingValues[t]}else{const t=e.defaultWidth,o=(null==n?void 0:n.width)?String(n.width):e.defaultWidth;a=e.values[o]||e.values[t]}return a[e.argumentCallback?e.argumentCallback(t):t]}}function Et(e){return(t,n={})=>{const a=n.width,o=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],i=t.match(o);if(!i)return null;const r=i[0],l=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],s=Array.isArray(l)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n;return}(l,(e=>e.test(r))):function(e,t){for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n;return}(l,(e=>e.test(r)));let u;u=e.valueCallback?e.valueCallback(s):s,u=n.valueCallback?n.valueCallback(u):u;return{value:u,rest:t.slice(r.length)}}}function At(e){return(t,n={})=>{const a=t.match(e.matchPattern);if(!a)return null;const o=a[0],i=t.match(e.parsePattern);if(!i)return null;let r=e.valueCallback?e.valueCallback(i[0]):i[0];r=n.valueCallback?n.valueCallback(r):r;return{value:r,rest:t.slice(o.length)}}}const Dt={code:"en-US",formatDistance:(e,t,n)=>{let a;const o=Tt[e];return a="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",t.toString()),(null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"in "+a:a+" ago":a},formatLong:_t,formatRelative:(e,t,n,a)=>Mt[e],localize:{ordinalNumber:(e,t)=>{const n=Number(e),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:St({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:St({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:St({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:St({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:St({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:At({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:Et({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:Et({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:Et({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Et({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Et({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function Ct(e,t){return function(e,t,n){const a=ft(),o=(null==n?void 0:n.locale)??a.locale??Dt,i=yt(e,t);if(isNaN(i))throw new RangeError("Invalid time value");const r=Object.assign({},n,{addSuffix:null==n?void 0:n.addSuffix,comparison:i}),[l,s]=gt(null==n?void 0:n.in,...i>0?[t,e]:[e,t]),u=wt(s,l),d=(ht(s)-ht(l))/1e3,c=Math.round((u-d)/60);let m;if(c<2)return(null==n?void 0:n.includeSeconds)?u<5?o.formatDistance("lessThanXSeconds",5,r):u<10?o.formatDistance("lessThanXSeconds",10,r):u<20?o.formatDistance("lessThanXSeconds",20,r):u<40?o.formatDistance("halfAMinute",0,r):u<60?o.formatDistance("lessThanXMinutes",1,r):o.formatDistance("xMinutes",1,r):0===c?o.formatDistance("lessThanXMinutes",1,r):o.formatDistance("xMinutes",c,r);if(c<45)return o.formatDistance("xMinutes",c,r);if(c<90)return o.formatDistance("aboutXHours",1,r);if(c<1440){const e=Math.round(c/60);return o.formatDistance("aboutXHours",e,r)}if(c<2520)return o.formatDistance("xDays",1,r);if(c<st){const e=Math.round(c/1440);return o.formatDistance("xDays",e,r)}if(c<86400)return m=Math.round(c/st),o.formatDistance("aboutXMonths",m,r);if(m=bt(s,l),m<12){const e=Math.round(c/st);return o.formatDistance("xMonths",e,r)}{const e=m%12,t=Math.trunc(m/12);return e<3?o.formatDistance("aboutXYears",t,r):e<9?o.formatDistance("overXYears",t,r):o.formatDistance("almostXYears",t+1,r)}}(e,function(e){return dt(e,Date.now())}(e),t)}function kt(e,t){const n=()=>dt(null==t?void 0:t.in,NaN),a=function(e){const t={},n=e.split(Pt.dateTimeDelimiter);let a;if(n.length>2)return t;/:/.test(n[0])?a=n[0]:(t.date=n[0],a=n[1],Pt.timeZoneDelimiter.test(t.date)&&(t.date=e.split(Pt.timeZoneDelimiter)[0],a=e.substr(t.date.length,e.length)));if(a){const e=Pt.timezone.exec(a);e?(t.time=a.replace(e[1],""),t.timezone=e[1]):t.time=a}return t}(e);let o;if(a.date){const e=function(e,t){const n=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),a=e.match(n);if(!a)return{year:NaN,restDateString:""};const o=a[1]?parseInt(a[1]):null,i=a[2]?parseInt(a[2]):null;return{year:null===i?o:100*i,restDateString:e.slice((a[1]||a[2]).length)}}(a.date,2);o=function(e,t){if(null===t)return new Date(NaN);const n=e.match(xt);if(!n)return new Date(NaN);const a=!!n[4],o=Ot(n[1]),i=Ot(n[2])-1,r=Ot(n[3]),l=Ot(n[4]),s=Ot(n[5])-1;if(a)return function(e,t,n){return t>=1&&t<=53&&n>=0&&n<=6}(0,l,s)?function(e,t,n){const a=new Date(0);a.setUTCFullYear(e,0,4);const o=a.getUTCDay()||7,i=7*(t-1)+n+1-o;return a.setUTCDate(a.getUTCDate()+i),a}(t,l,s):new Date(NaN);{const e=new Date(0);return function(e,t,n){return t>=0&&t<=11&&n>=1&&n<=(Ut[t]||(Wt(e)?29:28))}(t,i,r)&&function(e,t){return t>=1&&t<=(Wt(e)?366:365)}(t,o)?(e.setUTCFullYear(t,i,Math.max(o,r)),e):new Date(NaN)}}(e.restDateString,e.year)}if(!o||isNaN(+o))return n();const i=+o;let r,l=0;if(a.time&&(l=function(e){const t=e.match(It);if(!t)return NaN;const n=Lt(t[1]),a=Lt(t[2]),o=Lt(t[3]);if(!function(e,t,n){if(24===e)return 0===t&&0===n;return n>=0&&n<60&&t>=0&&t<60&&e>=0&&e<25}(n,a,o))return NaN;return n*lt+a*rt+1e3*o}(a.time),isNaN(l)))return n();if(!a.timezone){const e=new Date(i+l),n=ct(0,null==t?void 0:t.in);return n.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),n.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),n}return r=function(e){if("Z"===e)return 0;const t=e.match(Rt);if(!t)return 0;const n="+"===t[1]?-1:1,a=parseInt(t[2]),o=t[3]&&parseInt(t[3])||0;if(!function(e,t){return t>=0&&t<=59}(0,o))return NaN;return n*(a*lt+o*rt)}(a.timezone),isNaN(r)?n():ct(i+l+r,null==t?void 0:t.in)}const Pt={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},xt=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,It=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,Rt=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Ot(e){return e?parseInt(e):1}function Lt(e){return e&&parseFloat(e.replace(",","."))||0}const Ut=[31,null,31,30,31,30,31,31,30,31,30,31];function Wt(e){return e%400==0||e%4==0&&e%100!=0}const zt={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}},Ft={date:Nt({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:Nt({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:Nt({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};function Ht(e,t,n){const a="eeee p";return function(e,t,n){const[a,o]=gt(null==n?void 0:n.in,e,t);return+pt(a,n)==+pt(o,n)}(e,t,n)?a:e.getTime()>t.getTime()?"'下个'"+a:"'上个'"+a}const Bt={lastWeek:Ht,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:Ht,other:"PP p"},Vt={code:"zh-CN",formatDistance:(e,t,n)=>{let a;const o=zt[e];return a="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",String(t)),(null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?a+"内":a+"前":a},formatLong:Ft,formatRelative:(e,t,n,a)=>{const o=Bt[e];return"function"==typeof o?o(t,n,a):o},localize:{ordinalNumber:(e,t)=>{const n=Number(e);switch(null==t?void 0:t.unit){case"date":return n.toString()+"日";case"hour":return n.toString()+"时";case"minute":return n.toString()+"分";case"second":return n.toString()+"秒";default:return"第 "+n.toString()}},era:St({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:St({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:St({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:St({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:St({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:At({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:Et({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:Et({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:Et({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:Et({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:Et({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}},jt={key:0,class:"empty-notifications-dialog"},Yt=["onClick"],Xt={class:"notification-title-dialog"},Gt=["innerHTML"],qt={class:"notification-time-dialog"},$t={key:0,class:"empty-notifications-dialog"},Jt=["onClick"],Zt={class:"notification-title-dialog"},Kt=["innerHTML"],Qt={class:"notification-time-dialog"},en={key:0,class:"notification-detail-content"},tn=["innerHTML"],nn={class:"time-info"},an={key:0,class:"status-info"},on={key:1,class:"status-info"},rn=n({__name:"NotificationBell",setup(e){a();const t=te(),n=o(!1),_=o("unread"),M=o(!1),S=o(null);i((()=>t.shouldOpenCenterOnLoginIfUnread),(e=>{e&&(n.value=!0,_.value="unread",t.setOpenedCenterOnLogin())}));const E=()=>{n.value=!0},A=r((()=>t.bellNotifications.filter((e=>!e.isReadInBell&&"ARCHIVED"!==e.status)).sort(((e,t)=>new Date(t.generatedAt)-new Date(e.generatedAt))))),D=r((()=>[...t.bellNotifications].sort(((e,t)=>new Date(t.generatedAt)-new Date(e.generatedAt))))),C=e=>it.sanitize(e,{USE_PROFILES:{html:!0}}),k=e=>{if(!e)return"";try{return Ct(kt(e),{addSuffix:!0,locale:Vt})}catch(t){return e}},P=e=>{S.value=e,M.value=!0},x=()=>{S.value&&t.handleDismissNotification(S.value.notificationId),M.value=!1},I=()=>{const e=A.value.map((e=>e.notificationId));e.length>0&&t.handleDismissMultipleNotifications(e)};return(e,a)=>{var o;const i=l("el-icon"),r=l("el-badge"),R=l("el-scrollbar"),O=l("el-tab-pane"),L=l("el-tag"),U=l("el-tabs"),W=l("el-button"),z=l("el-dialog");return u(),s("div",{class:b(e.$attrs.class)},[d("span",{onClick:E,class:"el-dropdown-link notification-bell-trigger"},[c(r,{"is-dot":f(t).hasUnreadBellMessage,class:"item"},{default:m((()=>[c(i,{size:22},{default:m((()=>[c(f(p))])),_:1})])),_:1},8,["is-dot"])]),c(z,{modelValue:n.value,"onUpdate:modelValue":a[2]||(a[2]=e=>n.value=e),title:"通知中心",width:"600px",top:"10vh","append-to-body":"","custom-class":"notification-center-dialog","close-on-click-modal":!1},{footer:m((()=>[A.value.length>0&&"unread"===_.value?(u(),h(W,{key:0,onClick:I},{default:m((()=>[c(i,null,{default:m((()=>[c(f(N))])),_:1}),a[6]||(a[6]=T(" 全部标记为已读 "))])),_:1})):w("",!0),c(W,{onClick:a[1]||(a[1]=e=>n.value=!1)},{default:m((()=>a[7]||(a[7]=[T("关闭")]))),_:1})])),default:m((()=>[c(U,{modelValue:_.value,"onUpdate:modelValue":a[0]||(a[0]=e=>_.value=e),class:"notification-tabs-dialog"},{default:m((()=>[c(O,{label:"未读消息",name:"unread"},{default:m((()=>[0===A.value.length?(u(),s("div",jt," 暂无未读消息 ")):(u(),h(R,{key:1,"max-height":"calc(80vh - 200px)"},{default:m((()=>[(u(!0),s(g,null,y(A.value,(e=>(u(),s("div",{key:e.notificationId+"-unread",onClick:()=>P(e),class:"notification-item-dialog"},[d("div",Xt,v(e.titleCn||"系统提醒"),1),d("div",{class:"notification-message-dialog",innerHTML:C(e.messageCn)},null,8,Gt),d("div",qt,v(k(e.generatedAt)),1)],8,Yt)))),128))])),_:1}))])),_:1}),c(O,{label:"全部消息",name:"all"},{default:m((()=>[0===D.value.length?(u(),s("div",$t," 暂无任何消息 ")):(u(),h(R,{key:1,"max-height":"calc(80vh - 200px)"},{default:m((()=>[(u(!0),s(g,null,y(D.value,(e=>(u(),s("div",{key:e.notificationId+"-all",onClick:()=>P(e),class:b(["notification-item-dialog",{"is-read-in-dialog":e.isReadInBell}])},[d("div",Zt,v(e.titleCn||"系统提醒"),1),d("div",{class:"notification-message-dialog",innerHTML:C(e.messageCn)},null,8,Kt),d("div",Qt,v(k(e.generatedAt)),1),e.isReadInBell?(u(),h(L,{key:0,size:"small",type:"info",effect:"plain",class:"read-status-tag-dialog"},{default:m((()=>a[5]||(a[5]=[T("已读")]))),_:1})):w("",!0)],10,Jt)))),128))])),_:1}))])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["modelValue"]),c(z,{modelValue:M.value,"onUpdate:modelValue":a[4]||(a[4]=e=>M.value=e),title:(null==(o=S.value)?void 0:o.titleCn)||"通知详情",width:"500px","append-to-body":"","custom-class":"notification-detail-sub-dialog"},{footer:m((()=>[c(W,{onClick:a[3]||(a[3]=e=>M.value=!1)},{default:m((()=>a[8]||(a[8]=[T("关闭")]))),_:1}),S.value&&!S.value.isReadInBell?(u(),h(W,{key:0,type:"primary",onClick:x},{default:m((()=>a[9]||(a[9]=[T(" 我知道了 ")]))),_:1})):w("",!0)])),default:m((()=>[S.value?(u(),s("div",en,[d("p",{innerHTML:C(S.value.messageCn)},null,8,tn),d("p",nn,"接收时间: "+v(k(S.value.generatedAt)),1),S.value.isReadInBell?(u(),s("p",an,"状态: 已读")):(u(),s("p",on,"状态: 未读"))])):w("",!0)])),_:1},8,["modelValue","title"])],2)}}},[["__scopeId","data-v-beb7c151"]]),ln={class:"app-container"},sn={class:"logo-container"},un={key:0,src:"/assets/logo-FcOHnPPD.jpg",class:"logo-img",alt:"Logo"},dn={key:1,class:"logo-text-collapsed"},cn={class:"sidebar-footer"},mn={class:"header-left"},fn={class:"header-right"},pn={class:"user-info"},hn={class:"user-name"},gn={class:"dialog-footer"},yn=n({__name:"Dashboard",setup(e){const n=F(),a=H(),p=X(),N=te(),G=o(null),q=o(!1),$=o(""),J=o(null),Z=o([]),ne=o(!1),ae=o(!1),oe=_({name:"",email:"",phone:"",idCard:"",oldPassword:"",newPassword:"",confirmPassword:"",departmentName:"",positionName:"",entryDate:"",status:"",logisticsRoute:""}),ie={name:[{required:!0,message:"请输入姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱格式",trigger:["blur","change"]}],oldPassword:[{validator:(e,t,n)=>{oe.newPassword&&!t?n(new Error("修改密码时需要输入原密码")):t&&!oe.newPassword?n(new Error("请输入新密码")):n()},trigger:"blur"}],newPassword:[{validator:(e,t,n)=>{var a;if(oe.oldPassword&&!t)n(new Error("请输入新密码"));else if(t){if(t.length<8)return void n(new Error("密码长度至少为8位"));const e=/[A-Z]/.test(t),a=/[a-z]/.test(t),o=/\d/.test(t);if(!e||!a||!o)return void n(new Error("密码必须包含大小写字母和数字"));if(!oe.oldPassword)return void n(new Error("请先输入原密码"))}oe.confirmPassword&&(null==(a=J.value)||a.validateField("confirmPassword")),n()},trigger:"blur"}],confirmPassword:[{validator:(e,t,n)=>{oe.newPassword&&!t?n(new Error("请确认新密码")):t!==oe.newPassword?n(new Error("两次输入的密码不一致")):n()},trigger:"blur"}]},re=[{id:"home",title:"首页",icon:M(S),path:"/dashboard/home",isMenuItem:!0},{id:"client",title:"客户管理",icon:M(E),path:"/dashboard/client",isMenuItem:!0},{id:"salesReport",title:"我的日报",icon:M(A),path:"/dashboard/sales-report",isMenuItem:!0},{id:"pettyCash",title:"备用金管理",icon:M(D),path:"/dashboard/petty-cash",isMenuItem:!0},{id:"performance",title:"业绩查询",icon:M(C),path:"/dashboard/performance",isMenuItem:!0},{id:"salary",title:"工资查询",icon:M(k),path:"/dashboard/salary",isMenuItem:!0}],le={admin:["home","client","salesReport","departmentReports","pettyCash","performance","salary","departmentEmployees","departmentPerformance","departmentSalary","departmentClients","departmentPettyCash","departmentExpense","employeeExpense"],manager:["home","client","salesReport","departmentReports","pettyCash","performance","salary","departmentEmployees","departmentPerformance","departmentSalary","departmentClients","departmentPettyCash","departmentExpense","employeeExpense"],employee:["home","client","salesReport","pettyCash","performance","salary"]},se={id:"departmentEmployees",title:"部门员工",icon:M(P),path:"/dashboard/department-employees",isMenuItem:!0},ue={id:"departmentPerformance",title:"部门业绩",icon:M(x),path:"/dashboard/department-performance",isMenuItem:!0},de={id:"departmentSalary",title:"部门工资",icon:M(I),path:"/dashboard/department-salary",isMenuItem:!0},ce={id:"departmentClients",title:"部门客户",icon:M(R),path:"/dashboard/department-clients",isMenuItem:!0},me={id:"departmentReports",title:"部门日报",icon:M(O),path:"/dashboard/department-reports",isMenuItem:!0},fe={id:"departmentPettyCash",title:"部门备用金",icon:M(L),path:"/dashboard/department-petty-cash",isMenuItem:!0},pe={id:"departmentExpense",title:"部门开销",icon:M(U),path:"/dashboard/department-expense",isMenuItem:!0},he={id:"employeeExpense",title:"员工费用",icon:M(W),path:"/dashboard/employee-expense",isMenuItem:!0},ge=r((()=>{var e,t,n,a,o,i,r,l,s;const u=[...re],d=(null==(e=G.value)?void 0:e.role)||"employee";return(null==(t=le[d])?void 0:t.includes("departmentEmployees"))&&u.push(se),(null==(n=le[d])?void 0:n.includes("departmentPerformance"))&&u.push(ue),(null==(a=le[d])?void 0:a.includes("departmentSalary"))&&u.push(de),(null==(o=le[d])?void 0:o.includes("departmentClients"))&&u.push(ce),(null==(i=le[d])?void 0:i.includes("departmentReports"))&&u.push(me),(null==(r=le[d])?void 0:r.includes("departmentPettyCash"))&&u.push(fe),(null==(l=le[d])?void 0:l.includes("departmentExpense"))&&u.push(pe),(null==(s=le[d])?void 0:s.includes("employeeExpense"))&&u.push(he),u})),ye=r((()=>{const e=[];return ge.value.forEach((t=>{e.push(t)})),e})),ve=()=>{const e=a.path,t=ye.value.find((t=>t.path===e));t?($.value=t.id,Z.value=[],Z.value.push({title:t.title,path:t.path})):($.value="home",Z.value=[{title:"首页",path:"/dashboard/home"}])};i((()=>a.path),(()=>{ve()}),{immediate:!0});const be=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})},we=async(e=!1)=>{try{const t=await K();200===t.code&&(G.value=t.data,p.setUser(t.data),Object.assign(oe,{name:G.value.name,email:G.value.email,phone:G.value.phone,idCard:G.value.idCard,oldPassword:"",newPassword:"",confirmPassword:"",departmentName:G.value.departmentName,positionName:G.value.positionName,entryDate:be(G.value.entryDate),status:"Active"===G.value.status?"在职":"离职",logisticsRoute:G.value.logisticsRoute}),G.value&&N.fetchBellNotifications(e))}catch(t){}},Te=()=>{ae.value=!ae.value,ae.value||Object.assign(oe,{name:G.value.name,email:G.value.email,phone:G.value.phone,idCard:G.value.idCard,oldPassword:"",newPassword:"",confirmPassword:"",departmentName:G.value.departmentName,positionName:G.value.positionName,entryDate:be(G.value.entryDate),status:"Active"===G.value.status?"在职":"离职",logisticsRoute:G.value.logisticsRoute})},Ne=()=>{q.value=!q.value};return z((async()=>{var e;p.isAuthenticated?(await we(!0),ve(),setTimeout((()=>{p.isAuthenticated&&N.fetchBellNotifications(!1)}),7e3),a.query.fromLogin&&G.value&&t({message:`欢迎回来，${(null==(e=G.value)?void 0:e.name)||"员工"}！`,type:"success",duration:3e3})):n.push("/login")})),(e,a)=>{const o=l("el-icon"),i=l("el-menu-item"),r=l("el-menu"),_=l("el-scrollbar"),M=l("el-aside"),S=l("el-breadcrumb-item"),A=l("el-breadcrumb"),D=l("el-avatar"),C=l("el-dropdown-item"),P=l("el-dropdown-menu"),x=l("el-dropdown"),I=l("el-header"),R=l("router-view"),O=l("el-main"),L=l("el-container"),U=l("el-input"),W=l("el-form-item"),z=l("el-divider"),F=l("el-form"),H=l("el-button"),X=l("el-dialog");return u(),s("div",ln,[c(L,{class:"layout-container"},{default:m((()=>[c(M,{width:q.value?"64px":"200px",class:"sidebar"},{default:m((()=>[d("div",sn,[d("div",{class:b(["logo-content",{collapsed:q.value}])},[q.value?w("",!0):(u(),s("img",un)),q.value?(u(),s("span",dn,"中航")):w("",!0)],2)]),c(_,{class:"sidebar-menu-container"},{default:m((()=>[c(r,{"default-active":$.value,class:"sidebar-menu",collapse:q.value,"collapse-transition":!1,"background-color":"#001529","text-color":"#fff","active-text-color":"#409EFF"},{default:m((()=>[(u(!0),s(g,null,y(ge.value,(e=>(u(),h(i,{key:e.id,index:e.id,onClick:t=>{return a=e.path,void n.push(a);var a},class:"menu-item-with-transition"},{title:m((()=>[d("span",null,v(e.title),1)])),default:m((()=>[c(o,null,{default:m((()=>[(u(),h(B(e.icon)))])),_:2},1024)])),_:2},1032,["index","onClick"])))),128))])),_:1},8,["default-active","collapse"])])),_:1}),d("div",cn,[c(o,{class:"toggle-button",onClick:Ne},{default:m((()=>[c(f(V))])),_:1})])])),_:1},8,["width"]),c(L,{class:"main-container"},{default:m((()=>[c(I,{class:"main-header"},{default:m((()=>[d("div",mn,[c(A,{separator:"/"},{default:m((()=>[(u(!0),s(g,null,y(Z.value,((e,t)=>(u(),h(S,{key:t,to:e.path?{path:e.path}:null},{default:m((()=>[T(v(e.title),1)])),_:2},1032,["to"])))),128))])),_:1})]),d("div",fn,[c(rn,{class:"header-action-item"}),c(x,{trigger:"hover",onCommand:a[0]||(a[0]=e=>"logout"===e?(async()=>{try{await Y.confirm("确定要退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),N.clearNotifications(),await Q(),p.clearAuth(),t.success("已成功退出登录"),n.push("/login")}catch(e){}})():(async()=>{await we(),ne.value=!0,ae.value=!1})())},{dropdown:m((()=>[c(P,null,{default:m((()=>[c(C,{command:"profile"},{default:m((()=>[c(o,null,{default:m((()=>[c(f(E))])),_:1}),a[17]||(a[17]=T(" 个人信息 "))])),_:1}),c(C,{command:"logout"},{default:m((()=>[c(o,null,{default:m((()=>[c(f(k))])),_:1}),a[18]||(a[18]=T(" 退出登录 "))])),_:1})])),_:1})])),default:m((()=>{var e,t,n;return[d("div",pn,[c(D,{size:32,src:(null==(e=G.value)?void 0:e.avatar)||"https://ui-avatars.com/api/?name="+(null==(t=G.value)?void 0:t.name)+"&background=random",class:"user-avatar"},null,8,["src"]),d("span",hn,v((null==(n=G.value)?void 0:n.name)||"员工"),1),c(o,{class:"dropdown-icon"},{default:m((()=>[c(f(j))])),_:1})])]})),_:1})])])),_:1}),c(O,{class:"main-content"},{default:m((()=>[c(R)])),_:1})])),_:1})])),_:1}),c(X,{modelValue:ne.value,"onUpdate:modelValue":a[15]||(a[15]=e=>ne.value=e),title:ae.value?"编辑个人信息":"个人信息",width:"500px","close-on-click-modal":!1,onClosed:a[16]||(a[16]=e=>ae.value=!1)},{footer:m((()=>[d("span",gn,[c(H,{onClick:a[13]||(a[13]=e=>ne.value=!1)},{default:m((()=>a[20]||(a[20]=[T("关闭")]))),_:1}),ae.value?(u(),s(g,{key:1},[c(H,{onClick:Te},{default:m((()=>a[22]||(a[22]=[T("取消")]))),_:1}),c(H,{type:"primary",onClick:a[14]||(a[14]=e=>(async e=>{if(e)try{if(await e.validate()){if(oe.newPassword){if(!oe.oldPassword)return void t.error("修改密码时需要输入原密码");if(oe.newPassword!==oe.confirmPassword)return void t.error("两次输入的新密码不一致")}const e={name:oe.name,phone:oe.phone,email:oe.email,password:oe.newPassword||void 0,oldPassword:oe.oldPassword||void 0},n=await ee(e);200===n.code?(t.success("个人信息更新成功"),ae.value=!1,await we(),ne.value=!1):t.error(n.message||"更新失败")}}catch(n){n.response?t.error(n.response.data.message||"更新失败"):t.error(n.message||"更新失败")}})(J.value))},{default:m((()=>a[23]||(a[23]=[T(" 保存 ")]))),_:1})],64)):(u(),h(H,{key:0,type:"primary",onClick:Te},{default:m((()=>a[21]||(a[21]=[T(" 编辑信息 ")]))),_:1}))])])),default:m((()=>[c(F,{ref_key:"profileFormRef",ref:J,model:oe,rules:ie,"label-width":"100px",disabled:!ae.value,"status-icon":""},{default:m((()=>[c(W,{label:"姓名",prop:"name"},{default:m((()=>[c(U,{modelValue:oe.name,"onUpdate:modelValue":a[1]||(a[1]=e=>oe.name=e)},null,8,["modelValue"])])),_:1}),c(W,{label:"手机号",prop:ae.value?"phone":""},{default:m((()=>[c(U,{modelValue:oe.phone,"onUpdate:modelValue":a[2]||(a[2]=e=>oe.phone=e),disabled:!ae.value},null,8,["modelValue","disabled"])])),_:1},8,["prop"]),c(W,{label:"邮箱",prop:"email"},{default:m((()=>[c(U,{modelValue:oe.email,"onUpdate:modelValue":a[3]||(a[3]=e=>oe.email=e)},null,8,["modelValue"])])),_:1}),ae.value?w("",!0):(u(),h(W,{key:0,label:"身份证号"},{default:m((()=>[c(U,{modelValue:oe.idCard,"onUpdate:modelValue":a[4]||(a[4]=e=>oe.idCard=e),disabled:""},null,8,["modelValue"])])),_:1})),ae.value?w("",!0):(u(),s(g,{key:1},[c(W,{label:"所属部门"},{default:m((()=>[c(U,{modelValue:oe.departmentName,"onUpdate:modelValue":a[5]||(a[5]=e=>oe.departmentName=e),disabled:""},null,8,["modelValue"])])),_:1}),c(W,{label:"职位"},{default:m((()=>[c(U,{modelValue:oe.positionName,"onUpdate:modelValue":a[6]||(a[6]=e=>oe.positionName=e),disabled:""},null,8,["modelValue"])])),_:1}),c(W,{label:"入职日期"},{default:m((()=>[c(U,{modelValue:oe.entryDate,"onUpdate:modelValue":a[7]||(a[7]=e=>oe.entryDate=e),disabled:""},null,8,["modelValue"])])),_:1}),c(W,{label:"在职状态"},{default:m((()=>[c(U,{modelValue:oe.status,"onUpdate:modelValue":a[8]||(a[8]=e=>oe.status=e),disabled:""},null,8,["modelValue"])])),_:1}),c(W,{label:"物流航线"},{default:m((()=>[c(U,{modelValue:oe.logisticsRoute,"onUpdate:modelValue":a[9]||(a[9]=e=>oe.logisticsRoute=e),disabled:""},null,8,["modelValue"])])),_:1})],64)),ae.value?(u(),s(g,{key:2},[c(z,{"content-position":"center"},{default:m((()=>a[19]||(a[19]=[T("修改密码（选填）")]))),_:1}),c(W,{label:"原密码",prop:"oldPassword"},{default:m((()=>[c(U,{modelValue:oe.oldPassword,"onUpdate:modelValue":a[10]||(a[10]=e=>oe.oldPassword=e),type:"password","show-password":"",placeholder:"修改密码时必填"},null,8,["modelValue"])])),_:1}),c(W,{label:"新密码",prop:"newPassword"},{default:m((()=>[c(U,{modelValue:oe.newPassword,"onUpdate:modelValue":a[11]||(a[11]=e=>oe.newPassword=e),type:"password","show-password":"",placeholder:"至少8位，必须包含大小写字母和数字"},null,8,["modelValue"])])),_:1}),c(W,{label:"确认密码",prop:"confirmPassword"},{default:m((()=>[c(U,{modelValue:oe.confirmPassword,"onUpdate:modelValue":a[12]||(a[12]=e=>oe.confirmPassword=e),type:"password","show-password":"",placeholder:"请再次输入新密码"},null,8,["modelValue"])])),_:1})],64)):w("",!0)])),_:1},8,["model","disabled"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-acfb90a8"]]);export{yn as default};
